"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/templates/route";
exports.ids = ["app/api/templates/route"];
exports.modules = {

/***/ "jsdom":
/*!************************!*\
  !*** external "jsdom" ***!
  \************************/
/***/ ((module) => {

module.exports = require("jsdom");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "(rsc)/../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Ftemplates%2Froute&page=%2Fapi%2Ftemplates%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftemplates%2Froute.ts&appDir=C%3A%5CUsers%5CHIMANSHU%5COneDrive%5CDesktop%5Cportfolio%20saas%20-%20Copy%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHIMANSHU%5COneDrive%5CDesktop%5Cportfolio%20saas%20-%20Copy%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Ftemplates%2Froute&page=%2Fapi%2Ftemplates%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftemplates%2Froute.ts&appDir=C%3A%5CUsers%5CHIMANSHU%5COneDrive%5CDesktop%5Cportfolio%20saas%20-%20Copy%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHIMANSHU%5COneDrive%5CDesktop%5Cportfolio%20saas%20-%20Copy%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/../node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/../node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/../node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_HIMANSHU_OneDrive_Desktop_portfolio_saas_Copy_frontend_src_app_api_templates_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/templates/route.ts */ \"(rsc)/./src/app/api/templates/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/templates/route\",\n        pathname: \"/api/templates\",\n        filename: \"route\",\n        bundlePath: \"app/api/templates/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\api\\\\templates\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_HIMANSHU_OneDrive_Desktop_portfolio_saas_Copy_frontend_src_app_api_templates_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/templates/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Ftemplates%2Froute&page=%2Fapi%2Ftemplates%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftemplates%2Froute.ts&appDir=C%3A%5CUsers%5CHIMANSHU%5COneDrive%5CDesktop%5Cportfolio%20saas%20-%20Copy%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHIMANSHU%5COneDrive%5CDesktop%5Cportfolio%20saas%20-%20Copy%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/templates/route.ts":
/*!****************************************!*\
  !*** ./src/app/api/templates/route.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/../node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _services_template_processor__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/services/template-processor */ \"(rsc)/./src/services/template-processor.ts\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nasync function GET(request) {\n    try {\n        console.log(\"API: Loading templates...\");\n        // Load all templates\n        const templates = await (0,_services_template_processor__WEBPACK_IMPORTED_MODULE_1__.loadAllTemplates)();\n        console.log(\"API: Templates loaded:\", templates.length);\n        // Debug: Check if templates directory exists\n        const templatesDir = path__WEBPACK_IMPORTED_MODULE_2___default().join(process.cwd(), \"templates\", \"html-templates\");\n        console.log(\"API: Templates directory path:\", templatesDir);\n        console.log(\"API: Templates directory exists:\", fs__WEBPACK_IMPORTED_MODULE_3___default().existsSync(templatesDir));\n        if (fs__WEBPACK_IMPORTED_MODULE_3___default().existsSync(templatesDir)) {\n            const dirs = fs__WEBPACK_IMPORTED_MODULE_3___default().readdirSync(templatesDir, {\n                withFileTypes: true\n            }).filter((dirent)=>dirent.isDirectory()).map((dirent)=>dirent.name);\n            console.log(\"API: Template directories found:\", dirs);\n        }\n        // Return the templates as JSON\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            templates\n        });\n    } catch (error) {\n        console.error(\"Error loading templates:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Failed to load templates\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/templates/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/services/template-processor.ts":
/*!********************************************!*\
  !*** ./src/services/template-processor.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createSampleTemplateConfig: () => (/* binding */ createSampleTemplateConfig),\n/* harmony export */   loadAllTemplates: () => (/* binding */ loadAllTemplates),\n/* harmony export */   loadTemplateConfig: () => (/* binding */ loadTemplateConfig),\n/* harmony export */   processTemplate: () => (/* binding */ processTemplate),\n/* harmony export */   saveProcessedTemplate: () => (/* binding */ saveProcessedTemplate)\n/* harmony export */ });\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var jsdom__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! jsdom */ \"jsdom\");\n/* harmony import */ var jsdom__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(jsdom__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\n/**\r\n * Loads template configuration from the template directory\r\n */ async function loadTemplateConfig(templateId) {\n    try {\n        // Try different possible paths for the template config\n        const possiblePaths = [\n            // From project root\n            path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"templates\", \"html-templates\", templateId, \"config.json\"),\n            // From parent directory (when running from frontend folder)\n            path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"..\", \"templates\", \"html-templates\", templateId, \"config.json\"),\n            // From frontend public directory\n            path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"frontend\", \"public\", \"templates\", \"html-templates\", templateId, \"config.json\"),\n            // Alternative frontend path\n            path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"frontend\", \"templates\", \"html-templates\", templateId, \"config.json\")\n        ];\n        for (const configPath of possiblePaths){\n            if (fs__WEBPACK_IMPORTED_MODULE_0___default().existsSync(configPath)) {\n                console.log(`Loading template config from: ${configPath}`);\n                const configData = fs__WEBPACK_IMPORTED_MODULE_0___default().readFileSync(configPath, \"utf-8\");\n                return JSON.parse(configData);\n            }\n        }\n        console.error(`Template config not found for ${templateId} in any of the following paths:`, possiblePaths);\n        return null;\n    } catch (error) {\n        console.error(`Error loading template config for ${templateId}:`, error);\n        return null;\n    }\n}\n/**\r\n * Loads all available templates\r\n */ async function loadAllTemplates() {\n    try {\n        // Try different possible paths for templates directory\n        const possibleTemplatesDirs = [\n            path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"templates\", \"html-templates\"),\n            path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"..\", \"templates\", \"html-templates\"),\n            path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"frontend\", \"templates\", \"html-templates\")\n        ];\n        let templatesDir = null;\n        for (const testDir of possibleTemplatesDirs){\n            console.log(\"Testing templates directory:\", testDir);\n            if (fs__WEBPACK_IMPORTED_MODULE_0___default().existsSync(testDir)) {\n                templatesDir = testDir;\n                console.log(\"Found templates directory at:\", templatesDir);\n                break;\n            }\n        }\n        if (!templatesDir) {\n            console.warn(\"Templates directory not found in any of the expected locations:\", possibleTemplatesDirs);\n            return [];\n        }\n        const templateFolders = fs__WEBPACK_IMPORTED_MODULE_0___default().readdirSync(templatesDir, {\n            withFileTypes: true\n        }).filter((dirent)=>dirent.isDirectory()).map((dirent)=>dirent.name);\n        console.log(\"Found template folders:\", templateFolders);\n        const templates = [];\n        for (const folder of templateFolders){\n            const config = await loadTemplateConfig(folder);\n            if (config) {\n                templates.push(config);\n                console.log(`Loaded template: ${config.name} (${config.id})`);\n            }\n        }\n        return templates;\n    } catch (error) {\n        console.error(\"Error loading templates:\", error);\n        return [];\n    }\n}\n/**\r\n * Processes an HTML template by replacing placeholders with actual content\r\n */ async function processTemplate(templateId, content, colors) {\n    try {\n        console.log(\"=== PROCESS TEMPLATE CALLED ===\");\n        console.log(`Processing template ${templateId} with content:`, content);\n        console.log(\"Colors:\", colors);\n        const config = await loadTemplateConfig(templateId);\n        if (!config) {\n            throw new Error(`Template config not found for ${templateId}`);\n        }\n        console.log(\"Template config loaded successfully\");\n        // Try different possible paths for the template HTML file\n        const possibleTemplatePaths = [\n            path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"templates\", \"html-templates\", templateId, config.mainFile),\n            path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"..\", \"templates\", \"html-templates\", templateId, config.mainFile),\n            path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"frontend\", \"templates\", \"html-templates\", templateId, config.mainFile)\n        ];\n        let templatePath = null;\n        for (const testPath of possibleTemplatePaths){\n            if (fs__WEBPACK_IMPORTED_MODULE_0___default().existsSync(testPath)) {\n                templatePath = testPath;\n                console.log(`Found template file at: ${templatePath}`);\n                break;\n            }\n        }\n        if (!templatePath) {\n            throw new Error(`Template file not found for ${templateId} in any of the following paths: ${possibleTemplatePaths.join(\", \")}`);\n        }\n        // Read the HTML template\n        let htmlContent = fs__WEBPACK_IMPORTED_MODULE_0___default().readFileSync(templatePath, \"utf-8\");\n        console.log(\"Template HTML loaded, length:\", htmlContent.length);\n        // Replace content placeholders\n        console.log(\"About to replace placeholders...\");\n        htmlContent = replaceContentPlaceholders(htmlContent, content, config.placeholders);\n        console.log(\"Placeholders replaced, new length:\", htmlContent.length);\n        // Process the HTML with JSDOM to modify styles and other elements\n        const dom = new jsdom__WEBPACK_IMPORTED_MODULE_2__.JSDOM(htmlContent);\n        const document = dom.window.document;\n        console.log(\"=== STARTING TEMPLATE PROCESSING ===\");\n        console.log(\"About to apply color scheme...\");\n        // Apply color scheme\n        applyColorScheme(document, colors);\n        console.log(\"About to process dynamic sections...\");\n        // Process any dynamic sections (skills, projects, experience)\n        processDynamicSections(document, content, config);\n        console.log(\"Template processing completed, serializing...\");\n        // Return the processed HTML\n        const finalHtml = dom.serialize();\n        console.log(\"Final HTML length:\", finalHtml.length);\n        console.log(\"=== PROCESS TEMPLATE COMPLETED ===\");\n        return finalHtml;\n    } catch (error) {\n        console.error(`Error processing template ${templateId}:`, error);\n        return null;\n    }\n}\n/**\r\n * Replaces content placeholders in the HTML\r\n */ function replaceContentPlaceholders(html, content, placeholders) {\n    console.log(\"=== REPLACE CONTENT PLACEHOLDERS ===\");\n    console.log(\"Content received:\", content);\n    console.log(\"Placeholders config:\", placeholders);\n    let processedHtml = html;\n    // Direct replacement for common placeholders\n    console.log(\"Replacing direct placeholders...\");\n    processedHtml = processedHtml.replace(/\\{\\{name\\}\\}/g, content.name || \"Your Name\");\n    processedHtml = processedHtml.replace(/\\{\\{title\\}\\}/g, content.title || \"Your Title\");\n    processedHtml = processedHtml.replace(/\\{\\{bio\\}\\}/g, content.bio || \"Your bio goes here...\");\n    processedHtml = processedHtml.replace(/\\{\\{email\\}\\}/g, content.email || \"<EMAIL>\");\n    processedHtml = processedHtml.replace(/\\{\\{phone\\}\\}/g, content.phone || \"+****************\");\n    // Handle social media links\n    if (content.social) {\n        processedHtml = processedHtml.replace(/\\{\\{linkedin\\}\\}/g, content.social.linkedin || \"#\");\n        processedHtml = processedHtml.replace(/\\{\\{github\\}\\}/g, content.social.github || \"#\");\n        processedHtml = processedHtml.replace(/\\{\\{twitter\\}\\}/g, content.social.twitter || \"#\");\n    } else {\n        processedHtml = processedHtml.replace(/\\{\\{linkedin\\}\\}/g, \"#\");\n        processedHtml = processedHtml.replace(/\\{\\{github\\}\\}/g, \"#\");\n        processedHtml = processedHtml.replace(/\\{\\{twitter\\}\\}/g, \"#\");\n    }\n    // Process each content type from config\n    for (const [key, placeholderList] of Object.entries(placeholders)){\n        if (content[key] && typeof content[key] === \"string\") {\n            console.log(`Processing ${key}: ${content[key]}`);\n            // Replace simple string placeholders\n            for (const placeholder of placeholderList){\n                const regex = new RegExp(placeholder.replace(/[.*+?^${}()|[\\]\\\\]/g, \"\\\\$&\"), \"g\");\n                processedHtml = processedHtml.replace(regex, content[key]);\n                console.log(`Replaced ${placeholder} with ${content[key]}`);\n            }\n        }\n    }\n    console.log(\"Placeholder replacement completed\");\n    return processedHtml;\n}\n/**\r\n * Applies the color scheme to the HTML document\r\n */ function applyColorScheme(document, colors) {\n    // Find all style tags\n    const styleTags = document.querySelectorAll(\"style\");\n    styleTags.forEach((styleTag)=>{\n        let cssContent = styleTag.textContent || \"\";\n        // Replace color variables or specific color values\n        cssContent = cssContent.replace(/--primary-color:\\s*[^;]+;/g, `--primary-color: ${colors.primary};`).replace(/--secondary-color:\\s*[^;]+;/g, `--secondary-color: ${colors.secondary};`).replace(/--accent-color:\\s*[^;]+;/g, `--accent-color: ${colors.accent};`);\n        styleTag.textContent = cssContent;\n    });\n    // Also look for inline styles with color properties\n    const elementsWithStyle = document.querySelectorAll('[style*=\"color\"]');\n    elementsWithStyle.forEach((element)=>{\n        const style = element.getAttribute(\"style\") || \"\";\n        // Replace color values in inline styles\n        const updatedStyle = style.replace(/color:\\s*var\\(--primary-color\\)/g, `color: ${colors.primary}`).replace(/color:\\s*var\\(--secondary-color\\)/g, `color: ${colors.secondary}`).replace(/color:\\s*var\\(--accent-color\\)/g, `color: ${colors.accent}`).replace(/background-color:\\s*var\\(--primary-color\\)/g, `background-color: ${colors.primary}`).replace(/background-color:\\s*var\\(--secondary-color\\)/g, `background-color: ${colors.secondary}`).replace(/background-color:\\s*var\\(--accent-color\\)/g, `background-color: ${colors.accent}`);\n        element.setAttribute(\"style\", updatedStyle);\n    });\n}\n/**\r\n * Processes dynamic sections like skills, projects, and experience\r\n */ function processDynamicSections(document, content, config) {\n    console.log(\"=== PROCESSING DYNAMIC SECTIONS ===\");\n    console.log(\"Content received:\", content);\n    console.log(\"Skills:\", content.skills);\n    console.log(\"Projects:\", content.projects);\n    console.log(\"Experience:\", content.experience);\n    // Process skills section\n    processSkillsSection(document, content.skills);\n    // Process projects section\n    processProjectsSection(document, content.projects);\n    // Process experience section\n    processExperienceSection(document, content.experience);\n}\n/**\r\n * Processes the skills section\r\n */ function processSkillsSection(document, skills) {\n    console.log(\"=== PROCESSING SKILLS ===\");\n    console.log(\"Skills to process:\", skills);\n    const skillsContainer = document.querySelector('.skills-container, #skills-container, [data-section=\"skills\"]');\n    console.log(\"Skills container found:\", !!skillsContainer);\n    if (skillsContainer && skills && skills.length > 0) {\n        console.log(\"Processing\", skills.length, \"skills\");\n        // Clear existing skills\n        skillsContainer.innerHTML = \"\";\n        // Create skill elements for each skill\n        skills.forEach((skill)=>{\n            const skillElement = document.createElement(\"div\");\n            skillElement.className = \"skill\";\n            skillElement.textContent = skill;\n            skillsContainer.appendChild(skillElement);\n            console.log(\"Added skill:\", skill);\n        });\n    } else {\n        console.log(\"No skills container found or no skills to process\");\n        console.log(\"Skills array:\", skills);\n        console.log(\"Skills container:\", skillsContainer);\n    }\n}\n/**\r\n * Processes the projects section\r\n */ function processProjectsSection(document, projects) {\n    console.log(\"=== PROCESSING PROJECTS ===\");\n    console.log(\"Projects to process:\", projects);\n    const projectsContainer = document.querySelector('.projects-container, #projects-container, [data-section=\"projects\"]');\n    console.log(\"Projects container found:\", !!projectsContainer);\n    if (projectsContainer && projects && projects.length > 0) {\n        console.log(\"Processing\", projects.length, \"projects\");\n        // Clear existing projects\n        projectsContainer.innerHTML = \"\";\n        // Create project elements for each project\n        projects.forEach((project)=>{\n            const projectElement = document.createElement(\"div\");\n            projectElement.className = \"project\";\n            // Create project content\n            projectElement.innerHTML = `\r\n        <img src=\"${project.image || \"/placeholder-project.jpg\"}\" alt=\"${project.title}\">\r\n        <div class=\"project-content\">\r\n          <h3 class=\"project-title\">${project.title}</h3>\r\n          <p class=\"project-description\">${project.description}</p>\r\n          ${project.link ? `<a href=\"${project.link}\" class=\"project-link\" target=\"_blank\">View Project</a>` : \"\"}\r\n        </div>\r\n      `;\n            projectsContainer.appendChild(projectElement);\n            console.log(\"Added project:\", project.title);\n        });\n    } else {\n        console.log(\"No projects container found or no projects to process\");\n    }\n}\n/**\r\n * Processes the experience section\r\n */ function processExperienceSection(document, experiences) {\n    console.log(\"=== PROCESSING EXPERIENCE ===\");\n    console.log(\"Experience to process:\", experiences);\n    const experienceContainer = document.querySelector('.experience-container, #experience-container, [data-section=\"experience\"]');\n    console.log(\"Experience container found:\", !!experienceContainer);\n    if (experienceContainer && experiences.length > 0) {\n        console.log(\"Processing\", experiences.length, \"experience items\");\n        // Clear existing experiences\n        experienceContainer.innerHTML = \"\";\n        // Create experience elements for each experience\n        experiences.forEach((exp)=>{\n            const expElement = document.createElement(\"div\");\n            expElement.className = \"experience-item\";\n            const dateText = exp.endDate ? `${exp.startDate} - ${exp.endDate}` : `${exp.startDate} - Present`;\n            // Create experience content\n            expElement.innerHTML = `\r\n        <h3 class=\"job-title\">${exp.title}</h3>\r\n        <p class=\"company-name\">${exp.company}</p>\r\n        <p class=\"job-date\">${dateText}</p>\r\n        <p class=\"job-description\">${exp.description}</p>\r\n      `;\n            experienceContainer.appendChild(expElement);\n            console.log(\"Added experience:\", exp.title, \"at\", exp.company);\n        });\n    } else {\n        console.log(\"No experience container found or no experience to process\");\n    }\n}\n/**\r\n * Saves the processed HTML to the output directory\r\n */ async function saveProcessedTemplate(portfolioId, html, templateId) {\n    try {\n        // Try different possible output directories\n        const possibleOutputDirs = [\n            path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"data\", \"portfolios\", portfolioId),\n            path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"frontend\", \"data\", \"portfolios\", portfolioId),\n            path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"..\", \"data\", \"portfolios\", portfolioId)\n        ];\n        // Use the first directory that we can create or that already exists\n        let outputDir = possibleOutputDirs[0]; // Default to first option\n        // Try to create the directory\n        if (!fs__WEBPACK_IMPORTED_MODULE_0___default().existsSync(outputDir)) {\n            fs__WEBPACK_IMPORTED_MODULE_0___default().mkdirSync(outputDir, {\n                recursive: true\n            });\n        }\n        console.log(`Saving portfolio to: ${outputDir}`);\n        // Save the HTML file\n        const htmlPath = path__WEBPACK_IMPORTED_MODULE_1___default().join(outputDir, \"index.html\");\n        fs__WEBPACK_IMPORTED_MODULE_0___default().writeFileSync(htmlPath, html);\n        console.log(`HTML file saved to: ${htmlPath}`);\n        // Find the template directory and copy assets\n        const possibleTemplateDirs = [\n            path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"templates\", \"html-templates\", templateId),\n            path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"..\", \"templates\", \"html-templates\", templateId),\n            path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"frontend\", \"templates\", \"html-templates\", templateId)\n        ];\n        let templateDir = null;\n        for (const testDir of possibleTemplateDirs){\n            if (fs__WEBPACK_IMPORTED_MODULE_0___default().existsSync(testDir)) {\n                templateDir = testDir;\n                console.log(`Found template directory at: ${templateDir}`);\n                break;\n            }\n        }\n        if (templateDir) {\n            copyTemplateAssets(templateDir, outputDir);\n        } else {\n            console.warn(`Template directory not found for ${templateId}`);\n        }\n        return outputDir;\n    } catch (error) {\n        console.error(`Error saving processed template for portfolio ${portfolioId}:`, error);\n        throw new Error(\"Failed to save processed template\");\n    }\n}\n/**\r\n * Copies all assets from the template directory to the output directory\r\n */ function copyTemplateAssets(sourceDir, targetDir) {\n    // Read all files and directories in the source directory\n    const items = fs__WEBPACK_IMPORTED_MODULE_0___default().readdirSync(sourceDir, {\n        withFileTypes: true\n    });\n    for (const item of items){\n        const sourcePath = path__WEBPACK_IMPORTED_MODULE_1___default().join(sourceDir, item.name);\n        const targetPath = path__WEBPACK_IMPORTED_MODULE_1___default().join(targetDir, item.name);\n        if (item.isDirectory()) {\n            // Create the directory in the target\n            if (!fs__WEBPACK_IMPORTED_MODULE_0___default().existsSync(targetPath)) {\n                fs__WEBPACK_IMPORTED_MODULE_0___default().mkdirSync(targetPath, {\n                    recursive: true\n                });\n            }\n            // Recursively copy contents\n            copyTemplateAssets(sourcePath, targetPath);\n        } else if (item.isFile() && !item.name.endsWith(\".html\") && item.name !== \"config.json\") {\n            // Copy the file (excluding HTML and config files)\n            fs__WEBPACK_IMPORTED_MODULE_0___default().copyFileSync(sourcePath, targetPath);\n        }\n    }\n}\n/**\r\n * Creates a sample template config file\r\n */ function createSampleTemplateConfig(templateId, templateName, description) {\n    return {\n        id: templateId,\n        name: templateName,\n        description: description,\n        folderPath: templateId,\n        mainFile: \"index.html\",\n        previewImage: `/${templateId}/preview.jpg`,\n        placeholders: {\n            name: [\n                \"{{NAME}}\",\n                \"{{name}}\",\n                \"{{User Name}}\"\n            ],\n            title: [\n                \"{{TITLE}}\",\n                \"{{title}}\",\n                \"{{Job Title}}\"\n            ],\n            bio: [\n                \"{{BIO}}\",\n                \"{{bio}}\",\n                \"{{About Me}}\",\n                \"{{about}}\"\n            ],\n            skills: [\n                \"{{SKILLS}}\",\n                \"{{skills}}\"\n            ],\n            projects: [\n                \"{{PROJECTS}}\",\n                \"{{projects}}\"\n            ],\n            experience: [\n                \"{{EXPERIENCE}}\",\n                \"{{experience}}\",\n                \"{{work}}\"\n            ],\n            contact: [\n                \"{{CONTACT}}\",\n                \"{{contact}}\",\n                \"{{email}}\",\n                \"{{phone}}\"\n            ],\n            social: [\n                \"{{SOCIAL}}\",\n                \"{{social}}\",\n                \"{{linkedin}}\",\n                \"{{github}}\",\n                \"{{twitter}}\"\n            ]\n        }\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/services/template-processor.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Ftemplates%2Froute&page=%2Fapi%2Ftemplates%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftemplates%2Froute.ts&appDir=C%3A%5CUsers%5CHIMANSHU%5COneDrive%5CDesktop%5Cportfolio%20saas%20-%20Copy%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHIMANSHU%5COneDrive%5CDesktop%5Cportfolio%20saas%20-%20Copy%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();