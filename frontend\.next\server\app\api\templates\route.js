"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/templates/route";
exports.ids = ["app/api/templates/route"];
exports.modules = {

/***/ "jsdom":
/*!************************!*\
  !*** external "jsdom" ***!
  \************************/
/***/ ((module) => {

module.exports = require("jsdom");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "(rsc)/../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Ftemplates%2Froute&page=%2Fapi%2Ftemplates%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftemplates%2Froute.ts&appDir=C%3A%5CUsers%5CHIMANSHU%5COneDrive%5CDesktop%5Cportfolio%20saas%20-%20Copy%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHIMANSHU%5COneDrive%5CDesktop%5Cportfolio%20saas%20-%20Copy%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Ftemplates%2Froute&page=%2Fapi%2Ftemplates%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftemplates%2Froute.ts&appDir=C%3A%5CUsers%5CHIMANSHU%5COneDrive%5CDesktop%5Cportfolio%20saas%20-%20Copy%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHIMANSHU%5COneDrive%5CDesktop%5Cportfolio%20saas%20-%20Copy%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/../node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/../node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/../node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_HIMANSHU_OneDrive_Desktop_portfolio_saas_Copy_frontend_src_app_api_templates_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/templates/route.ts */ \"(rsc)/./src/app/api/templates/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/templates/route\",\n        pathname: \"/api/templates\",\n        filename: \"route\",\n        bundlePath: \"app/api/templates/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\api\\\\templates\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_HIMANSHU_OneDrive_Desktop_portfolio_saas_Copy_frontend_src_app_api_templates_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/templates/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Ftemplates%2Froute&page=%2Fapi%2Ftemplates%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftemplates%2Froute.ts&appDir=C%3A%5CUsers%5CHIMANSHU%5COneDrive%5CDesktop%5Cportfolio%20saas%20-%20Copy%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHIMANSHU%5COneDrive%5CDesktop%5Cportfolio%20saas%20-%20Copy%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/templates/route.ts":
/*!****************************************!*\
  !*** ./src/app/api/templates/route.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/../node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _services_template_processor__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/services/template-processor */ \"(rsc)/./src/services/template-processor.ts\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nasync function GET(request) {\n    try {\n        console.log(\"API: Loading templates...\");\n        // Load all templates\n        const templates = await (0,_services_template_processor__WEBPACK_IMPORTED_MODULE_1__.loadAllTemplates)();\n        console.log(\"API: Templates loaded:\", templates.length);\n        // Debug: Check if templates directory exists\n        const templatesDir = path__WEBPACK_IMPORTED_MODULE_2___default().join(process.cwd(), \"templates\", \"html-templates\");\n        console.log(\"API: Templates directory path:\", templatesDir);\n        console.log(\"API: Templates directory exists:\", fs__WEBPACK_IMPORTED_MODULE_3___default().existsSync(templatesDir));\n        if (fs__WEBPACK_IMPORTED_MODULE_3___default().existsSync(templatesDir)) {\n            const dirs = fs__WEBPACK_IMPORTED_MODULE_3___default().readdirSync(templatesDir, {\n                withFileTypes: true\n            }).filter((dirent)=>dirent.isDirectory()).map((dirent)=>dirent.name);\n            console.log(\"API: Template directories found:\", dirs);\n        }\n        // Return the templates as JSON\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            templates\n        });\n    } catch (error) {\n        console.error(\"Error loading templates:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Failed to load templates\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/templates/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/services/template-processor.ts":
/*!********************************************!*\
  !*** ./src/services/template-processor.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createSampleTemplateConfig: () => (/* binding */ createSampleTemplateConfig),\n/* harmony export */   loadAllTemplates: () => (/* binding */ loadAllTemplates),\n/* harmony export */   loadTemplateConfig: () => (/* binding */ loadTemplateConfig),\n/* harmony export */   processTemplate: () => (/* binding */ processTemplate),\n/* harmony export */   saveProcessedTemplate: () => (/* binding */ saveProcessedTemplate)\n/* harmony export */ });\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var jsdom__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! jsdom */ \"jsdom\");\n/* harmony import */ var jsdom__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(jsdom__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\n/**\r\n * Loads template configuration from the template directory\r\n */ async function loadTemplateConfig(templateId) {\n    try {\n        // Try different possible paths for the template config\n        const possiblePaths = [\n            // From project root\n            path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"templates\", \"html-templates\", templateId, \"config.json\"),\n            // From parent directory (when running from frontend folder)\n            path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"..\", \"templates\", \"html-templates\", templateId, \"config.json\"),\n            // From frontend public directory\n            path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"frontend\", \"public\", \"templates\", \"html-templates\", templateId, \"config.json\"),\n            // Alternative frontend path\n            path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"frontend\", \"templates\", \"html-templates\", templateId, \"config.json\")\n        ];\n        for (const configPath of possiblePaths){\n            if (fs__WEBPACK_IMPORTED_MODULE_0___default().existsSync(configPath)) {\n                console.log(`Loading template config from: ${configPath}`);\n                const configData = fs__WEBPACK_IMPORTED_MODULE_0___default().readFileSync(configPath, \"utf-8\");\n                return JSON.parse(configData);\n            }\n        }\n        console.error(`Template config not found for ${templateId} in any of the following paths:`, possiblePaths);\n        return null;\n    } catch (error) {\n        console.error(`Error loading template config for ${templateId}:`, error);\n        return null;\n    }\n}\n/**\r\n * Loads all available templates\r\n */ async function loadAllTemplates() {\n    try {\n        // Try different possible paths for templates directory\n        const possibleTemplatesDirs = [\n            path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"templates\", \"html-templates\"),\n            path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"..\", \"templates\", \"html-templates\"),\n            path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"frontend\", \"templates\", \"html-templates\")\n        ];\n        let templatesDir = null;\n        for (const testDir of possibleTemplatesDirs){\n            console.log(\"Testing templates directory:\", testDir);\n            if (fs__WEBPACK_IMPORTED_MODULE_0___default().existsSync(testDir)) {\n                templatesDir = testDir;\n                console.log(\"Found templates directory at:\", templatesDir);\n                break;\n            }\n        }\n        if (!templatesDir) {\n            console.warn(\"Templates directory not found in any of the expected locations:\", possibleTemplatesDirs);\n            return [];\n        }\n        const templateFolders = fs__WEBPACK_IMPORTED_MODULE_0___default().readdirSync(templatesDir, {\n            withFileTypes: true\n        }).filter((dirent)=>dirent.isDirectory()).map((dirent)=>dirent.name);\n        console.log(\"Found template folders:\", templateFolders);\n        const templates = [];\n        for (const folder of templateFolders){\n            const config = await loadTemplateConfig(folder);\n            if (config) {\n                templates.push(config);\n                console.log(`Loaded template: ${config.name} (${config.id})`);\n            }\n        }\n        return templates;\n    } catch (error) {\n        console.error(\"Error loading templates:\", error);\n        return [];\n    }\n}\n/**\r\n * Processes an HTML template by replacing placeholders with actual content\r\n */ async function processTemplate(templateId, content, colors) {\n    try {\n        console.log(\"=== PROCESS TEMPLATE CALLED ===\");\n        console.log(`Processing template ${templateId} with content:`, content);\n        console.log(\"Colors:\", colors);\n        const config = await loadTemplateConfig(templateId);\n        if (!config) {\n            throw new Error(`Template config not found for ${templateId}`);\n        }\n        console.log(\"Template config loaded successfully\");\n        // Try different possible paths for the template HTML file\n        const possibleTemplatePaths = [\n            path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"templates\", \"html-templates\", templateId, config.mainFile),\n            path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"..\", \"templates\", \"html-templates\", templateId, config.mainFile),\n            path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"frontend\", \"templates\", \"html-templates\", templateId, config.mainFile)\n        ];\n        let templatePath = null;\n        for (const testPath of possibleTemplatePaths){\n            if (fs__WEBPACK_IMPORTED_MODULE_0___default().existsSync(testPath)) {\n                templatePath = testPath;\n                console.log(`Found template file at: ${templatePath}`);\n                break;\n            }\n        }\n        if (!templatePath) {\n            throw new Error(`Template file not found for ${templateId} in any of the following paths: ${possibleTemplatePaths.join(\", \")}`);\n        }\n        // Read the HTML template\n        let htmlContent = fs__WEBPACK_IMPORTED_MODULE_0___default().readFileSync(templatePath, \"utf-8\");\n        console.log(\"Template HTML loaded, length:\", htmlContent.length);\n        // Replace content placeholders\n        console.log(\"About to replace placeholders...\");\n        htmlContent = replaceContentPlaceholders(htmlContent, content, config.placeholders);\n        console.log(\"Placeholders replaced, new length:\", htmlContent.length);\n        // Process the HTML with JSDOM to modify styles and other elements\n        const dom = new jsdom__WEBPACK_IMPORTED_MODULE_2__.JSDOM(htmlContent);\n        const document = dom.window.document;\n        console.log(\"=== STARTING TEMPLATE PROCESSING ===\");\n        console.log(\"About to apply color scheme...\");\n        // Apply color scheme\n        applyColorScheme(document, colors);\n        console.log(\"About to process dynamic sections...\");\n        // Process any dynamic sections (skills, projects, experience)\n        processDynamicSections(document, content, config);\n        console.log(\"Template processing completed, serializing...\");\n        // Return the processed HTML\n        const finalHtml = dom.serialize();\n        console.log(\"Final HTML length:\", finalHtml.length);\n        console.log(\"=== PROCESS TEMPLATE COMPLETED ===\");\n        return finalHtml;\n    } catch (error) {\n        console.error(`Error processing template ${templateId}:`, error);\n        return null;\n    }\n}\n/**\r\n * Replaces content placeholders in the HTML\r\n */ function replaceContentPlaceholders(html, content, placeholders) {\n    let processedHtml = html;\n    // Process each content type\n    for (const [key, placeholderList] of Object.entries(placeholders)){\n        if (content[key] && typeof content[key] === \"string\") {\n            // Replace simple string placeholders\n            for (const placeholder of placeholderList){\n                processedHtml = processedHtml.replace(new RegExp(placeholder, \"g\"), content[key]);\n            }\n        }\n    }\n    // Handle social media links\n    if (content.social) {\n        processedHtml = processedHtml.replace(/\\{\\{linkedin\\}\\}/g, content.social.linkedin || \"#\");\n        processedHtml = processedHtml.replace(/\\{\\{github\\}\\}/g, content.social.github || \"#\");\n        processedHtml = processedHtml.replace(/\\{\\{twitter\\}\\}/g, content.social.twitter || \"#\");\n    }\n    // Handle email and phone if they exist\n    processedHtml = processedHtml.replace(/\\{\\{email\\}\\}/g, content.email || \"<EMAIL>\");\n    processedHtml = processedHtml.replace(/\\{\\{phone\\}\\}/g, content.phone || \"+****************\");\n    return processedHtml;\n}\n/**\r\n * Applies the color scheme to the HTML document\r\n */ function applyColorScheme(document, colors) {\n    // Find all style tags\n    const styleTags = document.querySelectorAll(\"style\");\n    styleTags.forEach((styleTag)=>{\n        let cssContent = styleTag.textContent || \"\";\n        // Replace color variables or specific color values\n        cssContent = cssContent.replace(/--primary-color:\\s*[^;]+;/g, `--primary-color: ${colors.primary};`).replace(/--secondary-color:\\s*[^;]+;/g, `--secondary-color: ${colors.secondary};`).replace(/--accent-color:\\s*[^;]+;/g, `--accent-color: ${colors.accent};`);\n        styleTag.textContent = cssContent;\n    });\n    // Also look for inline styles with color properties\n    const elementsWithStyle = document.querySelectorAll('[style*=\"color\"]');\n    elementsWithStyle.forEach((element)=>{\n        const style = element.getAttribute(\"style\") || \"\";\n        // Replace color values in inline styles\n        const updatedStyle = style.replace(/color:\\s*var\\(--primary-color\\)/g, `color: ${colors.primary}`).replace(/color:\\s*var\\(--secondary-color\\)/g, `color: ${colors.secondary}`).replace(/color:\\s*var\\(--accent-color\\)/g, `color: ${colors.accent}`).replace(/background-color:\\s*var\\(--primary-color\\)/g, `background-color: ${colors.primary}`).replace(/background-color:\\s*var\\(--secondary-color\\)/g, `background-color: ${colors.secondary}`).replace(/background-color:\\s*var\\(--accent-color\\)/g, `background-color: ${colors.accent}`);\n        element.setAttribute(\"style\", updatedStyle);\n    });\n}\n/**\r\n * Processes dynamic sections like skills, projects, and experience\r\n */ function processDynamicSections(document, content, config) {\n    console.log(\"=== PROCESSING DYNAMIC SECTIONS ===\");\n    console.log(\"Content received:\", content);\n    console.log(\"Skills:\", content.skills);\n    console.log(\"Projects:\", content.projects);\n    console.log(\"Experience:\", content.experience);\n    // Process skills section\n    processSkillsSection(document, content.skills);\n    // Process projects section\n    processProjectsSection(document, content.projects);\n    // Process experience section\n    processExperienceSection(document, content.experience);\n}\n/**\r\n * Processes the skills section\r\n */ function processSkillsSection(document, skills) {\n    console.log(\"=== PROCESSING SKILLS ===\");\n    console.log(\"Skills to process:\", skills);\n    const skillsContainer = document.querySelector('.skills-container, #skills-container, [data-section=\"skills\"]');\n    console.log(\"Skills container found:\", !!skillsContainer);\n    if (skillsContainer && skills.length > 0) {\n        console.log(\"Processing\", skills.length, \"skills\");\n        // Clear existing skills\n        skillsContainer.innerHTML = \"\";\n        // Create skill elements for each skill\n        skills.forEach((skill)=>{\n            const skillElement = document.createElement(\"div\");\n            skillElement.className = \"skill\";\n            skillElement.textContent = skill;\n            skillsContainer.appendChild(skillElement);\n            console.log(\"Added skill:\", skill);\n        });\n    } else {\n        console.log(\"No skills container found or no skills to process\");\n    }\n}\n/**\r\n * Processes the projects section\r\n */ function processProjectsSection(document, projects) {\n    console.log(\"=== PROCESSING PROJECTS ===\");\n    console.log(\"Projects to process:\", projects);\n    const projectsContainer = document.querySelector('.projects-container, #projects-container, [data-section=\"projects\"]');\n    console.log(\"Projects container found:\", !!projectsContainer);\n    if (projectsContainer && projects.length > 0) {\n        console.log(\"Processing\", projects.length, \"projects\");\n        // Clear existing projects\n        projectsContainer.innerHTML = \"\";\n        // Create project elements for each project\n        projects.forEach((project)=>{\n            const projectElement = document.createElement(\"div\");\n            projectElement.className = \"project\";\n            // Create project content\n            projectElement.innerHTML = `\r\n        <img src=\"${project.image || \"/placeholder-project.jpg\"}\" alt=\"${project.title}\">\r\n        <div class=\"project-content\">\r\n          <h3 class=\"project-title\">${project.title}</h3>\r\n          <p class=\"project-description\">${project.description}</p>\r\n          ${project.link ? `<a href=\"${project.link}\" class=\"project-link\" target=\"_blank\">View Project</a>` : \"\"}\r\n        </div>\r\n      `;\n            projectsContainer.appendChild(projectElement);\n            console.log(\"Added project:\", project.title);\n        });\n    } else {\n        console.log(\"No projects container found or no projects to process\");\n    }\n}\n/**\r\n * Processes the experience section\r\n */ function processExperienceSection(document, experiences) {\n    console.log(\"=== PROCESSING EXPERIENCE ===\");\n    console.log(\"Experience to process:\", experiences);\n    const experienceContainer = document.querySelector('.experience-container, #experience-container, [data-section=\"experience\"]');\n    console.log(\"Experience container found:\", !!experienceContainer);\n    if (experienceContainer && experiences.length > 0) {\n        console.log(\"Processing\", experiences.length, \"experience items\");\n        // Clear existing experiences\n        experienceContainer.innerHTML = \"\";\n        // Create experience elements for each experience\n        experiences.forEach((exp)=>{\n            const expElement = document.createElement(\"div\");\n            expElement.className = \"experience-item\";\n            const dateText = exp.endDate ? `${exp.startDate} - ${exp.endDate}` : `${exp.startDate} - Present`;\n            // Create experience content\n            expElement.innerHTML = `\r\n        <h3 class=\"job-title\">${exp.title}</h3>\r\n        <p class=\"company-name\">${exp.company}</p>\r\n        <p class=\"job-date\">${dateText}</p>\r\n        <p class=\"job-description\">${exp.description}</p>\r\n      `;\n            experienceContainer.appendChild(expElement);\n            console.log(\"Added experience:\", exp.title, \"at\", exp.company);\n        });\n    } else {\n        console.log(\"No experience container found or no experience to process\");\n    }\n}\n/**\r\n * Saves the processed HTML to the output directory\r\n */ async function saveProcessedTemplate(portfolioId, html, templateId) {\n    try {\n        // Try different possible output directories\n        const possibleOutputDirs = [\n            path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"data\", \"portfolios\", portfolioId),\n            path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"frontend\", \"data\", \"portfolios\", portfolioId),\n            path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"..\", \"data\", \"portfolios\", portfolioId)\n        ];\n        // Use the first directory that we can create or that already exists\n        let outputDir = possibleOutputDirs[0]; // Default to first option\n        // Try to create the directory\n        if (!fs__WEBPACK_IMPORTED_MODULE_0___default().existsSync(outputDir)) {\n            fs__WEBPACK_IMPORTED_MODULE_0___default().mkdirSync(outputDir, {\n                recursive: true\n            });\n        }\n        console.log(`Saving portfolio to: ${outputDir}`);\n        // Save the HTML file\n        const htmlPath = path__WEBPACK_IMPORTED_MODULE_1___default().join(outputDir, \"index.html\");\n        fs__WEBPACK_IMPORTED_MODULE_0___default().writeFileSync(htmlPath, html);\n        console.log(`HTML file saved to: ${htmlPath}`);\n        // Find the template directory and copy assets\n        const possibleTemplateDirs = [\n            path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"templates\", \"html-templates\", templateId),\n            path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"..\", \"templates\", \"html-templates\", templateId),\n            path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"frontend\", \"templates\", \"html-templates\", templateId)\n        ];\n        let templateDir = null;\n        for (const testDir of possibleTemplateDirs){\n            if (fs__WEBPACK_IMPORTED_MODULE_0___default().existsSync(testDir)) {\n                templateDir = testDir;\n                console.log(`Found template directory at: ${templateDir}`);\n                break;\n            }\n        }\n        if (templateDir) {\n            copyTemplateAssets(templateDir, outputDir);\n        } else {\n            console.warn(`Template directory not found for ${templateId}`);\n        }\n        return outputDir;\n    } catch (error) {\n        console.error(`Error saving processed template for portfolio ${portfolioId}:`, error);\n        throw new Error(\"Failed to save processed template\");\n    }\n}\n/**\r\n * Copies all assets from the template directory to the output directory\r\n */ function copyTemplateAssets(sourceDir, targetDir) {\n    // Read all files and directories in the source directory\n    const items = fs__WEBPACK_IMPORTED_MODULE_0___default().readdirSync(sourceDir, {\n        withFileTypes: true\n    });\n    for (const item of items){\n        const sourcePath = path__WEBPACK_IMPORTED_MODULE_1___default().join(sourceDir, item.name);\n        const targetPath = path__WEBPACK_IMPORTED_MODULE_1___default().join(targetDir, item.name);\n        if (item.isDirectory()) {\n            // Create the directory in the target\n            if (!fs__WEBPACK_IMPORTED_MODULE_0___default().existsSync(targetPath)) {\n                fs__WEBPACK_IMPORTED_MODULE_0___default().mkdirSync(targetPath, {\n                    recursive: true\n                });\n            }\n            // Recursively copy contents\n            copyTemplateAssets(sourcePath, targetPath);\n        } else if (item.isFile() && !item.name.endsWith(\".html\") && item.name !== \"config.json\") {\n            // Copy the file (excluding HTML and config files)\n            fs__WEBPACK_IMPORTED_MODULE_0___default().copyFileSync(sourcePath, targetPath);\n        }\n    }\n}\n/**\r\n * Creates a sample template config file\r\n */ function createSampleTemplateConfig(templateId, templateName, description) {\n    return {\n        id: templateId,\n        name: templateName,\n        description: description,\n        folderPath: templateId,\n        mainFile: \"index.html\",\n        previewImage: `/${templateId}/preview.jpg`,\n        placeholders: {\n            name: [\n                \"{{NAME}}\",\n                \"{{name}}\",\n                \"{{User Name}}\"\n            ],\n            title: [\n                \"{{TITLE}}\",\n                \"{{title}}\",\n                \"{{Job Title}}\"\n            ],\n            bio: [\n                \"{{BIO}}\",\n                \"{{bio}}\",\n                \"{{About Me}}\",\n                \"{{about}}\"\n            ],\n            skills: [\n                \"{{SKILLS}}\",\n                \"{{skills}}\"\n            ],\n            projects: [\n                \"{{PROJECTS}}\",\n                \"{{projects}}\"\n            ],\n            experience: [\n                \"{{EXPERIENCE}}\",\n                \"{{experience}}\",\n                \"{{work}}\"\n            ],\n            contact: [\n                \"{{CONTACT}}\",\n                \"{{contact}}\",\n                \"{{email}}\",\n                \"{{phone}}\"\n            ],\n            social: [\n                \"{{SOCIAL}}\",\n                \"{{social}}\",\n                \"{{linkedin}}\",\n                \"{{github}}\",\n                \"{{twitter}}\"\n            ]\n        }\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/services/template-processor.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Ftemplates%2Froute&page=%2Fapi%2Ftemplates%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftemplates%2Froute.ts&appDir=C%3A%5CUsers%5CHIMANSHU%5COneDrive%5CDesktop%5Cportfolio%20saas%20-%20Copy%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHIMANSHU%5COneDrive%5CDesktop%5Cportfolio%20saas%20-%20Copy%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();