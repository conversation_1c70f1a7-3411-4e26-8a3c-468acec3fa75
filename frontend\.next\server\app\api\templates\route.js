"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/templates/route";
exports.ids = ["app/api/templates/route"];
exports.modules = {

/***/ "jsdom":
/*!************************!*\
  !*** external "jsdom" ***!
  \************************/
/***/ ((module) => {

module.exports = require("jsdom");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "(rsc)/../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Ftemplates%2Froute&page=%2Fapi%2Ftemplates%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftemplates%2Froute.ts&appDir=C%3A%5CUsers%5CHIMANSHU%5COneDrive%5CDesktop%5Cportfolio%20saas%20-%20Copy%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHIMANSHU%5COneDrive%5CDesktop%5Cportfolio%20saas%20-%20Copy%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Ftemplates%2Froute&page=%2Fapi%2Ftemplates%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftemplates%2Froute.ts&appDir=C%3A%5CUsers%5CHIMANSHU%5COneDrive%5CDesktop%5Cportfolio%20saas%20-%20Copy%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHIMANSHU%5COneDrive%5CDesktop%5Cportfolio%20saas%20-%20Copy%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/../node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/../node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/../node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_HIMANSHU_OneDrive_Desktop_portfolio_saas_Copy_frontend_src_app_api_templates_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/templates/route.ts */ \"(rsc)/./src/app/api/templates/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/templates/route\",\n        pathname: \"/api/templates\",\n        filename: \"route\",\n        bundlePath: \"app/api/templates/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\api\\\\templates\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_HIMANSHU_OneDrive_Desktop_portfolio_saas_Copy_frontend_src_app_api_templates_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/templates/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Ftemplates%2Froute&page=%2Fapi%2Ftemplates%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftemplates%2Froute.ts&appDir=C%3A%5CUsers%5CHIMANSHU%5COneDrive%5CDesktop%5Cportfolio%20saas%20-%20Copy%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHIMANSHU%5COneDrive%5CDesktop%5Cportfolio%20saas%20-%20Copy%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/templates/route.ts":
/*!****************************************!*\
  !*** ./src/app/api/templates/route.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/../node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _services_template_processor__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/services/template-processor */ \"(rsc)/./src/services/template-processor.ts\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nasync function GET(request) {\n    try {\n        console.log(\"API: Loading templates...\");\n        // Load all templates\n        const templates = await (0,_services_template_processor__WEBPACK_IMPORTED_MODULE_1__.loadAllTemplates)();\n        console.log(\"API: Templates loaded:\", templates.length);\n        // Debug: Check if templates directory exists\n        const templatesDir = path__WEBPACK_IMPORTED_MODULE_2___default().join(process.cwd(), \"templates\", \"html-templates\");\n        console.log(\"API: Templates directory path:\", templatesDir);\n        console.log(\"API: Templates directory exists:\", fs__WEBPACK_IMPORTED_MODULE_3___default().existsSync(templatesDir));\n        if (fs__WEBPACK_IMPORTED_MODULE_3___default().existsSync(templatesDir)) {\n            const dirs = fs__WEBPACK_IMPORTED_MODULE_3___default().readdirSync(templatesDir, {\n                withFileTypes: true\n            }).filter((dirent)=>dirent.isDirectory()).map((dirent)=>dirent.name);\n            console.log(\"API: Template directories found:\", dirs);\n        }\n        // Return the templates as JSON\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            templates\n        });\n    } catch (error) {\n        console.error(\"Error loading templates:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Failed to load templates\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/templates/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/services/template-processor.ts":
/*!********************************************!*\
  !*** ./src/services/template-processor.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createSampleTemplateConfig: () => (/* binding */ createSampleTemplateConfig),\n/* harmony export */   loadAllTemplates: () => (/* binding */ loadAllTemplates),\n/* harmony export */   loadTemplateConfig: () => (/* binding */ loadTemplateConfig),\n/* harmony export */   processTemplate: () => (/* binding */ processTemplate),\n/* harmony export */   saveProcessedTemplate: () => (/* binding */ saveProcessedTemplate)\n/* harmony export */ });\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var jsdom__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! jsdom */ \"jsdom\");\n/* harmony import */ var jsdom__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(jsdom__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\n/**\r\n * Loads template configuration from the template directory\r\n */ async function loadTemplateConfig(templateId) {\n    try {\n        // Try the main path first (from project root)\n        const configPath = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"templates\", \"html-templates\", templateId, \"config.json\");\n        if (fs__WEBPACK_IMPORTED_MODULE_0___default().existsSync(configPath)) {\n            console.log(`Loading template config from: ${configPath}`);\n            const configData = fs__WEBPACK_IMPORTED_MODULE_0___default().readFileSync(configPath, \"utf-8\");\n            return JSON.parse(configData);\n        }\n        // Try the public directory path (for Next.js static files)\n        const publicConfigPath = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"frontend\", \"public\", \"templates\", \"html-templates\", templateId, \"config.json\");\n        if (fs__WEBPACK_IMPORTED_MODULE_0___default().existsSync(publicConfigPath)) {\n            console.log(`Loading template config from public path: ${publicConfigPath}`);\n            const configData = fs__WEBPACK_IMPORTED_MODULE_0___default().readFileSync(publicConfigPath, \"utf-8\");\n            return JSON.parse(configData);\n        }\n        console.error(`Template config not found for ${templateId}`);\n        return null;\n    } catch (error) {\n        console.error(`Error loading template config for ${templateId}:`, error);\n        return null;\n    }\n}\n/**\r\n * Loads all available templates\r\n */ async function loadAllTemplates() {\n    try {\n        // Try the main path first (from project root)\n        const templatesDir = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"templates\", \"html-templates\");\n        console.log(\"Loading templates from:\", templatesDir);\n        if (!fs__WEBPACK_IMPORTED_MODULE_0___default().existsSync(templatesDir)) {\n            console.warn(\"Templates directory does not exist\");\n            // Try alternative path (from frontend directory)\n            const altTemplatesDir = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"..\", \"templates\", \"html-templates\");\n            console.log(\"Trying alternative path:\", altTemplatesDir);\n            if (!fs__WEBPACK_IMPORTED_MODULE_0___default().existsSync(altTemplatesDir)) {\n                console.warn(\"Alternative templates directory does not exist either\");\n                return [];\n            }\n            // Use the alternative path\n            const templateFolders = fs__WEBPACK_IMPORTED_MODULE_0___default().readdirSync(altTemplatesDir, {\n                withFileTypes: true\n            }).filter((dirent)=>dirent.isDirectory()).map((dirent)=>dirent.name);\n            console.log(\"Found template folders:\", templateFolders);\n            const templates = [];\n            for (const folder of templateFolders){\n                const configPath = path__WEBPACK_IMPORTED_MODULE_1___default().join(altTemplatesDir, folder, \"config.json\");\n                if (!fs__WEBPACK_IMPORTED_MODULE_0___default().existsSync(configPath)) {\n                    console.warn(`Config file not found for template ${folder}`);\n                    continue;\n                }\n                try {\n                    const configData = fs__WEBPACK_IMPORTED_MODULE_0___default().readFileSync(configPath, \"utf-8\");\n                    const config = JSON.parse(configData);\n                    templates.push(config);\n                    console.log(`Loaded template: ${config.name} (${config.id})`);\n                } catch (err) {\n                    console.error(`Error loading config for template ${folder}:`, err);\n                }\n            }\n            return templates;\n        }\n        const templateFolders = fs__WEBPACK_IMPORTED_MODULE_0___default().readdirSync(templatesDir, {\n            withFileTypes: true\n        }).filter((dirent)=>dirent.isDirectory()).map((dirent)=>dirent.name);\n        console.log(\"Found template folders:\", templateFolders);\n        const templates = [];\n        for (const folder of templateFolders){\n            const config = await loadTemplateConfig(folder);\n            if (config) {\n                templates.push(config);\n                console.log(`Loaded template: ${config.name} (${config.id})`);\n            }\n        }\n        return templates;\n    } catch (error) {\n        console.error(\"Error loading templates:\", error);\n        return [];\n    }\n}\n/**\r\n * Processes an HTML template by replacing placeholders with actual content\r\n */ async function processTemplate(templateId, content, colors) {\n    try {\n        const config = await loadTemplateConfig(templateId);\n        if (!config) {\n            throw new Error(`Template config not found for ${templateId}`);\n        }\n        const templatePath = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"templates\", \"html-templates\", templateId, config.mainFile);\n        if (!fs__WEBPACK_IMPORTED_MODULE_0___default().existsSync(templatePath)) {\n            throw new Error(`Template file not found: ${templatePath}`);\n        }\n        // Read the HTML template\n        let htmlContent = fs__WEBPACK_IMPORTED_MODULE_0___default().readFileSync(templatePath, \"utf-8\");\n        // Replace content placeholders\n        htmlContent = replaceContentPlaceholders(htmlContent, content, config.placeholders);\n        // Process the HTML with JSDOM to modify styles and other elements\n        const dom = new jsdom__WEBPACK_IMPORTED_MODULE_2__.JSDOM(htmlContent);\n        const document = dom.window.document;\n        // Apply color scheme\n        applyColorScheme(document, colors);\n        // Process any dynamic sections (skills, projects, experience)\n        processDynamicSections(document, content, config);\n        // Return the processed HTML\n        return dom.serialize();\n    } catch (error) {\n        console.error(`Error processing template ${templateId}:`, error);\n        return null;\n    }\n}\n/**\r\n * Replaces content placeholders in the HTML\r\n */ function replaceContentPlaceholders(html, content, placeholders) {\n    let processedHtml = html;\n    // Process each content type\n    for (const [key, placeholderList] of Object.entries(placeholders)){\n        if (content[key] && typeof content[key] === \"string\") {\n            // Replace simple string placeholders\n            for (const placeholder of placeholderList){\n                processedHtml = processedHtml.replace(new RegExp(placeholder, \"g\"), content[key]);\n            }\n        }\n    }\n    // Handle social media links\n    if (content.social) {\n        processedHtml = processedHtml.replace(/\\{\\{linkedin\\}\\}/g, content.social.linkedin || \"#\");\n        processedHtml = processedHtml.replace(/\\{\\{github\\}\\}/g, content.social.github || \"#\");\n        processedHtml = processedHtml.replace(/\\{\\{twitter\\}\\}/g, content.social.twitter || \"#\");\n    }\n    // Handle email and phone if they exist\n    processedHtml = processedHtml.replace(/\\{\\{email\\}\\}/g, content.email || \"<EMAIL>\");\n    processedHtml = processedHtml.replace(/\\{\\{phone\\}\\}/g, content.phone || \"+****************\");\n    return processedHtml;\n}\n/**\r\n * Applies the color scheme to the HTML document\r\n */ function applyColorScheme(document, colors) {\n    // Find all style tags\n    const styleTags = document.querySelectorAll(\"style\");\n    styleTags.forEach((styleTag)=>{\n        let cssContent = styleTag.textContent || \"\";\n        // Replace color variables or specific color values\n        cssContent = cssContent.replace(/--primary-color:\\s*[^;]+;/g, `--primary-color: ${colors.primary};`).replace(/--secondary-color:\\s*[^;]+;/g, `--secondary-color: ${colors.secondary};`).replace(/--accent-color:\\s*[^;]+;/g, `--accent-color: ${colors.accent};`);\n        styleTag.textContent = cssContent;\n    });\n    // Also look for inline styles with color properties\n    const elementsWithStyle = document.querySelectorAll('[style*=\"color\"]');\n    elementsWithStyle.forEach((element)=>{\n        const style = element.getAttribute(\"style\") || \"\";\n        // Replace color values in inline styles\n        const updatedStyle = style.replace(/color:\\s*var\\(--primary-color\\)/g, `color: ${colors.primary}`).replace(/color:\\s*var\\(--secondary-color\\)/g, `color: ${colors.secondary}`).replace(/color:\\s*var\\(--accent-color\\)/g, `color: ${colors.accent}`).replace(/background-color:\\s*var\\(--primary-color\\)/g, `background-color: ${colors.primary}`).replace(/background-color:\\s*var\\(--secondary-color\\)/g, `background-color: ${colors.secondary}`).replace(/background-color:\\s*var\\(--accent-color\\)/g, `background-color: ${colors.accent}`);\n        element.setAttribute(\"style\", updatedStyle);\n    });\n}\n/**\r\n * Processes dynamic sections like skills, projects, and experience\r\n */ function processDynamicSections(document, content, config) {\n    // Process skills section\n    processSkillsSection(document, content.skills);\n    // Process projects section\n    processProjectsSection(document, content.projects);\n    // Process experience section\n    processExperienceSection(document, content.experience);\n}\n/**\r\n * Processes the skills section\r\n */ function processSkillsSection(document, skills) {\n    const skillsContainer = document.querySelector('.skills-container, #skills-container, [data-section=\"skills\"]');\n    if (skillsContainer && skills.length > 0) {\n        // Clear existing skills\n        skillsContainer.innerHTML = \"\";\n        // Create skill elements for each skill\n        skills.forEach((skill)=>{\n            const skillElement = document.createElement(\"div\");\n            skillElement.className = \"skill\";\n            skillElement.textContent = skill;\n            skillsContainer.appendChild(skillElement);\n        });\n    }\n}\n/**\r\n * Processes the projects section\r\n */ function processProjectsSection(document, projects) {\n    const projectsContainer = document.querySelector('.projects-container, #projects-container, [data-section=\"projects\"]');\n    if (projectsContainer && projects.length > 0) {\n        // Clear existing projects\n        projectsContainer.innerHTML = \"\";\n        // Create project elements for each project\n        projects.forEach((project)=>{\n            const projectElement = document.createElement(\"div\");\n            projectElement.className = \"project\";\n            // Create project content\n            projectElement.innerHTML = `\r\n        <img src=\"${project.image || \"/placeholder-project.jpg\"}\" alt=\"${project.title}\">\r\n        <div class=\"project-content\">\r\n          <h3 class=\"project-title\">${project.title}</h3>\r\n          <p class=\"project-description\">${project.description}</p>\r\n          ${project.link ? `<a href=\"${project.link}\" class=\"project-link\" target=\"_blank\">View Project</a>` : \"\"}\r\n        </div>\r\n      `;\n            projectsContainer.appendChild(projectElement);\n        });\n    }\n}\n/**\r\n * Processes the experience section\r\n */ function processExperienceSection(document, experiences) {\n    const experienceContainer = document.querySelector('.experience-container, #experience-container, [data-section=\"experience\"]');\n    if (experienceContainer && experiences.length > 0) {\n        // Clear existing experiences\n        experienceContainer.innerHTML = \"\";\n        // Create experience elements for each experience\n        experiences.forEach((exp)=>{\n            const expElement = document.createElement(\"div\");\n            expElement.className = \"experience-item\";\n            const dateText = exp.endDate ? `${exp.startDate} - ${exp.endDate}` : `${exp.startDate} - Present`;\n            // Create experience content\n            expElement.innerHTML = `\r\n        <h3 class=\"job-title\">${exp.title}</h3>\r\n        <p class=\"company-name\">${exp.company}</p>\r\n        <p class=\"job-date\">${dateText}</p>\r\n        <p class=\"job-description\">${exp.description}</p>\r\n      `;\n            experienceContainer.appendChild(expElement);\n        });\n    }\n}\n/**\r\n * Saves the processed HTML to the output directory\r\n */ async function saveProcessedTemplate(portfolioId, html, templateId) {\n    try {\n        // Create the output directory\n        const outputDir = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"data\", \"portfolios\", portfolioId);\n        if (!fs__WEBPACK_IMPORTED_MODULE_0___default().existsSync(outputDir)) {\n            fs__WEBPACK_IMPORTED_MODULE_0___default().mkdirSync(outputDir, {\n                recursive: true\n            });\n        }\n        // Save the HTML file\n        const htmlPath = path__WEBPACK_IMPORTED_MODULE_1___default().join(outputDir, \"index.html\");\n        fs__WEBPACK_IMPORTED_MODULE_0___default().writeFileSync(htmlPath, html);\n        // Copy all assets from the template directory\n        const templateDir = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"templates\", \"html-templates\", templateId);\n        copyTemplateAssets(templateDir, outputDir);\n        return outputDir;\n    } catch (error) {\n        console.error(`Error saving processed template for portfolio ${portfolioId}:`, error);\n        throw new Error(\"Failed to save processed template\");\n    }\n}\n/**\r\n * Copies all assets from the template directory to the output directory\r\n */ function copyTemplateAssets(sourceDir, targetDir) {\n    // Read all files and directories in the source directory\n    const items = fs__WEBPACK_IMPORTED_MODULE_0___default().readdirSync(sourceDir, {\n        withFileTypes: true\n    });\n    for (const item of items){\n        const sourcePath = path__WEBPACK_IMPORTED_MODULE_1___default().join(sourceDir, item.name);\n        const targetPath = path__WEBPACK_IMPORTED_MODULE_1___default().join(targetDir, item.name);\n        if (item.isDirectory()) {\n            // Create the directory in the target\n            if (!fs__WEBPACK_IMPORTED_MODULE_0___default().existsSync(targetPath)) {\n                fs__WEBPACK_IMPORTED_MODULE_0___default().mkdirSync(targetPath, {\n                    recursive: true\n                });\n            }\n            // Recursively copy contents\n            copyTemplateAssets(sourcePath, targetPath);\n        } else if (item.isFile() && !item.name.endsWith(\".html\") && item.name !== \"config.json\") {\n            // Copy the file (excluding HTML and config files)\n            fs__WEBPACK_IMPORTED_MODULE_0___default().copyFileSync(sourcePath, targetPath);\n        }\n    }\n}\n/**\r\n * Creates a sample template config file\r\n */ function createSampleTemplateConfig(templateId, templateName, description) {\n    return {\n        id: templateId,\n        name: templateName,\n        description: description,\n        folderPath: templateId,\n        mainFile: \"index.html\",\n        previewImage: `/${templateId}/preview.jpg`,\n        placeholders: {\n            name: [\n                \"{{NAME}}\",\n                \"{{name}}\",\n                \"{{User Name}}\"\n            ],\n            title: [\n                \"{{TITLE}}\",\n                \"{{title}}\",\n                \"{{Job Title}}\"\n            ],\n            bio: [\n                \"{{BIO}}\",\n                \"{{bio}}\",\n                \"{{About Me}}\",\n                \"{{about}}\"\n            ],\n            skills: [\n                \"{{SKILLS}}\",\n                \"{{skills}}\"\n            ],\n            projects: [\n                \"{{PROJECTS}}\",\n                \"{{projects}}\"\n            ],\n            experience: [\n                \"{{EXPERIENCE}}\",\n                \"{{experience}}\",\n                \"{{work}}\"\n            ],\n            contact: [\n                \"{{CONTACT}}\",\n                \"{{contact}}\",\n                \"{{email}}\",\n                \"{{phone}}\"\n            ],\n            social: [\n                \"{{SOCIAL}}\",\n                \"{{social}}\",\n                \"{{linkedin}}\",\n                \"{{github}}\",\n                \"{{twitter}}\"\n            ]\n        }\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/services/template-processor.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Ftemplates%2Froute&page=%2Fapi%2Ftemplates%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftemplates%2Froute.ts&appDir=C%3A%5CUsers%5CHIMANSHU%5COneDrive%5CDesktop%5Cportfolio%20saas%20-%20Copy%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHIMANSHU%5COneDrive%5CDesktop%5Cportfolio%20saas%20-%20Copy%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();