"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/templates/route";
exports.ids = ["app/api/templates/route"];
exports.modules = {

/***/ "jsdom":
/*!************************!*\
  !*** external "jsdom" ***!
  \************************/
/***/ ((module) => {

module.exports = require("jsdom");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "(rsc)/../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Ftemplates%2Froute&page=%2Fapi%2Ftemplates%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftemplates%2Froute.ts&appDir=C%3A%5CUsers%5CHIMANSHU%5COneDrive%5CDesktop%5Cportfolio%20saas%20-%20Copy%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHIMANSHU%5COneDrive%5CDesktop%5Cportfolio%20saas%20-%20Copy%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Ftemplates%2Froute&page=%2Fapi%2Ftemplates%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftemplates%2Froute.ts&appDir=C%3A%5CUsers%5CHIMANSHU%5COneDrive%5CDesktop%5Cportfolio%20saas%20-%20Copy%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHIMANSHU%5COneDrive%5CDesktop%5Cportfolio%20saas%20-%20Copy%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/../node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/../node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/../node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_HIMANSHU_OneDrive_Desktop_portfolio_saas_Copy_frontend_src_app_api_templates_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/templates/route.ts */ \"(rsc)/./src/app/api/templates/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/templates/route\",\n        pathname: \"/api/templates\",\n        filename: \"route\",\n        bundlePath: \"app/api/templates/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\api\\\\templates\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_HIMANSHU_OneDrive_Desktop_portfolio_saas_Copy_frontend_src_app_api_templates_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/templates/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Ftemplates%2Froute&page=%2Fapi%2Ftemplates%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftemplates%2Froute.ts&appDir=C%3A%5CUsers%5CHIMANSHU%5COneDrive%5CDesktop%5Cportfolio%20saas%20-%20Copy%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHIMANSHU%5COneDrive%5CDesktop%5Cportfolio%20saas%20-%20Copy%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/templates/route.ts":
/*!****************************************!*\
  !*** ./src/app/api/templates/route.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/../node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _services_template_processor__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/services/template-processor */ \"(rsc)/./src/services/template-processor.ts\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nasync function GET(request) {\n    try {\n        console.log(\"API: Loading templates...\");\n        // Load all templates\n        const templates = await (0,_services_template_processor__WEBPACK_IMPORTED_MODULE_1__.loadAllTemplates)();\n        console.log(\"API: Templates loaded:\", templates.length);\n        // Debug: Check if templates directory exists\n        const templatesDir = path__WEBPACK_IMPORTED_MODULE_2___default().join(process.cwd(), \"templates\", \"html-templates\");\n        console.log(\"API: Templates directory path:\", templatesDir);\n        console.log(\"API: Templates directory exists:\", fs__WEBPACK_IMPORTED_MODULE_3___default().existsSync(templatesDir));\n        if (fs__WEBPACK_IMPORTED_MODULE_3___default().existsSync(templatesDir)) {\n            const dirs = fs__WEBPACK_IMPORTED_MODULE_3___default().readdirSync(templatesDir, {\n                withFileTypes: true\n            }).filter((dirent)=>dirent.isDirectory()).map((dirent)=>dirent.name);\n            console.log(\"API: Template directories found:\", dirs);\n        }\n        // Return the templates as JSON\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            templates\n        });\n    } catch (error) {\n        console.error(\"Error loading templates:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Failed to load templates\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/templates/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/services/template-processor.ts":
/*!********************************************!*\
  !*** ./src/services/template-processor.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createSampleTemplateConfig: () => (/* binding */ createSampleTemplateConfig),\n/* harmony export */   loadAllTemplates: () => (/* binding */ loadAllTemplates),\n/* harmony export */   loadTemplateConfig: () => (/* binding */ loadTemplateConfig),\n/* harmony export */   processTemplate: () => (/* binding */ processTemplate),\n/* harmony export */   saveProcessedTemplate: () => (/* binding */ saveProcessedTemplate)\n/* harmony export */ });\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var jsdom__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! jsdom */ \"jsdom\");\n/* harmony import */ var jsdom__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(jsdom__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\n/**\r\n * Loads template configuration from the template directory\r\n */ async function loadTemplateConfig(templateId) {\n    try {\n        // Try different possible paths for the template config\n        const possiblePaths = [\n            // From project root\n            path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"templates\", \"html-templates\", templateId, \"config.json\"),\n            // From parent directory (when running from frontend folder)\n            path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"..\", \"templates\", \"html-templates\", templateId, \"config.json\"),\n            // From frontend public directory\n            path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"frontend\", \"public\", \"templates\", \"html-templates\", templateId, \"config.json\"),\n            // Alternative frontend path\n            path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"frontend\", \"templates\", \"html-templates\", templateId, \"config.json\")\n        ];\n        for (const configPath of possiblePaths){\n            if (fs__WEBPACK_IMPORTED_MODULE_0___default().existsSync(configPath)) {\n                console.log(`Loading template config from: ${configPath}`);\n                const configData = fs__WEBPACK_IMPORTED_MODULE_0___default().readFileSync(configPath, \"utf-8\");\n                return JSON.parse(configData);\n            }\n        }\n        console.error(`Template config not found for ${templateId} in any of the following paths:`, possiblePaths);\n        return null;\n    } catch (error) {\n        console.error(`Error loading template config for ${templateId}:`, error);\n        return null;\n    }\n}\n/**\r\n * Loads all available templates\r\n */ async function loadAllTemplates() {\n    try {\n        // Try different possible paths for templates directory\n        const possibleTemplatesDirs = [\n            path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"templates\", \"html-templates\"),\n            path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"..\", \"templates\", \"html-templates\"),\n            path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"frontend\", \"templates\", \"html-templates\")\n        ];\n        let templatesDir = null;\n        for (const testDir of possibleTemplatesDirs){\n            console.log(\"Testing templates directory:\", testDir);\n            if (fs__WEBPACK_IMPORTED_MODULE_0___default().existsSync(testDir)) {\n                templatesDir = testDir;\n                console.log(\"Found templates directory at:\", templatesDir);\n                break;\n            }\n        }\n        if (!templatesDir) {\n            console.warn(\"Templates directory not found in any of the expected locations:\", possibleTemplatesDirs);\n            return [];\n        }\n        const templateFolders = fs__WEBPACK_IMPORTED_MODULE_0___default().readdirSync(templatesDir, {\n            withFileTypes: true\n        }).filter((dirent)=>dirent.isDirectory()).map((dirent)=>dirent.name);\n        console.log(\"Found template folders:\", templateFolders);\n        const templates = [];\n        for (const folder of templateFolders){\n            const config = await loadTemplateConfig(folder);\n            if (config) {\n                templates.push(config);\n                console.log(`Loaded template: ${config.name} (${config.id})`);\n            }\n        }\n        return templates;\n    } catch (error) {\n        console.error(\"Error loading templates:\", error);\n        return [];\n    }\n}\n/**\r\n * Processes an HTML template by replacing placeholders with actual content\r\n */ async function processTemplate(templateId, content, colors) {\n    try {\n        const config = await loadTemplateConfig(templateId);\n        if (!config) {\n            throw new Error(`Template config not found for ${templateId}`);\n        }\n        // Try different possible paths for the template HTML file\n        const possibleTemplatePaths = [\n            path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"templates\", \"html-templates\", templateId, config.mainFile),\n            path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"..\", \"templates\", \"html-templates\", templateId, config.mainFile),\n            path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"frontend\", \"templates\", \"html-templates\", templateId, config.mainFile)\n        ];\n        let templatePath = null;\n        for (const testPath of possibleTemplatePaths){\n            if (fs__WEBPACK_IMPORTED_MODULE_0___default().existsSync(testPath)) {\n                templatePath = testPath;\n                console.log(`Found template file at: ${templatePath}`);\n                break;\n            }\n        }\n        if (!templatePath) {\n            throw new Error(`Template file not found for ${templateId} in any of the following paths: ${possibleTemplatePaths.join(\", \")}`);\n        }\n        // Read the HTML template\n        let htmlContent = fs__WEBPACK_IMPORTED_MODULE_0___default().readFileSync(templatePath, \"utf-8\");\n        // Replace content placeholders\n        htmlContent = replaceContentPlaceholders(htmlContent, content, config.placeholders);\n        // Process the HTML with JSDOM to modify styles and other elements\n        const dom = new jsdom__WEBPACK_IMPORTED_MODULE_2__.JSDOM(htmlContent);\n        const document = dom.window.document;\n        // Apply color scheme\n        applyColorScheme(document, colors);\n        // Process any dynamic sections (skills, projects, experience)\n        processDynamicSections(document, content, config);\n        // Return the processed HTML\n        return dom.serialize();\n    } catch (error) {\n        console.error(`Error processing template ${templateId}:`, error);\n        return null;\n    }\n}\n/**\r\n * Replaces content placeholders in the HTML\r\n */ function replaceContentPlaceholders(html, content, placeholders) {\n    let processedHtml = html;\n    // Process each content type\n    for (const [key, placeholderList] of Object.entries(placeholders)){\n        if (content[key] && typeof content[key] === \"string\") {\n            // Replace simple string placeholders\n            for (const placeholder of placeholderList){\n                processedHtml = processedHtml.replace(new RegExp(placeholder, \"g\"), content[key]);\n            }\n        }\n    }\n    // Handle social media links\n    if (content.social) {\n        processedHtml = processedHtml.replace(/\\{\\{linkedin\\}\\}/g, content.social.linkedin || \"#\");\n        processedHtml = processedHtml.replace(/\\{\\{github\\}\\}/g, content.social.github || \"#\");\n        processedHtml = processedHtml.replace(/\\{\\{twitter\\}\\}/g, content.social.twitter || \"#\");\n    }\n    // Handle email and phone if they exist\n    processedHtml = processedHtml.replace(/\\{\\{email\\}\\}/g, content.email || \"<EMAIL>\");\n    processedHtml = processedHtml.replace(/\\{\\{phone\\}\\}/g, content.phone || \"+****************\");\n    return processedHtml;\n}\n/**\r\n * Applies the color scheme to the HTML document\r\n */ function applyColorScheme(document, colors) {\n    // Find all style tags\n    const styleTags = document.querySelectorAll(\"style\");\n    styleTags.forEach((styleTag)=>{\n        let cssContent = styleTag.textContent || \"\";\n        // Replace color variables or specific color values\n        cssContent = cssContent.replace(/--primary-color:\\s*[^;]+;/g, `--primary-color: ${colors.primary};`).replace(/--secondary-color:\\s*[^;]+;/g, `--secondary-color: ${colors.secondary};`).replace(/--accent-color:\\s*[^;]+;/g, `--accent-color: ${colors.accent};`);\n        styleTag.textContent = cssContent;\n    });\n    // Also look for inline styles with color properties\n    const elementsWithStyle = document.querySelectorAll('[style*=\"color\"]');\n    elementsWithStyle.forEach((element)=>{\n        const style = element.getAttribute(\"style\") || \"\";\n        // Replace color values in inline styles\n        const updatedStyle = style.replace(/color:\\s*var\\(--primary-color\\)/g, `color: ${colors.primary}`).replace(/color:\\s*var\\(--secondary-color\\)/g, `color: ${colors.secondary}`).replace(/color:\\s*var\\(--accent-color\\)/g, `color: ${colors.accent}`).replace(/background-color:\\s*var\\(--primary-color\\)/g, `background-color: ${colors.primary}`).replace(/background-color:\\s*var\\(--secondary-color\\)/g, `background-color: ${colors.secondary}`).replace(/background-color:\\s*var\\(--accent-color\\)/g, `background-color: ${colors.accent}`);\n        element.setAttribute(\"style\", updatedStyle);\n    });\n}\n/**\r\n * Processes dynamic sections like skills, projects, and experience\r\n */ function processDynamicSections(document, content, config) {\n    // Process skills section\n    processSkillsSection(document, content.skills);\n    // Process projects section\n    processProjectsSection(document, content.projects);\n    // Process experience section\n    processExperienceSection(document, content.experience);\n}\n/**\r\n * Processes the skills section\r\n */ function processSkillsSection(document, skills) {\n    const skillsContainer = document.querySelector('.skills-container, #skills-container, [data-section=\"skills\"]');\n    if (skillsContainer && skills.length > 0) {\n        // Clear existing skills\n        skillsContainer.innerHTML = \"\";\n        // Create skill elements for each skill\n        skills.forEach((skill)=>{\n            const skillElement = document.createElement(\"div\");\n            skillElement.className = \"skill\";\n            skillElement.textContent = skill;\n            skillsContainer.appendChild(skillElement);\n        });\n    }\n}\n/**\r\n * Processes the projects section\r\n */ function processProjectsSection(document, projects) {\n    const projectsContainer = document.querySelector('.projects-container, #projects-container, [data-section=\"projects\"]');\n    if (projectsContainer && projects.length > 0) {\n        // Clear existing projects\n        projectsContainer.innerHTML = \"\";\n        // Create project elements for each project\n        projects.forEach((project)=>{\n            const projectElement = document.createElement(\"div\");\n            projectElement.className = \"project\";\n            // Create project content\n            projectElement.innerHTML = `\r\n        <img src=\"${project.image || \"/placeholder-project.jpg\"}\" alt=\"${project.title}\">\r\n        <div class=\"project-content\">\r\n          <h3 class=\"project-title\">${project.title}</h3>\r\n          <p class=\"project-description\">${project.description}</p>\r\n          ${project.link ? `<a href=\"${project.link}\" class=\"project-link\" target=\"_blank\">View Project</a>` : \"\"}\r\n        </div>\r\n      `;\n            projectsContainer.appendChild(projectElement);\n        });\n    }\n}\n/**\r\n * Processes the experience section\r\n */ function processExperienceSection(document, experiences) {\n    const experienceContainer = document.querySelector('.experience-container, #experience-container, [data-section=\"experience\"]');\n    if (experienceContainer && experiences.length > 0) {\n        // Clear existing experiences\n        experienceContainer.innerHTML = \"\";\n        // Create experience elements for each experience\n        experiences.forEach((exp)=>{\n            const expElement = document.createElement(\"div\");\n            expElement.className = \"experience-item\";\n            const dateText = exp.endDate ? `${exp.startDate} - ${exp.endDate}` : `${exp.startDate} - Present`;\n            // Create experience content\n            expElement.innerHTML = `\r\n        <h3 class=\"job-title\">${exp.title}</h3>\r\n        <p class=\"company-name\">${exp.company}</p>\r\n        <p class=\"job-date\">${dateText}</p>\r\n        <p class=\"job-description\">${exp.description}</p>\r\n      `;\n            experienceContainer.appendChild(expElement);\n        });\n    }\n}\n/**\r\n * Saves the processed HTML to the output directory\r\n */ async function saveProcessedTemplate(portfolioId, html, templateId) {\n    try {\n        // Try different possible output directories\n        const possibleOutputDirs = [\n            path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"data\", \"portfolios\", portfolioId),\n            path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"frontend\", \"data\", \"portfolios\", portfolioId),\n            path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"..\", \"data\", \"portfolios\", portfolioId)\n        ];\n        // Use the first directory that we can create or that already exists\n        let outputDir = possibleOutputDirs[0]; // Default to first option\n        // Try to create the directory\n        if (!fs__WEBPACK_IMPORTED_MODULE_0___default().existsSync(outputDir)) {\n            fs__WEBPACK_IMPORTED_MODULE_0___default().mkdirSync(outputDir, {\n                recursive: true\n            });\n        }\n        console.log(`Saving portfolio to: ${outputDir}`);\n        // Save the HTML file\n        const htmlPath = path__WEBPACK_IMPORTED_MODULE_1___default().join(outputDir, \"index.html\");\n        fs__WEBPACK_IMPORTED_MODULE_0___default().writeFileSync(htmlPath, html);\n        console.log(`HTML file saved to: ${htmlPath}`);\n        // Find the template directory and copy assets\n        const possibleTemplateDirs = [\n            path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"templates\", \"html-templates\", templateId),\n            path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"..\", \"templates\", \"html-templates\", templateId),\n            path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"frontend\", \"templates\", \"html-templates\", templateId)\n        ];\n        let templateDir = null;\n        for (const testDir of possibleTemplateDirs){\n            if (fs__WEBPACK_IMPORTED_MODULE_0___default().existsSync(testDir)) {\n                templateDir = testDir;\n                console.log(`Found template directory at: ${templateDir}`);\n                break;\n            }\n        }\n        if (templateDir) {\n            copyTemplateAssets(templateDir, outputDir);\n        } else {\n            console.warn(`Template directory not found for ${templateId}`);\n        }\n        return outputDir;\n    } catch (error) {\n        console.error(`Error saving processed template for portfolio ${portfolioId}:`, error);\n        throw new Error(\"Failed to save processed template\");\n    }\n}\n/**\r\n * Copies all assets from the template directory to the output directory\r\n */ function copyTemplateAssets(sourceDir, targetDir) {\n    // Read all files and directories in the source directory\n    const items = fs__WEBPACK_IMPORTED_MODULE_0___default().readdirSync(sourceDir, {\n        withFileTypes: true\n    });\n    for (const item of items){\n        const sourcePath = path__WEBPACK_IMPORTED_MODULE_1___default().join(sourceDir, item.name);\n        const targetPath = path__WEBPACK_IMPORTED_MODULE_1___default().join(targetDir, item.name);\n        if (item.isDirectory()) {\n            // Create the directory in the target\n            if (!fs__WEBPACK_IMPORTED_MODULE_0___default().existsSync(targetPath)) {\n                fs__WEBPACK_IMPORTED_MODULE_0___default().mkdirSync(targetPath, {\n                    recursive: true\n                });\n            }\n            // Recursively copy contents\n            copyTemplateAssets(sourcePath, targetPath);\n        } else if (item.isFile() && !item.name.endsWith(\".html\") && item.name !== \"config.json\") {\n            // Copy the file (excluding HTML and config files)\n            fs__WEBPACK_IMPORTED_MODULE_0___default().copyFileSync(sourcePath, targetPath);\n        }\n    }\n}\n/**\r\n * Creates a sample template config file\r\n */ function createSampleTemplateConfig(templateId, templateName, description) {\n    return {\n        id: templateId,\n        name: templateName,\n        description: description,\n        folderPath: templateId,\n        mainFile: \"index.html\",\n        previewImage: `/${templateId}/preview.jpg`,\n        placeholders: {\n            name: [\n                \"{{NAME}}\",\n                \"{{name}}\",\n                \"{{User Name}}\"\n            ],\n            title: [\n                \"{{TITLE}}\",\n                \"{{title}}\",\n                \"{{Job Title}}\"\n            ],\n            bio: [\n                \"{{BIO}}\",\n                \"{{bio}}\",\n                \"{{About Me}}\",\n                \"{{about}}\"\n            ],\n            skills: [\n                \"{{SKILLS}}\",\n                \"{{skills}}\"\n            ],\n            projects: [\n                \"{{PROJECTS}}\",\n                \"{{projects}}\"\n            ],\n            experience: [\n                \"{{EXPERIENCE}}\",\n                \"{{experience}}\",\n                \"{{work}}\"\n            ],\n            contact: [\n                \"{{CONTACT}}\",\n                \"{{contact}}\",\n                \"{{email}}\",\n                \"{{phone}}\"\n            ],\n            social: [\n                \"{{SOCIAL}}\",\n                \"{{social}}\",\n                \"{{linkedin}}\",\n                \"{{github}}\",\n                \"{{twitter}}\"\n            ]\n        }\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/services/template-processor.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Ftemplates%2Froute&page=%2Fapi%2Ftemplates%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftemplates%2Froute.ts&appDir=C%3A%5CUsers%5CHIMANSHU%5COneDrive%5CDesktop%5Cportfolio%20saas%20-%20Copy%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHIMANSHU%5COneDrive%5CDesktop%5Cportfolio%20saas%20-%20Copy%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();