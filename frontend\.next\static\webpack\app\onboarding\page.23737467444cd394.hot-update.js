"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/onboarding/page",{

/***/ "(app-pages-browser)/./src/app/onboarding/page.tsx":
/*!*************************************!*\
  !*** ./src/app/onboarding/page.tsx ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ OnboardingPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/../node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_create_steps__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/create/steps */ \"(app-pages-browser)/./src/components/create/steps.tsx\");\n/* harmony import */ var _components_onboarding_profession_selector__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/onboarding/profession-selector */ \"(app-pages-browser)/./src/components/onboarding/profession-selector.tsx\");\n/* harmony import */ var _components_onboarding_template_selector__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/onboarding/template-selector */ \"(app-pages-browser)/./src/components/onboarding/template-selector.tsx\");\n/* harmony import */ var _components_onboarding_color_palette__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/onboarding/color-palette */ \"(app-pages-browser)/./src/components/onboarding/color-palette.tsx\");\n/* harmony import */ var _components_onboarding_section_config__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/onboarding/section-config */ \"(app-pages-browser)/./src/components/onboarding/section-config.tsx\");\n/* harmony import */ var _radix_ui_react_icons__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-icons */ \"(app-pages-browser)/../node_modules/@radix-ui/react-icons/dist/react-icons.esm.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nconst steps = [\n    {\n        id: \"profession\",\n        title: \"Profession\",\n        description: \"Select your profession\"\n    },\n    {\n        id: \"template\",\n        title: \"Template\",\n        description: \"Choose a template\"\n    },\n    {\n        id: \"colors\",\n        title: \"Colors\",\n        description: \"Pick your brand colors\"\n    },\n    {\n        id: \"sections\",\n        title: \"Sections\",\n        description: \"Customize sections\"\n    },\n    {\n        id: \"content\",\n        title: \"Content\",\n        description: \"Add your content\"\n    }\n];\nfunction OnboardingPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [currentStep, setCurrentStep] = react__WEBPACK_IMPORTED_MODULE_1__.useState(0);\n    const [formData, setFormData] = react__WEBPACK_IMPORTED_MODULE_1__.useState({\n        profession: \"\",\n        template: \"\",\n        colors: {\n            primary: \"#0070f3\",\n            secondary: \"#6b7280\",\n            accent: \"#f59e0b\"\n        },\n        sections: [\n            {\n                id: \"hero\",\n                name: \"Hero\",\n                description: \"Introduction and main headline\",\n                isRequired: true,\n                isEnabled: true\n            },\n            {\n                id: \"about\",\n                name: \"About\",\n                description: \"Personal bio and background\",\n                isRequired: true,\n                isEnabled: true\n            },\n            {\n                id: \"experience\",\n                name: \"Experience\",\n                description: \"Work history and achievements\",\n                isEnabled: true\n            },\n            {\n                id: \"projects\",\n                name: \"Projects\",\n                description: \"Showcase of your work\",\n                isEnabled: true\n            },\n            {\n                id: \"skills\",\n                name: \"Skills\",\n                description: \"Technical and professional skills\",\n                isEnabled: true\n            },\n            {\n                id: \"testimonials\",\n                name: \"Testimonials\",\n                description: \"Client and colleague reviews\",\n                isEnabled: false\n            },\n            {\n                id: \"blog\",\n                name: \"Blog\",\n                description: \"Articles and thoughts\",\n                isEnabled: false\n            },\n            {\n                id: \"contact\",\n                name: \"Contact\",\n                description: \"Contact information and form\",\n                isRequired: true,\n                isEnabled: true\n            }\n        ],\n        content: {\n            name: \"\",\n            title: \"\",\n            bio: \"\",\n            skills: [],\n            experience: [],\n            projects: [],\n            email: \"\",\n            phone: \"\",\n            social: {\n                linkedin: \"\",\n                github: \"\",\n                twitter: \"\"\n            }\n        }\n    });\n    const handleNext = ()=>{\n        if (currentStep < steps.length - 1) {\n            setCurrentStep(currentStep + 1);\n        } else {\n            handleSubmit();\n        }\n    };\n    const handleBack = ()=>{\n        if (currentStep > 0) {\n            setCurrentStep(currentStep - 1);\n        }\n    };\n    const handleSubmit = async ()=>{\n        try {\n            console.log(\"Starting portfolio creation...\");\n            console.log(\"Form data:\", formData);\n            // Call the portfolio generation API\n            const response = await fetch(\"/api/portfolio/generate\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    profession: formData.profession,\n                    template: formData.template,\n                    colors: formData.colors,\n                    sections: formData.sections,\n                    content: formData.content\n                })\n            });\n            console.log(\"API response status:\", response.status);\n            const data = await response.json();\n            console.log(\"API response data:\", data);\n            if (data.success) {\n                console.log(\"Portfolio created successfully, redirecting to:\", data.portfolioUrl);\n                // Redirect to the generated portfolio\n                router.push(data.portfolioUrl);\n            } else {\n                console.error(\"Error generating portfolio:\", data.error);\n                alert(\"Error creating portfolio: \" + (data.error || \"Unknown error\"));\n            }\n        } catch (error) {\n            console.error(\"Error saving onboarding data:\", error);\n            alert(\"Error creating portfolio: \" + error.message);\n        }\n    };\n    const isStepValid = ()=>{\n        switch(currentStep){\n            case 0:\n                return !!formData.profession;\n            case 1:\n                return !!formData.template;\n            case 2:\n                return !!formData.colors.primary;\n            case 3:\n                return formData.sections.length > 0;\n            case 4:\n                return !!formData.content.name;\n            default:\n                return false;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"container max-w-4xl py-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                className: \"w-full\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                children: \"Create Your Portfolio\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                children: \"Follow these steps to create your personalized portfolio website\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                        lineNumber: 203,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        className: \"space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_create_steps__WEBPACK_IMPORTED_MODULE_5__.Steps, {\n                                steps: steps,\n                                currentStep: currentStep\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-8\",\n                                children: [\n                                    currentStep === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium\",\n                                                children: \"What's your profession?\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                                lineNumber: 216,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_onboarding_profession_selector__WEBPACK_IMPORTED_MODULE_6__.ProfessionSelector, {\n                                                value: formData.profession,\n                                                onChange: (value)=>setFormData({\n                                                        ...formData,\n                                                        profession: value\n                                                    })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                                lineNumber: 217,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 15\n                                    }, this),\n                                    currentStep === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium\",\n                                                children: \"Choose a template for your portfolio\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                                lineNumber: 227,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-muted-foreground\",\n                                                children: \"Select a template that best represents your professional style.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_onboarding_template_selector__WEBPACK_IMPORTED_MODULE_7__.TemplateSelector, {\n                                                value: formData.template,\n                                                onChange: (value)=>setFormData({\n                                                        ...formData,\n                                                        template: value\n                                                    })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                                lineNumber: 229,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 15\n                                    }, this),\n                                    currentStep === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium\",\n                                                children: \"Choose your brand colors\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                                lineNumber: 239,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-muted-foreground\",\n                                                children: \"Select a color palette or customize your own colors.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                                lineNumber: 240,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_onboarding_color_palette__WEBPACK_IMPORTED_MODULE_8__.ColorPalette, {\n                                                colors: formData.colors,\n                                                onChange: (colors)=>setFormData({\n                                                        ...formData,\n                                                        colors\n                                                    })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 15\n                                    }, this),\n                                    currentStep === 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium\",\n                                                children: \"Customize your portfolio sections\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                                lineNumber: 251,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-muted-foreground\",\n                                                children: \"Select and arrange the sections you want to include.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                                lineNumber: 252,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_onboarding_section_config__WEBPACK_IMPORTED_MODULE_9__.SectionConfig, {\n                                                sections: formData.sections,\n                                                onChange: (sections)=>setFormData({\n                                                        ...formData,\n                                                        sections\n                                                    })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                                lineNumber: 253,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 15\n                                    }, this),\n                                    currentStep === 4 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium\",\n                                                children: \"Add your personal information\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                                lineNumber: 263,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-muted-foreground\",\n                                                children: \"Fill in your details to personalize your portfolio.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                                lineNumber: 264,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SimpleContentForm, {\n                                                defaultValues: formData.content,\n                                                onSubmit: (content)=>{\n                                                    console.log(\"=== ONBOARDING RECEIVED CONTENT ===\");\n                                                    console.log(\"Content received:\", content);\n                                                    setFormData({\n                                                        ...formData,\n                                                        content: content\n                                                    });\n                                                    if (currentStep === steps.length - 1) {\n                                                        handleSubmit();\n                                                    } else {\n                                                        handleNext();\n                                                    }\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                                lineNumber: 265,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 9\n                    }, this),\n                    currentStep !== 4 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardFooter, {\n                        className: \"flex justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: handleBack,\n                                disabled: currentStep === 0,\n                                children: \"Back\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                lineNumber: 285,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: handleNext,\n                                disabled: !isStepValid(),\n                                children: currentStep === steps.length - 1 ? \"Create Portfolio\" : \"Next\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                lineNumber: 292,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                        lineNumber: 284,\n                        columnNumber: 11\n                    }, this),\n                    currentStep === 4 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardFooter, {\n                        className: \"flex justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: handleBack,\n                                children: \"Back\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                lineNumber: 302,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-muted-foreground\",\n                                children: 'Click \"Create Portfolio\" button in the form above to generate your portfolio'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                lineNumber: 308,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                        lineNumber: 301,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                lineNumber: 202,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-8 p-4 border rounded-lg bg-slate-50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_icons__WEBPACK_IMPORTED_MODULE_10__.InfoCircledIcon, {\n                                className: \"h-5 w-5 text-blue-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                lineNumber: 318,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-md font-medium\",\n                                children: \"Admin Information\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                lineNumber: 319,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                        lineNumber: 317,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-muted-foreground mb-2\",\n                        children: \"As an admin, you can upload and manage templates at:\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                        lineNumber: 321,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                        className: \"bg-slate-200 p-2 rounded text-sm block mb-2\",\n                        children: \"/templates\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                        lineNumber: 324,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-muted-foreground\",\n                        children: \"This page allows you to upload Envato Elements templates and configure them for use with the portfolio generator.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                        lineNumber: 327,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                lineNumber: 316,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n        lineNumber: 201,\n        columnNumber: 5\n    }, this);\n}\n_s(OnboardingPage, \"9YGt226IZAs450ATaUl2B60h8z8=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = OnboardingPage;\nvar _c;\n$RefreshReg$(_c, \"OnboardingPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvb25ib2FyZGluZy9wYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFOEI7QUFDYTtBQUNJO0FBUWxCO0FBQ29CO0FBQytCO0FBQ0o7QUFDUjtBQUNFO0FBRWY7QUFFdkQsTUFBTWUsUUFBUTtJQUNaO1FBQ0VDLElBQUk7UUFDSkMsT0FBTztRQUNQQyxhQUFhO0lBQ2Y7SUFDQTtRQUNFRixJQUFJO1FBQ0pDLE9BQU87UUFDUEMsYUFBYTtJQUNmO0lBQ0E7UUFDRUYsSUFBSTtRQUNKQyxPQUFPO1FBQ1BDLGFBQWE7SUFDZjtJQUNBO1FBQ0VGLElBQUk7UUFDSkMsT0FBTztRQUNQQyxhQUFhO0lBQ2Y7SUFDQTtRQUNFRixJQUFJO1FBQ0pDLE9BQU87UUFDUEMsYUFBYTtJQUNmO0NBQ0Q7QUFFYyxTQUFTQzs7SUFDdEIsTUFBTUMsU0FBU25CLDBEQUFTQTtJQUN4QixNQUFNLENBQUNvQixhQUFhQyxlQUFlLEdBQUd0QiwyQ0FBYyxDQUFDO0lBQ3JELE1BQU0sQ0FBQ3dCLFVBQVVDLFlBQVksR0FBR3pCLDJDQUFjLENBQUM7UUFDN0MwQixZQUFZO1FBQ1pDLFVBQVU7UUFDVkMsUUFBUTtZQUNOQyxTQUFTO1lBQ1RDLFdBQVc7WUFDWEMsUUFBUTtRQUNWO1FBQ0FDLFVBQVU7WUFDUjtnQkFDRWhCLElBQUk7Z0JBQ0ppQixNQUFNO2dCQUNOZixhQUFhO2dCQUNiZ0IsWUFBWTtnQkFDWkMsV0FBVztZQUNiO1lBQ0E7Z0JBQ0VuQixJQUFJO2dCQUNKaUIsTUFBTTtnQkFDTmYsYUFBYTtnQkFDYmdCLFlBQVk7Z0JBQ1pDLFdBQVc7WUFDYjtZQUNBO2dCQUNFbkIsSUFBSTtnQkFDSmlCLE1BQU07Z0JBQ05mLGFBQWE7Z0JBQ2JpQixXQUFXO1lBQ2I7WUFDQTtnQkFDRW5CLElBQUk7Z0JBQ0ppQixNQUFNO2dCQUNOZixhQUFhO2dCQUNiaUIsV0FBVztZQUNiO1lBQ0E7Z0JBQ0VuQixJQUFJO2dCQUNKaUIsTUFBTTtnQkFDTmYsYUFBYTtnQkFDYmlCLFdBQVc7WUFDYjtZQUNBO2dCQUNFbkIsSUFBSTtnQkFDSmlCLE1BQU07Z0JBQ05mLGFBQWE7Z0JBQ2JpQixXQUFXO1lBQ2I7WUFDQTtnQkFDRW5CLElBQUk7Z0JBQ0ppQixNQUFNO2dCQUNOZixhQUFhO2dCQUNiaUIsV0FBVztZQUNiO1lBQ0E7Z0JBQ0VuQixJQUFJO2dCQUNKaUIsTUFBTTtnQkFDTmYsYUFBYTtnQkFDYmdCLFlBQVk7Z0JBQ1pDLFdBQVc7WUFDYjtTQUNEO1FBQ0RDLFNBQVM7WUFDUEgsTUFBTTtZQUNOaEIsT0FBTztZQUNQb0IsS0FBSztZQUNMQyxRQUFRLEVBQUU7WUFDVkMsWUFBWSxFQUFFO1lBQ2RDLFVBQVUsRUFBRTtZQUNaQyxPQUFPO1lBQ1BDLE9BQU87WUFDUEMsUUFBUTtnQkFDTkMsVUFBVTtnQkFDVkMsUUFBUTtnQkFDUkMsU0FBUztZQUNYO1FBQ0Y7SUFDRjtJQUVBLE1BQU1DLGFBQWE7UUFDakIsSUFBSTFCLGNBQWNOLE1BQU1pQyxNQUFNLEdBQUcsR0FBRztZQUNsQzFCLGVBQWVELGNBQWM7UUFDL0IsT0FBTztZQUNMNEI7UUFDRjtJQUNGO0lBRUEsTUFBTUMsYUFBYTtRQUNqQixJQUFJN0IsY0FBYyxHQUFHO1lBQ25CQyxlQUFlRCxjQUFjO1FBQy9CO0lBQ0Y7SUFFQSxNQUFNNEIsZUFBZTtRQUNuQixJQUFJO1lBQ0ZFLFFBQVFDLEdBQUcsQ0FBQztZQUNaRCxRQUFRQyxHQUFHLENBQUMsY0FBYzVCO1lBRTFCLG9DQUFvQztZQUNwQyxNQUFNNkIsV0FBVyxNQUFNQyxNQUFNLDJCQUEyQjtnQkFDdERDLFFBQVE7Z0JBQ1JDLFNBQVM7b0JBQ1AsZ0JBQWdCO2dCQUNsQjtnQkFDQUMsTUFBTUMsS0FBS0MsU0FBUyxDQUFDO29CQUNuQmpDLFlBQVlGLFNBQVNFLFVBQVU7b0JBQy9CQyxVQUFVSCxTQUFTRyxRQUFRO29CQUMzQkMsUUFBUUosU0FBU0ksTUFBTTtvQkFDdkJJLFVBQVVSLFNBQVNRLFFBQVE7b0JBQzNCSSxTQUFTWixTQUFTWSxPQUFPO2dCQUMzQjtZQUNGO1lBRUFlLFFBQVFDLEdBQUcsQ0FBQyx3QkFBd0JDLFNBQVNPLE1BQU07WUFDbkQsTUFBTUMsT0FBTyxNQUFNUixTQUFTUyxJQUFJO1lBQ2hDWCxRQUFRQyxHQUFHLENBQUMsc0JBQXNCUztZQUVsQyxJQUFJQSxLQUFLRSxPQUFPLEVBQUU7Z0JBQ2hCWixRQUFRQyxHQUFHLENBQUMsbURBQW1EUyxLQUFLRyxZQUFZO2dCQUNoRixzQ0FBc0M7Z0JBQ3RDNUMsT0FBTzZDLElBQUksQ0FBQ0osS0FBS0csWUFBWTtZQUMvQixPQUFPO2dCQUNMYixRQUFRZSxLQUFLLENBQUMsK0JBQStCTCxLQUFLSyxLQUFLO2dCQUN2REMsTUFBTSwrQkFBZ0NOLENBQUFBLEtBQUtLLEtBQUssSUFBSSxlQUFjO1lBQ3BFO1FBQ0YsRUFBRSxPQUFPQSxPQUFPO1lBQ2RmLFFBQVFlLEtBQUssQ0FBQyxpQ0FBaUNBO1lBQy9DQyxNQUFNLCtCQUErQkQsTUFBTUUsT0FBTztRQUNwRDtJQUNGO0lBRUEsTUFBTUMsY0FBYztRQUNsQixPQUFRaEQ7WUFDTixLQUFLO2dCQUNILE9BQU8sQ0FBQyxDQUFDRyxTQUFTRSxVQUFVO1lBQzlCLEtBQUs7Z0JBQ0gsT0FBTyxDQUFDLENBQUNGLFNBQVNHLFFBQVE7WUFDNUIsS0FBSztnQkFDSCxPQUFPLENBQUMsQ0FBQ0gsU0FBU0ksTUFBTSxDQUFDQyxPQUFPO1lBQ2xDLEtBQUs7Z0JBQ0gsT0FBT0wsU0FBU1EsUUFBUSxDQUFDZ0IsTUFBTSxHQUFHO1lBQ3BDLEtBQUs7Z0JBQ0gsT0FBTyxDQUFDLENBQUN4QixTQUFTWSxPQUFPLENBQUNILElBQUk7WUFDaEM7Z0JBQ0UsT0FBTztRQUNYO0lBQ0Y7SUFFQSxxQkFDRSw4REFBQ3FDO1FBQUtDLFdBQVU7OzBCQUNkLDhEQUFDcEUscURBQUlBO2dCQUFDb0UsV0FBVTs7a0NBQ2QsOERBQUNoRSwyREFBVUE7OzBDQUNULDhEQUFDQywwREFBU0E7MENBQUM7Ozs7OzswQ0FDWCw4REFBQ0gsZ0VBQWVBOzBDQUFDOzs7Ozs7Ozs7Ozs7a0NBSW5CLDhEQUFDRCw0REFBV0E7d0JBQUNtRSxXQUFVOzswQ0FDckIsOERBQUM5RCwyREFBS0E7Z0NBQUNNLE9BQU9BO2dDQUFPTSxhQUFhQTs7Ozs7OzBDQUVsQyw4REFBQ21EO2dDQUFJRCxXQUFVOztvQ0FFWmxELGdCQUFnQixtQkFDZiw4REFBQ21EO3dDQUFJRCxXQUFVOzswREFDYiw4REFBQ0U7Z0RBQUdGLFdBQVU7MERBQXNCOzs7Ozs7MERBQ3BDLDhEQUFDN0QsMEZBQWtCQTtnREFDakJnRSxPQUFPbEQsU0FBU0UsVUFBVTtnREFDMUJpRCxVQUFVLENBQUNELFFBQ1RqRCxZQUFZO3dEQUFFLEdBQUdELFFBQVE7d0RBQUVFLFlBQVlnRDtvREFBTTs7Ozs7Ozs7Ozs7O29DQUtwRHJELGdCQUFnQixtQkFDZiw4REFBQ21EO3dDQUFJRCxXQUFVOzswREFDYiw4REFBQ0U7Z0RBQUdGLFdBQVU7MERBQXNCOzs7Ozs7MERBQ3BDLDhEQUFDSztnREFBRUwsV0FBVTswREFBZ0M7Ozs7OzswREFDN0MsOERBQUM1RCxzRkFBZ0JBO2dEQUNmK0QsT0FBT2xELFNBQVNHLFFBQVE7Z0RBQ3hCZ0QsVUFBVSxDQUFDRCxRQUNUakQsWUFBWTt3REFBRSxHQUFHRCxRQUFRO3dEQUFFRyxVQUFVK0M7b0RBQU07Ozs7Ozs7Ozs7OztvQ0FLbERyRCxnQkFBZ0IsbUJBQ2YsOERBQUNtRDt3Q0FBSUQsV0FBVTs7MERBQ2IsOERBQUNFO2dEQUFHRixXQUFVOzBEQUFzQjs7Ozs7OzBEQUNwQyw4REFBQ0s7Z0RBQUVMLFdBQVU7MERBQWdDOzs7Ozs7MERBQzdDLDhEQUFDM0QsOEVBQVlBO2dEQUNYZ0IsUUFBUUosU0FBU0ksTUFBTTtnREFDdkIrQyxVQUFVLENBQUMvQyxTQUNUSCxZQUFZO3dEQUFFLEdBQUdELFFBQVE7d0RBQUVJO29EQUFPOzs7Ozs7Ozs7Ozs7b0NBS3pDUCxnQkFBZ0IsbUJBQ2YsOERBQUNtRDt3Q0FBSUQsV0FBVTs7MERBQ2IsOERBQUNFO2dEQUFHRixXQUFVOzBEQUFzQjs7Ozs7OzBEQUNwQyw4REFBQ0s7Z0RBQUVMLFdBQVU7MERBQWdDOzs7Ozs7MERBQzdDLDhEQUFDMUQsZ0ZBQWFBO2dEQUNabUIsVUFBVVIsU0FBU1EsUUFBUTtnREFDM0IyQyxVQUFVLENBQUMzQyxXQUNUUCxZQUFZO3dEQUFFLEdBQUdELFFBQVE7d0RBQUVRO29EQUFTOzs7Ozs7Ozs7Ozs7b0NBSzNDWCxnQkFBZ0IsbUJBQ2YsOERBQUNtRDt3Q0FBSUQsV0FBVTs7MERBQ2IsOERBQUNFO2dEQUFHRixXQUFVOzBEQUFzQjs7Ozs7OzBEQUNwQyw4REFBQ0s7Z0RBQUVMLFdBQVU7MERBQWdDOzs7Ozs7MERBQzdDLDhEQUFDTTtnREFDQ0MsZUFBZXRELFNBQVNZLE9BQU87Z0RBQy9CMkMsVUFBVSxDQUFDM0M7b0RBQ1RlLFFBQVFDLEdBQUcsQ0FBQztvREFDWkQsUUFBUUMsR0FBRyxDQUFDLHFCQUFxQmhCO29EQUNqQ1gsWUFBWTt3REFBRSxHQUFHRCxRQUFRO3dEQUFFWSxTQUFTQTtvREFBZTtvREFDbkQsSUFBSWYsZ0JBQWdCTixNQUFNaUMsTUFBTSxHQUFHLEdBQUc7d0RBQ3BDQztvREFDRixPQUFPO3dEQUNMRjtvREFDRjtnREFDRjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O29CQU9UMUIsZ0JBQWdCLG1CQUNmLDhEQUFDZiwyREFBVUE7d0JBQUNpRSxXQUFVOzswQ0FDcEIsOERBQUNyRSx5REFBTUE7Z0NBQ0w4RSxTQUFRO2dDQUNSQyxTQUFTL0I7Z0NBQ1RnQyxVQUFVN0QsZ0JBQWdCOzBDQUMzQjs7Ozs7OzBDQUdELDhEQUFDbkIseURBQU1BO2dDQUNMK0UsU0FBU2xDO2dDQUNUbUMsVUFBVSxDQUFDYjswQ0FFVmhELGdCQUFnQk4sTUFBTWlDLE1BQU0sR0FBRyxJQUFJLHFCQUFxQjs7Ozs7Ozs7Ozs7O29CQUk5RDNCLGdCQUFnQixtQkFDZiw4REFBQ2YsMkRBQVVBO3dCQUFDaUUsV0FBVTs7MENBQ3BCLDhEQUFDckUseURBQU1BO2dDQUNMOEUsU0FBUTtnQ0FDUkMsU0FBUy9COzBDQUNWOzs7Ozs7MENBR0QsOERBQUNzQjtnQ0FBSUQsV0FBVTswQ0FBZ0M7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFRckQsOERBQUNDO2dCQUFJRCxXQUFVOztrQ0FDYiw4REFBQ0M7d0JBQUlELFdBQVU7OzBDQUNiLDhEQUFDekQsbUVBQWVBO2dDQUFDeUQsV0FBVTs7Ozs7OzBDQUMzQiw4REFBQ0U7Z0NBQUdGLFdBQVU7MENBQXNCOzs7Ozs7Ozs7Ozs7a0NBRXRDLDhEQUFDSzt3QkFBRUwsV0FBVTtrQ0FBcUM7Ozs7OztrQ0FHbEQsOERBQUNZO3dCQUFLWixXQUFVO2tDQUE4Qzs7Ozs7O2tDQUc5RCw4REFBQ0s7d0JBQUVMLFdBQVU7a0NBQWdDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFNckQ7R0EzUndCcEQ7O1FBQ1BsQixzREFBU0E7OztLQURGa0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2FwcC9vbmJvYXJkaW5nL3BhZ2UudHN4P2Y0YWMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCJcclxuXHJcbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiXHJcbmltcG9ydCB7IHVzZVJvdXRlciB9IGZyb20gXCJuZXh0L25hdmlnYXRpb25cIlxyXG5pbXBvcnQgeyBCdXR0b24gfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL2J1dHRvblwiXHJcbmltcG9ydCB7XHJcbiAgQ2FyZCxcclxuICBDYXJkQ29udGVudCxcclxuICBDYXJkRGVzY3JpcHRpb24sXHJcbiAgQ2FyZEZvb3RlcixcclxuICBDYXJkSGVhZGVyLFxyXG4gIENhcmRUaXRsZSxcclxufSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL2NhcmRcIlxyXG5pbXBvcnQgeyBTdGVwcyB9IGZyb20gXCJAL2NvbXBvbmVudHMvY3JlYXRlL3N0ZXBzXCJcclxuaW1wb3J0IHsgUHJvZmVzc2lvblNlbGVjdG9yIH0gZnJvbSBcIkAvY29tcG9uZW50cy9vbmJvYXJkaW5nL3Byb2Zlc3Npb24tc2VsZWN0b3JcIlxyXG5pbXBvcnQgeyBUZW1wbGF0ZVNlbGVjdG9yIH0gZnJvbSBcIkAvY29tcG9uZW50cy9vbmJvYXJkaW5nL3RlbXBsYXRlLXNlbGVjdG9yXCJcclxuaW1wb3J0IHsgQ29sb3JQYWxldHRlIH0gZnJvbSBcIkAvY29tcG9uZW50cy9vbmJvYXJkaW5nL2NvbG9yLXBhbGV0dGVcIlxyXG5pbXBvcnQgeyBTZWN0aW9uQ29uZmlnIH0gZnJvbSBcIkAvY29tcG9uZW50cy9vbmJvYXJkaW5nL3NlY3Rpb24tY29uZmlnXCJcclxuaW1wb3J0IHsgQ29udGVudEZvcm0gfSBmcm9tIFwiQC9jb21wb25lbnRzL29uYm9hcmRpbmcvY29udGVudC1mb3JtXCJcclxuaW1wb3J0IHsgSW5mb0NpcmNsZWRJY29uIH0gZnJvbSBcIkByYWRpeC11aS9yZWFjdC1pY29uc1wiXHJcblxyXG5jb25zdCBzdGVwcyA9IFtcclxuICB7XHJcbiAgICBpZDogXCJwcm9mZXNzaW9uXCIsXHJcbiAgICB0aXRsZTogXCJQcm9mZXNzaW9uXCIsXHJcbiAgICBkZXNjcmlwdGlvbjogXCJTZWxlY3QgeW91ciBwcm9mZXNzaW9uXCJcclxuICB9LFxyXG4gIHtcclxuICAgIGlkOiBcInRlbXBsYXRlXCIsXHJcbiAgICB0aXRsZTogXCJUZW1wbGF0ZVwiLFxyXG4gICAgZGVzY3JpcHRpb246IFwiQ2hvb3NlIGEgdGVtcGxhdGVcIlxyXG4gIH0sXHJcbiAge1xyXG4gICAgaWQ6IFwiY29sb3JzXCIsXHJcbiAgICB0aXRsZTogXCJDb2xvcnNcIixcclxuICAgIGRlc2NyaXB0aW9uOiBcIlBpY2sgeW91ciBicmFuZCBjb2xvcnNcIlxyXG4gIH0sXHJcbiAge1xyXG4gICAgaWQ6IFwic2VjdGlvbnNcIixcclxuICAgIHRpdGxlOiBcIlNlY3Rpb25zXCIsXHJcbiAgICBkZXNjcmlwdGlvbjogXCJDdXN0b21pemUgc2VjdGlvbnNcIlxyXG4gIH0sXHJcbiAge1xyXG4gICAgaWQ6IFwiY29udGVudFwiLFxyXG4gICAgdGl0bGU6IFwiQ29udGVudFwiLFxyXG4gICAgZGVzY3JpcHRpb246IFwiQWRkIHlvdXIgY29udGVudFwiXHJcbiAgfVxyXG5dXHJcblxyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBPbmJvYXJkaW5nUGFnZSgpIHtcclxuICBjb25zdCByb3V0ZXIgPSB1c2VSb3V0ZXIoKVxyXG4gIGNvbnN0IFtjdXJyZW50U3RlcCwgc2V0Q3VycmVudFN0ZXBdID0gUmVhY3QudXNlU3RhdGUoMClcclxuICBjb25zdCBbZm9ybURhdGEsIHNldEZvcm1EYXRhXSA9IFJlYWN0LnVzZVN0YXRlKHtcclxuICAgIHByb2Zlc3Npb246IFwiXCIsXHJcbiAgICB0ZW1wbGF0ZTogXCJcIixcclxuICAgIGNvbG9yczoge1xyXG4gICAgICBwcmltYXJ5OiBcIiMwMDcwZjNcIixcclxuICAgICAgc2Vjb25kYXJ5OiBcIiM2YjcyODBcIixcclxuICAgICAgYWNjZW50OiBcIiNmNTllMGJcIixcclxuICAgIH0sXHJcbiAgICBzZWN0aW9uczogW1xyXG4gICAgICB7XHJcbiAgICAgICAgaWQ6IFwiaGVyb1wiLFxyXG4gICAgICAgIG5hbWU6IFwiSGVyb1wiLFxyXG4gICAgICAgIGRlc2NyaXB0aW9uOiBcIkludHJvZHVjdGlvbiBhbmQgbWFpbiBoZWFkbGluZVwiLFxyXG4gICAgICAgIGlzUmVxdWlyZWQ6IHRydWUsXHJcbiAgICAgICAgaXNFbmFibGVkOiB0cnVlLFxyXG4gICAgICB9LFxyXG4gICAgICB7XHJcbiAgICAgICAgaWQ6IFwiYWJvdXRcIixcclxuICAgICAgICBuYW1lOiBcIkFib3V0XCIsXHJcbiAgICAgICAgZGVzY3JpcHRpb246IFwiUGVyc29uYWwgYmlvIGFuZCBiYWNrZ3JvdW5kXCIsXHJcbiAgICAgICAgaXNSZXF1aXJlZDogdHJ1ZSxcclxuICAgICAgICBpc0VuYWJsZWQ6IHRydWUsXHJcbiAgICAgIH0sXHJcbiAgICAgIHtcclxuICAgICAgICBpZDogXCJleHBlcmllbmNlXCIsXHJcbiAgICAgICAgbmFtZTogXCJFeHBlcmllbmNlXCIsXHJcbiAgICAgICAgZGVzY3JpcHRpb246IFwiV29yayBoaXN0b3J5IGFuZCBhY2hpZXZlbWVudHNcIixcclxuICAgICAgICBpc0VuYWJsZWQ6IHRydWUsXHJcbiAgICAgIH0sXHJcbiAgICAgIHtcclxuICAgICAgICBpZDogXCJwcm9qZWN0c1wiLFxyXG4gICAgICAgIG5hbWU6IFwiUHJvamVjdHNcIixcclxuICAgICAgICBkZXNjcmlwdGlvbjogXCJTaG93Y2FzZSBvZiB5b3VyIHdvcmtcIixcclxuICAgICAgICBpc0VuYWJsZWQ6IHRydWUsXHJcbiAgICAgIH0sXHJcbiAgICAgIHtcclxuICAgICAgICBpZDogXCJza2lsbHNcIixcclxuICAgICAgICBuYW1lOiBcIlNraWxsc1wiLFxyXG4gICAgICAgIGRlc2NyaXB0aW9uOiBcIlRlY2huaWNhbCBhbmQgcHJvZmVzc2lvbmFsIHNraWxsc1wiLFxyXG4gICAgICAgIGlzRW5hYmxlZDogdHJ1ZSxcclxuICAgICAgfSxcclxuICAgICAge1xyXG4gICAgICAgIGlkOiBcInRlc3RpbW9uaWFsc1wiLFxyXG4gICAgICAgIG5hbWU6IFwiVGVzdGltb25pYWxzXCIsXHJcbiAgICAgICAgZGVzY3JpcHRpb246IFwiQ2xpZW50IGFuZCBjb2xsZWFndWUgcmV2aWV3c1wiLFxyXG4gICAgICAgIGlzRW5hYmxlZDogZmFsc2UsXHJcbiAgICAgIH0sXHJcbiAgICAgIHtcclxuICAgICAgICBpZDogXCJibG9nXCIsXHJcbiAgICAgICAgbmFtZTogXCJCbG9nXCIsXHJcbiAgICAgICAgZGVzY3JpcHRpb246IFwiQXJ0aWNsZXMgYW5kIHRob3VnaHRzXCIsXHJcbiAgICAgICAgaXNFbmFibGVkOiBmYWxzZSxcclxuICAgICAgfSxcclxuICAgICAge1xyXG4gICAgICAgIGlkOiBcImNvbnRhY3RcIixcclxuICAgICAgICBuYW1lOiBcIkNvbnRhY3RcIixcclxuICAgICAgICBkZXNjcmlwdGlvbjogXCJDb250YWN0IGluZm9ybWF0aW9uIGFuZCBmb3JtXCIsXHJcbiAgICAgICAgaXNSZXF1aXJlZDogdHJ1ZSxcclxuICAgICAgICBpc0VuYWJsZWQ6IHRydWUsXHJcbiAgICAgIH0sXHJcbiAgICBdLFxyXG4gICAgY29udGVudDoge1xyXG4gICAgICBuYW1lOiBcIlwiLFxyXG4gICAgICB0aXRsZTogXCJcIixcclxuICAgICAgYmlvOiBcIlwiLFxyXG4gICAgICBza2lsbHM6IFtdLFxyXG4gICAgICBleHBlcmllbmNlOiBbXSxcclxuICAgICAgcHJvamVjdHM6IFtdLFxyXG4gICAgICBlbWFpbDogXCJcIixcclxuICAgICAgcGhvbmU6IFwiXCIsXHJcbiAgICAgIHNvY2lhbDoge1xyXG4gICAgICAgIGxpbmtlZGluOiBcIlwiLFxyXG4gICAgICAgIGdpdGh1YjogXCJcIixcclxuICAgICAgICB0d2l0dGVyOiBcIlwiLFxyXG4gICAgICB9LFxyXG4gICAgfVxyXG4gIH0pXHJcblxyXG4gIGNvbnN0IGhhbmRsZU5leHQgPSAoKSA9PiB7XHJcbiAgICBpZiAoY3VycmVudFN0ZXAgPCBzdGVwcy5sZW5ndGggLSAxKSB7XHJcbiAgICAgIHNldEN1cnJlbnRTdGVwKGN1cnJlbnRTdGVwICsgMSk7XHJcbiAgICB9IGVsc2Uge1xyXG4gICAgICBoYW5kbGVTdWJtaXQoKTtcclxuICAgIH1cclxuICB9XHJcblxyXG4gIGNvbnN0IGhhbmRsZUJhY2sgPSAoKSA9PiB7XHJcbiAgICBpZiAoY3VycmVudFN0ZXAgPiAwKSB7XHJcbiAgICAgIHNldEN1cnJlbnRTdGVwKGN1cnJlbnRTdGVwIC0gMSlcclxuICAgIH1cclxuICB9XHJcblxyXG4gIGNvbnN0IGhhbmRsZVN1Ym1pdCA9IGFzeW5jICgpID0+IHtcclxuICAgIHRyeSB7XHJcbiAgICAgIGNvbnNvbGUubG9nKFwiU3RhcnRpbmcgcG9ydGZvbGlvIGNyZWF0aW9uLi4uXCIpO1xyXG4gICAgICBjb25zb2xlLmxvZyhcIkZvcm0gZGF0YTpcIiwgZm9ybURhdGEpO1xyXG5cclxuICAgICAgLy8gQ2FsbCB0aGUgcG9ydGZvbGlvIGdlbmVyYXRpb24gQVBJXHJcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJy9hcGkvcG9ydGZvbGlvL2dlbmVyYXRlJywge1xyXG4gICAgICAgIG1ldGhvZDogJ1BPU1QnLFxyXG4gICAgICAgIGhlYWRlcnM6IHtcclxuICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXHJcbiAgICAgICAgfSxcclxuICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh7XHJcbiAgICAgICAgICBwcm9mZXNzaW9uOiBmb3JtRGF0YS5wcm9mZXNzaW9uLFxyXG4gICAgICAgICAgdGVtcGxhdGU6IGZvcm1EYXRhLnRlbXBsYXRlLFxyXG4gICAgICAgICAgY29sb3JzOiBmb3JtRGF0YS5jb2xvcnMsXHJcbiAgICAgICAgICBzZWN0aW9uczogZm9ybURhdGEuc2VjdGlvbnMsXHJcbiAgICAgICAgICBjb250ZW50OiBmb3JtRGF0YS5jb250ZW50XHJcbiAgICAgICAgfSksXHJcbiAgICAgIH0pO1xyXG5cclxuICAgICAgY29uc29sZS5sb2coXCJBUEkgcmVzcG9uc2Ugc3RhdHVzOlwiLCByZXNwb25zZS5zdGF0dXMpO1xyXG4gICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xyXG4gICAgICBjb25zb2xlLmxvZyhcIkFQSSByZXNwb25zZSBkYXRhOlwiLCBkYXRhKTtcclxuXHJcbiAgICAgIGlmIChkYXRhLnN1Y2Nlc3MpIHtcclxuICAgICAgICBjb25zb2xlLmxvZyhcIlBvcnRmb2xpbyBjcmVhdGVkIHN1Y2Nlc3NmdWxseSwgcmVkaXJlY3RpbmcgdG86XCIsIGRhdGEucG9ydGZvbGlvVXJsKTtcclxuICAgICAgICAvLyBSZWRpcmVjdCB0byB0aGUgZ2VuZXJhdGVkIHBvcnRmb2xpb1xyXG4gICAgICAgIHJvdXRlci5wdXNoKGRhdGEucG9ydGZvbGlvVXJsKTtcclxuICAgICAgfSBlbHNlIHtcclxuICAgICAgICBjb25zb2xlLmVycm9yKFwiRXJyb3IgZ2VuZXJhdGluZyBwb3J0Zm9saW86XCIsIGRhdGEuZXJyb3IpO1xyXG4gICAgICAgIGFsZXJ0KFwiRXJyb3IgY3JlYXRpbmcgcG9ydGZvbGlvOiBcIiArIChkYXRhLmVycm9yIHx8IFwiVW5rbm93biBlcnJvclwiKSk7XHJcbiAgICAgIH1cclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciBzYXZpbmcgb25ib2FyZGluZyBkYXRhOlwiLCBlcnJvcik7XHJcbiAgICAgIGFsZXJ0KFwiRXJyb3IgY3JlYXRpbmcgcG9ydGZvbGlvOiBcIiArIGVycm9yLm1lc3NhZ2UpO1xyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgY29uc3QgaXNTdGVwVmFsaWQgPSAoKSA9PiB7XHJcbiAgICBzd2l0Y2ggKGN1cnJlbnRTdGVwKSB7XHJcbiAgICAgIGNhc2UgMDpcclxuICAgICAgICByZXR1cm4gISFmb3JtRGF0YS5wcm9mZXNzaW9uXHJcbiAgICAgIGNhc2UgMTpcclxuICAgICAgICByZXR1cm4gISFmb3JtRGF0YS50ZW1wbGF0ZVxyXG4gICAgICBjYXNlIDI6XHJcbiAgICAgICAgcmV0dXJuICEhZm9ybURhdGEuY29sb3JzLnByaW1hcnlcclxuICAgICAgY2FzZSAzOlxyXG4gICAgICAgIHJldHVybiBmb3JtRGF0YS5zZWN0aW9ucy5sZW5ndGggPiAwXHJcbiAgICAgIGNhc2UgNDpcclxuICAgICAgICByZXR1cm4gISFmb3JtRGF0YS5jb250ZW50Lm5hbWVcclxuICAgICAgZGVmYXVsdDpcclxuICAgICAgICByZXR1cm4gZmFsc2VcclxuICAgIH1cclxuICB9XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8bWFpbiBjbGFzc05hbWU9XCJjb250YWluZXIgbWF4LXctNHhsIHB5LThcIj5cclxuICAgICAgPENhcmQgY2xhc3NOYW1lPVwidy1mdWxsXCI+XHJcbiAgICAgICAgPENhcmRIZWFkZXI+XHJcbiAgICAgICAgICA8Q2FyZFRpdGxlPkNyZWF0ZSBZb3VyIFBvcnRmb2xpbzwvQ2FyZFRpdGxlPlxyXG4gICAgICAgICAgPENhcmREZXNjcmlwdGlvbj5cclxuICAgICAgICAgICAgRm9sbG93IHRoZXNlIHN0ZXBzIHRvIGNyZWF0ZSB5b3VyIHBlcnNvbmFsaXplZCBwb3J0Zm9saW8gd2Vic2l0ZVxyXG4gICAgICAgICAgPC9DYXJkRGVzY3JpcHRpb24+XHJcbiAgICAgICAgPC9DYXJkSGVhZGVyPlxyXG4gICAgICAgIDxDYXJkQ29udGVudCBjbGFzc05hbWU9XCJzcGFjZS15LThcIj5cclxuICAgICAgICAgIDxTdGVwcyBzdGVwcz17c3RlcHN9IGN1cnJlbnRTdGVwPXtjdXJyZW50U3RlcH0gLz5cclxuICAgICAgICAgIFxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC04XCI+XHJcbiAgICAgICAgICAgIHsvKiBTdGVwIGNvbnRlbnQgd2lsbCBiZSBhZGRlZCBoZXJlICovfVxyXG4gICAgICAgICAgICB7Y3VycmVudFN0ZXAgPT09IDAgJiYgKFxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XHJcbiAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LW1lZGl1bVwiPldoYXQmYXBvcztzIHlvdXIgcHJvZmVzc2lvbj88L2gzPlxyXG4gICAgICAgICAgICAgICAgPFByb2Zlc3Npb25TZWxlY3RvclxyXG4gICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEucHJvZmVzc2lvbn1cclxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyh2YWx1ZSkgPT5cclxuICAgICAgICAgICAgICAgICAgICBzZXRGb3JtRGF0YSh7IC4uLmZvcm1EYXRhLCBwcm9mZXNzaW9uOiB2YWx1ZSB9KVxyXG4gICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICApfVxyXG4gICAgICAgICAgICB7Y3VycmVudFN0ZXAgPT09IDEgJiYgKFxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XHJcbiAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LW1lZGl1bVwiPkNob29zZSBhIHRlbXBsYXRlIGZvciB5b3VyIHBvcnRmb2xpbzwvaDM+XHJcbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPlNlbGVjdCBhIHRlbXBsYXRlIHRoYXQgYmVzdCByZXByZXNlbnRzIHlvdXIgcHJvZmVzc2lvbmFsIHN0eWxlLjwvcD5cclxuICAgICAgICAgICAgICAgIDxUZW1wbGF0ZVNlbGVjdG9yXHJcbiAgICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS50ZW1wbGF0ZX1cclxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyh2YWx1ZSkgPT5cclxuICAgICAgICAgICAgICAgICAgICBzZXRGb3JtRGF0YSh7IC4uLmZvcm1EYXRhLCB0ZW1wbGF0ZTogdmFsdWUgfSlcclxuICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAge2N1cnJlbnRTdGVwID09PSAyICYmIChcclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxyXG4gICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1tZWRpdW1cIj5DaG9vc2UgeW91ciBicmFuZCBjb2xvcnM8L2gzPlxyXG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LW11dGVkLWZvcmVncm91bmRcIj5TZWxlY3QgYSBjb2xvciBwYWxldHRlIG9yIGN1c3RvbWl6ZSB5b3VyIG93biBjb2xvcnMuPC9wPlxyXG4gICAgICAgICAgICAgICAgPENvbG9yUGFsZXR0ZVxyXG4gICAgICAgICAgICAgICAgICBjb2xvcnM9e2Zvcm1EYXRhLmNvbG9yc31cclxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhjb2xvcnMpID0+XHJcbiAgICAgICAgICAgICAgICAgICAgc2V0Rm9ybURhdGEoeyAuLi5mb3JtRGF0YSwgY29sb3JzIH0pXHJcbiAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgIHtjdXJyZW50U3RlcCA9PT0gMyAmJiAoXHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cclxuICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtbWVkaXVtXCI+Q3VzdG9taXplIHlvdXIgcG9ydGZvbGlvIHNlY3Rpb25zPC9oMz5cclxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+U2VsZWN0IGFuZCBhcnJhbmdlIHRoZSBzZWN0aW9ucyB5b3Ugd2FudCB0byBpbmNsdWRlLjwvcD5cclxuICAgICAgICAgICAgICAgIDxTZWN0aW9uQ29uZmlnXHJcbiAgICAgICAgICAgICAgICAgIHNlY3Rpb25zPXtmb3JtRGF0YS5zZWN0aW9uc31cclxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhzZWN0aW9ucykgPT5cclxuICAgICAgICAgICAgICAgICAgICBzZXRGb3JtRGF0YSh7IC4uLmZvcm1EYXRhLCBzZWN0aW9ucyB9KVxyXG4gICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICApfVxyXG4gICAgICAgICAgICB7Y3VycmVudFN0ZXAgPT09IDQgJiYgKFxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XHJcbiAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LW1lZGl1bVwiPkFkZCB5b3VyIHBlcnNvbmFsIGluZm9ybWF0aW9uPC9oMz5cclxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+RmlsbCBpbiB5b3VyIGRldGFpbHMgdG8gcGVyc29uYWxpemUgeW91ciBwb3J0Zm9saW8uPC9wPlxyXG4gICAgICAgICAgICAgICAgPFNpbXBsZUNvbnRlbnRGb3JtXHJcbiAgICAgICAgICAgICAgICAgIGRlZmF1bHRWYWx1ZXM9e2Zvcm1EYXRhLmNvbnRlbnQgYXMgYW55fVxyXG4gICAgICAgICAgICAgICAgICBvblN1Ym1pdD17KGNvbnRlbnQpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICBjb25zb2xlLmxvZyhcIj09PSBPTkJPQVJESU5HIFJFQ0VJVkVEIENPTlRFTlQgPT09XCIpO1xyXG4gICAgICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKFwiQ29udGVudCByZWNlaXZlZDpcIiwgY29udGVudCk7XHJcbiAgICAgICAgICAgICAgICAgICAgc2V0Rm9ybURhdGEoeyAuLi5mb3JtRGF0YSwgY29udGVudDogY29udGVudCBhcyBhbnkgfSk7XHJcbiAgICAgICAgICAgICAgICAgICAgaWYgKGN1cnJlbnRTdGVwID09PSBzdGVwcy5sZW5ndGggLSAxKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICBoYW5kbGVTdWJtaXQoKTtcclxuICAgICAgICAgICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgICAgICAgICAgaGFuZGxlTmV4dCgpO1xyXG4gICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgIHsvKiBBZGRpdGlvbmFsIHN0ZXAgY29udGVudCB3aWxsIGJlIGFkZGVkIGZvciBvdGhlciBzdGVwcyAqL31cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgIDwvQ2FyZENvbnRlbnQ+XHJcbiAgICAgICAge2N1cnJlbnRTdGVwICE9PSA0ICYmIChcclxuICAgICAgICAgIDxDYXJkRm9vdGVyIGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuXCI+XHJcbiAgICAgICAgICAgIDxCdXR0b25cclxuICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZVwiXHJcbiAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlQmFja31cclxuICAgICAgICAgICAgICBkaXNhYmxlZD17Y3VycmVudFN0ZXAgPT09IDB9XHJcbiAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICBCYWNrXHJcbiAgICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgICA8QnV0dG9uXHJcbiAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlTmV4dH1cclxuICAgICAgICAgICAgICBkaXNhYmxlZD17IWlzU3RlcFZhbGlkKCl9XHJcbiAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICB7Y3VycmVudFN0ZXAgPT09IHN0ZXBzLmxlbmd0aCAtIDEgPyBcIkNyZWF0ZSBQb3J0Zm9saW9cIiA6IFwiTmV4dFwifVxyXG4gICAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgIDwvQ2FyZEZvb3Rlcj5cclxuICAgICAgICApfVxyXG4gICAgICAgIHtjdXJyZW50U3RlcCA9PT0gNCAmJiAoXHJcbiAgICAgICAgICA8Q2FyZEZvb3RlciBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlblwiPlxyXG4gICAgICAgICAgICA8QnV0dG9uXHJcbiAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxyXG4gICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZUJhY2t9XHJcbiAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICBCYWNrXHJcbiAgICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+XHJcbiAgICAgICAgICAgICAgQ2xpY2sgXCJDcmVhdGUgUG9ydGZvbGlvXCIgYnV0dG9uIGluIHRoZSBmb3JtIGFib3ZlIHRvIGdlbmVyYXRlIHlvdXIgcG9ydGZvbGlvXHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgPC9DYXJkRm9vdGVyPlxyXG4gICAgICAgICl9XHJcbiAgICAgIDwvQ2FyZD5cclxuICAgICAgXHJcbiAgICAgIHsvKiBBZG1pbiBJbmZvIFNlY3Rpb24gKi99XHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtOCBwLTQgYm9yZGVyIHJvdW5kZWQtbGcgYmctc2xhdGUtNTBcIj5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yIG1iLTJcIj5cclxuICAgICAgICAgIDxJbmZvQ2lyY2xlZEljb24gY2xhc3NOYW1lPVwiaC01IHctNSB0ZXh0LWJsdWUtNTAwXCIgLz5cclxuICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LW1kIGZvbnQtbWVkaXVtXCI+QWRtaW4gSW5mb3JtYXRpb248L2gzPlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1tdXRlZC1mb3JlZ3JvdW5kIG1iLTJcIj5cclxuICAgICAgICAgIEFzIGFuIGFkbWluLCB5b3UgY2FuIHVwbG9hZCBhbmQgbWFuYWdlIHRlbXBsYXRlcyBhdDpcclxuICAgICAgICA8L3A+XHJcbiAgICAgICAgPGNvZGUgY2xhc3NOYW1lPVwiYmctc2xhdGUtMjAwIHAtMiByb3VuZGVkIHRleHQtc20gYmxvY2sgbWItMlwiPlxyXG4gICAgICAgICAgL3RlbXBsYXRlc1xyXG4gICAgICAgIDwvY29kZT5cclxuICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPlxyXG4gICAgICAgICAgVGhpcyBwYWdlIGFsbG93cyB5b3UgdG8gdXBsb2FkIEVudmF0byBFbGVtZW50cyB0ZW1wbGF0ZXMgYW5kIGNvbmZpZ3VyZSB0aGVtIGZvciB1c2Ugd2l0aCB0aGUgcG9ydGZvbGlvIGdlbmVyYXRvci5cclxuICAgICAgICA8L3A+XHJcbiAgICAgIDwvZGl2PlxyXG4gICAgPC9tYWluPlxyXG4gIClcclxufSAiXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VSb3V0ZXIiLCJCdXR0b24iLCJDYXJkIiwiQ2FyZENvbnRlbnQiLCJDYXJkRGVzY3JpcHRpb24iLCJDYXJkRm9vdGVyIiwiQ2FyZEhlYWRlciIsIkNhcmRUaXRsZSIsIlN0ZXBzIiwiUHJvZmVzc2lvblNlbGVjdG9yIiwiVGVtcGxhdGVTZWxlY3RvciIsIkNvbG9yUGFsZXR0ZSIsIlNlY3Rpb25Db25maWciLCJJbmZvQ2lyY2xlZEljb24iLCJzdGVwcyIsImlkIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsIk9uYm9hcmRpbmdQYWdlIiwicm91dGVyIiwiY3VycmVudFN0ZXAiLCJzZXRDdXJyZW50U3RlcCIsInVzZVN0YXRlIiwiZm9ybURhdGEiLCJzZXRGb3JtRGF0YSIsInByb2Zlc3Npb24iLCJ0ZW1wbGF0ZSIsImNvbG9ycyIsInByaW1hcnkiLCJzZWNvbmRhcnkiLCJhY2NlbnQiLCJzZWN0aW9ucyIsIm5hbWUiLCJpc1JlcXVpcmVkIiwiaXNFbmFibGVkIiwiY29udGVudCIsImJpbyIsInNraWxscyIsImV4cGVyaWVuY2UiLCJwcm9qZWN0cyIsImVtYWlsIiwicGhvbmUiLCJzb2NpYWwiLCJsaW5rZWRpbiIsImdpdGh1YiIsInR3aXR0ZXIiLCJoYW5kbGVOZXh0IiwibGVuZ3RoIiwiaGFuZGxlU3VibWl0IiwiaGFuZGxlQmFjayIsImNvbnNvbGUiLCJsb2ciLCJyZXNwb25zZSIsImZldGNoIiwibWV0aG9kIiwiaGVhZGVycyIsImJvZHkiLCJKU09OIiwic3RyaW5naWZ5Iiwic3RhdHVzIiwiZGF0YSIsImpzb24iLCJzdWNjZXNzIiwicG9ydGZvbGlvVXJsIiwicHVzaCIsImVycm9yIiwiYWxlcnQiLCJtZXNzYWdlIiwiaXNTdGVwVmFsaWQiLCJtYWluIiwiY2xhc3NOYW1lIiwiZGl2IiwiaDMiLCJ2YWx1ZSIsIm9uQ2hhbmdlIiwicCIsIlNpbXBsZUNvbnRlbnRGb3JtIiwiZGVmYXVsdFZhbHVlcyIsIm9uU3VibWl0IiwidmFyaWFudCIsIm9uQ2xpY2siLCJkaXNhYmxlZCIsImNvZGUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/onboarding/page.tsx\n"));

/***/ })

});