"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Card, CardContent } from "@/components/ui/card"
import { Plus, Trash2 } from "lucide-react"

interface ContentFormValues {
  name: string
  title: string
  bio: string
  skills: string[]
  experience: Array<{
    title: string
    company: string
    startDate: string
    endDate: string
    description: string
  }>
  projects: Array<{
    title: string
    description: string
    link: string
  }>
  email: string
  phone: string
  social: {
    linkedin: string
    github: string
    twitter: string
  }
}

interface ContentFormProps {
  defaultValues?: Partial<ContentFormValues>
  onSubmit: (values: ContentFormValues) => void
}

export function SimpleContentForm({ defaultValues, onSubmit }: ContentFormProps) {
  // Simple state management
  const [formData, setFormData] = useState<ContentFormValues>({
    name: defaultValues?.name || "",
    title: defaultValues?.title || "",
    bio: defaultValues?.bio || "",
    skills: defaultValues?.skills || [],
    experience: defaultValues?.experience || [],
    projects: defaultValues?.projects || [],
    email: defaultValues?.email || "",
    phone: defaultValues?.phone || "",
    social: {
      linkedin: defaultValues?.social?.linkedin || "",
      github: defaultValues?.social?.github || "",
      twitter: defaultValues?.social?.twitter || "",
    },
  })

  const [newSkill, setNewSkill] = useState("")

  const handleInputChange = (field: keyof ContentFormValues, value: any) => {
    console.log(`Updating ${field} with value:`, value)
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleSocialChange = (platform: keyof ContentFormValues['social'], value: string) => {
    console.log(`Updating social.${platform} with value:`, value)
    setFormData(prev => ({
      ...prev,
      social: {
        ...prev.social,
        [platform]: value
      }
    }))
  }

  const addSkill = () => {
    if (newSkill.trim()) {
      console.log("Adding skill:", newSkill)
      setFormData(prev => ({
        ...prev,
        skills: [...prev.skills, newSkill.trim()]
      }))
      setNewSkill("")
    }
  }

  const removeSkill = (index: number) => {
    console.log("Removing skill at index:", index)
    setFormData(prev => ({
      ...prev,
      skills: prev.skills.filter((_, i) => i !== index)
    }))
  }

  const handleSubmit = () => {
    console.log("=== SIMPLE FORM SUBMIT ===")
    console.log("Form data being submitted:", formData)
    console.log("Name:", formData.name)
    console.log("Title:", formData.title)
    console.log("Bio:", formData.bio)
    console.log("Skills:", formData.skills)
    console.log("Email:", formData.email)
    
    onSubmit(formData)
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardContent className="p-6">
          <div className="space-y-4">
            <div>
              <Label htmlFor="name">Full Name *</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                placeholder="Enter your full name"
              />
            </div>

            <div>
              <Label htmlFor="title">Professional Title *</Label>
              <Input
                id="title"
                value={formData.title}
                onChange={(e) => handleInputChange('title', e.target.value)}
                placeholder="e.g., Full Stack Developer"
              />
            </div>

            <div>
              <Label htmlFor="bio">Bio *</Label>
              <Textarea
                id="bio"
                value={formData.bio}
                onChange={(e) => handleInputChange('bio', e.target.value)}
                placeholder="Tell us about yourself..."
                rows={4}
              />
            </div>

            <div>
              <Label>Skills *</Label>
              <div className="flex gap-2 mb-2">
                <Input
                  value={newSkill}
                  onChange={(e) => setNewSkill(e.target.value)}
                  placeholder="Add a skill"
                  onKeyPress={(e) => e.key === 'Enter' && addSkill()}
                />
                <Button type="button" onClick={addSkill}>
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
              <div className="flex flex-wrap gap-2">
                {formData.skills.map((skill, index) => (
                  <div key={index} className="flex items-center gap-1 bg-blue-100 px-2 py-1 rounded">
                    <span>{skill}</span>
                    <button
                      type="button"
                      onClick={() => removeSkill(index)}
                      className="text-red-500 hover:text-red-700"
                    >
                      <Trash2 className="h-3 w-3" />
                    </button>
                  </div>
                ))}
              </div>
            </div>

            <div>
              <Label htmlFor="email">Email *</Label>
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                placeholder="<EMAIL>"
              />
            </div>

            <div>
              <Label htmlFor="phone">Phone</Label>
              <Input
                id="phone"
                value={formData.phone}
                onChange={(e) => handleInputChange('phone', e.target.value)}
                placeholder="+****************"
              />
            </div>

            <div>
              <Label>Social Links</Label>
              <div className="space-y-2">
                <Input
                  value={formData.social.linkedin}
                  onChange={(e) => handleSocialChange('linkedin', e.target.value)}
                  placeholder="LinkedIn URL"
                />
                <Input
                  value={formData.social.github}
                  onChange={(e) => handleSocialChange('github', e.target.value)}
                  placeholder="GitHub URL"
                />
                <Input
                  value={formData.social.twitter}
                  onChange={(e) => handleSocialChange('twitter', e.target.value)}
                  placeholder="Twitter URL"
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Button 
        onClick={handleSubmit}
        className="w-full bg-green-600 hover:bg-green-700"
        size="lg"
      >
        🚀 Create Portfolio
      </Button>
    </div>
  )
}
