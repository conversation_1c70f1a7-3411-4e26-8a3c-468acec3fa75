import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    
    if (!id) {
      return NextResponse.json(
        { error: 'Template ID is required' },
        { status: 400 }
      );
    }
    
    // Try to find the preview image in the template directory
    const templateDir = path.join(process.cwd(), 'frontend', 'templates', 'html-templates', id);
    const possibleExtensions = ['png', 'jpg', 'jpeg', 'gif', 'webp'];
    
    let previewPath = null;
    for (const ext of possibleExtensions) {
      const testPath = path.join(templateDir, `preview.${ext}`);
      if (fs.existsSync(testPath)) {
        previewPath = testPath;
        break;
      }
    }
    
    if (!previewPath) {
      // Return a placeholder SVG if no preview image exists
      const placeholderSvg = `
        <svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
          <rect width="100%" height="100%" fill="#f3f4f6"/>
          <text x="50%" y="50%" text-anchor="middle" dy=".3em" font-family="Arial, sans-serif" font-size="16" fill="#6b7280">
            ${id} Template Preview
          </text>
        </svg>
      `;
      
      return new NextResponse(placeholderSvg, {
        headers: {
          'Content-Type': 'image/svg+xml',
        },
      });
    }
    
    // Read and serve the actual image
    const imageBuffer = fs.readFileSync(previewPath);
    const ext = path.extname(previewPath).toLowerCase();
    
    let contentType = 'image/png';
    switch (ext) {
      case '.jpg':
      case '.jpeg':
        contentType = 'image/jpeg';
        break;
      case '.gif':
        contentType = 'image/gif';
        break;
      case '.webp':
        contentType = 'image/webp';
        break;
    }
    
    return new NextResponse(imageBuffer, {
      headers: {
        'Content-Type': contentType,
      },
    });
    
  } catch (error) {
    console.error('Error serving template preview:', error);
    
    // Return error placeholder
    const errorSvg = `
      <svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
        <rect width="100%" height="100%" fill="#fee2e2"/>
        <text x="50%" y="50%" text-anchor="middle" dy=".3em" font-family="Arial, sans-serif" font-size="16" fill="#dc2626">
          Preview Not Available
        </text>
      </svg>
    `;
    
    return new NextResponse(errorSvg, {
      headers: {
        'Content-Type': 'image/svg+xml',
      },
    });
  }
}
