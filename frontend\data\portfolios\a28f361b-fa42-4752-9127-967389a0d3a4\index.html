<!DOCTYPE html><html lang="en"><head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Portfolio Template</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        /* Reset and Base Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Arial', sans-serif;
        }

        body {
            background-color: #0a0a0a;
            color: #ffffff;
            line-height: 1.6;
        }

        /* Header Styles */
        header {
            padding: 20px 50px;
            position: fixed;
            width: 100%;
            top: 0;
            z-index: 1000;
            background-color: rgba(10, 10, 10, 0.95);
        }

        nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            color: #00ff84;
            font-size: 24px;
            font-weight: bold;
        }

        .nav-links {
            display: flex;
            gap: 30px;
        }

        .nav-links a {
            color: white;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s;
        }

        .nav-links a:hover {
            color: #00ff84;
        }

        /* Hero Section */
        .hero {
            min-height: 100vh;
            display: flex;
            align-items: center;
            padding: 100px 50px;
            gap: 50px;
        }

        .hero-content {
            flex: 1;
        }

        .hero h1 {
            font-size: 48px;
            margin-bottom: 20px;
        }

        .hero h1 span {
            color: #00ff84;
        }

        .hero p {
            margin-bottom: 30px;
            color: #888;
        }

        .hero-image {
            flex: 1;
        }

        .hero-image img {
            max-width: 100%;
            height: auto;
        }

        .btn {
            display: inline-block;
            padding: 12px 30px;
            background-color: #00ff84;
            color: #0a0a0a;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            transition: transform 0.3s;
        }

        .btn:hover {
            transform: translateY(-3px);
        }

        /* Failure Section */
        .failure-section {
            padding: 100px 50px;
            display: flex;
            align-items: center;
            gap: 50px;
        }

        .failure-content {
            flex: 1;
        }

        .failure-image {
            flex: 1;
        }

        .failure-image img {
            max-width: 100%;
            height: auto;
        }

        .section-title {
            font-size: 36px;
            margin-bottom: 20px;
        }

        .section-title span {
            color: #00ff84;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            header {
                padding: 20px;
            }

            .nav-links {
                display: none;
            }

            .hero, .failure-section {
                flex-direction: column;
                padding: 50px 20px;
                text-align: center;
            }

            .hero h1 {
                font-size: 36px;
            }
        }

        /* Experience and Skills Section Styles */
        .experience-section {
            padding: 100px 50px;
            text-align: center;
        }

        .section-header {
            max-width: 800px;
            margin: 0 auto 60px;
        }

        .section-description {
            color: #888;
            margin-top: 20px;
        }

        .skills-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            margin-bottom: 60px;
        }

        .skill-item {
            padding: 30px;
            background-color: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            transition: transform 0.3s;
        }

        .skill-item:hover {
            transform: translateY(-10px);
        }

        .skill-item h3 {
            color: #00ff84;
            margin-bottom: 15px;
        }

        .skill-item p {
            color: #888;
        }

        .progress-bars {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 40px;
        }

        .progress-item {
            text-align: center;
        }

        .circular-progress {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
            margin-bottom: 20px;
        }

        .progress-value {
            font-size: 24px;
            font-weight: bold;
            color: #00ff84;
        }

        .progress-item h4 {
            color: #fff;
            margin-top: 10px;
        }

        @media (max-width: 768px) {
            .experience-section {
                padding: 50px 20px;
            }

            .skills-grid {
                grid-template-columns: 1fr;
            }

            .progress-bars {
                gap: 20px;
            }

            .circular-progress {
                width: 120px;
                height: 120px;
            }
        }

        /* Services Section Styles */
        .services-section {
            padding: 100px 50px;
            background-color: rgba(255, 255, 255, 0.02);
        }

        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-top: 50px;
        }

        .service-card {
            background-color: #0a0a0a;
            padding: 40px;
            border-radius: 10px;
            text-align: center;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .service-card.active,
        .service-card:hover {
            background-color: #00ff84;
            transform: translateY(-10px);
        }

        .service-card.active *,
        .service-card:hover * {
            color: #0a0a0a;
        }

        .service-icon {
            font-size: 40px;
            color: #00ff84;
            margin-bottom: 20px;
        }

        .service-card h3 {
            margin-bottom: 15px;
            color: #ffffff;
        }

        .service-card p {
            color: #888;
            margin-bottom: 25px;
        }

        .service-btn {
            display: inline-block;
            padding: 10px 25px;
            background-color: transparent;
            border: 2px solid #00ff84;
            color: #00ff84;
            text-decoration: none;
            border-radius: 5px;
            transition: all 0.3s ease;
        }

        .service-card.active .service-btn,
        .service-card:hover .service-btn {
            background-color: #0a0a0a;
            border-color: #0a0a0a;
            color: #00ff84;
        }

        @media (max-width: 768px) {
            .services-section {
                padding: 50px 20px;
            }

            .services-grid {
                grid-template-columns: 1fr;
            }
        }

        /* Projects Section Styles */
        .projects-section {
            padding: 100px 50px;
            position: relative;
        }

        .projects-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-top: 50px;
        }

        .project-card {
            position: relative;
            border-radius: 10px;
            overflow: hidden;
            aspect-ratio: 4/3;
        }

        .project-card img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .project-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 255, 132, 0.9);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .project-card:hover .project-overlay {
            opacity: 1;
        }

        .project-card:hover img {
            transform: scale(1.1);
        }

        .project-overlay h3 {
            color: #0a0a0a;
            margin-bottom: 10px;
        }

        .project-overlay p {
            color: #0a0a0a;
            margin-bottom: 20px;
        }

        .project-link {
            width: 40px;
            height: 40px;
            background: #0a0a0a;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            color: #00ff84;
            text-decoration: none;
            transition: transform 0.3s ease;
        }

        .project-link:hover {
            transform: scale(1.1);
        }

        .project-navigation {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 40px;
        }

        .nav-btn {
            width: 40px;
            height: 40px;
            border: none;
            background: rgba(255, 255, 255, 0.1);
            color: #00ff84;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .nav-btn:hover {
            background: #00ff84;
            color: #0a0a0a;
        }

        @media (max-width: 768px) {
            .projects-section {
                padding: 50px 20px;
            }

            .projects-grid {
                grid-template-columns: 1fr;
            }
        }

        /* Clients Section Styles */
        .clients-section {
            padding: 100px 50px;
            background-color: rgba(255, 255, 255, 0.02);
        }

        .testimonial-container {
            max-width: 800px;
            margin: 50px auto;
        }

        .testimonial {
            display: flex;
            align-items: center;
            gap: 30px;
            padding: 30px;
            background-color: #0a0a0a;
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .client-image {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            overflow: hidden;
        }

        .client-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .testimonial-content {
            flex: 1;
        }

        .testimonial-text {
            color: #888;
            font-style: italic;
            margin-bottom: 20px;
        }

        .client-info h4 {
            color: #00ff84;
            margin-bottom: 5px;
        }

        .client-info p {
            color: #888;
            font-size: 0.9em;
        }

        .testimonial-dots {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-top: 30px;
        }

        .dot {
            width: 10px;
            height: 10px;
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }

        .dot.active {
            background-color: #00ff84;
        }

        /* Payment Section Styles */
        .payment-section {
            padding: 100px 50px;
        }

        .pricing-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            margin-top: 50px;
        }

        .pricing-card {
            background-color: #0a0a0a;
            padding: 40px;
            border-radius: 10px;
            text-align: center;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .pricing-card.featured {
            background-color: #00ff84;
            transform: translateY(-20px);
        }

        .pricing-card.featured * {
            color: #0a0a0a;
        }

        .pricing-icon {
            font-size: 40px;
            color: #00ff84;
            margin-bottom: 20px;
        }

        .pricing-card h3 {
            margin-bottom: 20px;
            color: #ffffff;
        }

        .price {
            font-size: 36px;
            font-weight: bold;
            color: #00ff84;
            margin-bottom: 10px;
        }

        .period {
            color: #888;
            margin-bottom: 30px;
        }

        .pricing-btn {
            display: inline-block;
            padding: 12px 30px;
            background-color: transparent;
            border: 2px solid #00ff84;
            color: #00ff84;
            text-decoration: none;
            border-radius: 5px;
            transition: all 0.3s ease;
        }

        .pricing-card.featured .pricing-btn {
            background-color: #0a0a0a;
            border-color: #0a0a0a;
            color: #00ff84;
        }

        .pricing-btn:hover {
            background-color: #00ff84;
            color: #0a0a0a;
        }

        .pricing-card.featured .pricing-btn:hover {
            background-color: transparent;
            color: #0a0a0a;
        }

        @media (max-width: 768px) {
            .clients-section,
            .payment-section {
                padding: 50px 20px;
            }

            .testimonial {
                flex-direction: column;
                text-align: center;
            }

            .pricing-card.featured {
                transform: none;
            }

            .pricing-grid {
                grid-template-columns: 1fr;
            }
        }

        /* Blog Section Styles */
        .blog-section {
            padding: 100px 50px;
            background-color: rgba(255, 255, 255, 0.02);
        }

        .blog-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-top: 50px;
        }

        .blog-card {
            background-color: #0a0a0a;
            border-radius: 10px;
            overflow: hidden;
            transition: transform 0.3s ease;
        }

        .blog-card:hover {
            transform: translateY(-10px);
        }

        .blog-image {
            position: relative;
        }

        .blog-image img {
            width: 100%;
            height: 200px;
            object-fit: cover;
        }

        .blog-date {
            position: absolute;
            bottom: 0;
            left: 0;
            background-color: #00ff84;
            color: #0a0a0a;
            padding: 8px 15px;
            font-size: 0.9em;
            font-weight: bold;
        }

        .blog-content {
            padding: 25px;
        }

        .blog-content h3 {
            color: #ffffff;
            margin-bottom: 15px;
            font-size: 1.2em;
        }

        .blog-content p {
            color: #888;
            margin-bottom: 20px;
        }

        .read-more {
            color: #00ff84;
            text-decoration: none;
            font-weight: bold;
            transition: color 0.3s ease;
        }

        .read-more:hover {
            color: #ffffff;
        }

        /* Newsletter Section Styles */
        .newsletter-section {
            padding: 100px 50px;
            text-align: center;
            background: linear-gradient(rgba(0,0,0,0.8), rgba(0,0,0,0.8)), url('https://placehold.co/1920x1080') center/cover;
        }

        .newsletter-form {
            max-width: 600px;
            margin: 40px auto 0;
            display: flex;
            gap: 15px;
        }

        .newsletter-form input {
            flex: 1;
            padding: 15px;
            border: none;
            border-radius: 5px;
            background-color: rgba(255, 255, 255, 0.1);
            color: #ffffff;
        }

        .newsletter-form input::placeholder {
            color: #888;
        }

        .newsletter-form button {
            padding: 15px 30px;
            border: none;
            border-radius: 5px;
            background-color: #00ff84;
            color: #0a0a0a;
            cursor: pointer;
            font-weight: bold;
            transition: transform 0.3s ease;
        }

        .newsletter-form button:hover {
            transform: translateY(-3px);
        }

        /* Footer Styles */
        .footer {
            background-color: #0a0a0a;
            padding: 80px 50px 30px;
        }

        .footer-content {
            display: grid;
            grid-template-columns: 2fr 3fr;
            gap: 50px;
            margin-bottom: 50px;
        }

        .footer-logo p {
            color: #888;
            margin-top: 20px;
            max-width: 300px;
        }

        .footer-links {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 30px;
        }

        .footer-column h4 {
            color: #ffffff;
            margin-bottom: 20px;
        }

        .footer-column ul {
            list-style: none;
            padding: 0;
        }

        .footer-column ul li {
            margin-bottom: 10px;
        }

        .footer-column ul li a {
            color: #888;
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .footer-column ul li a:hover {
            color: #00ff84;
        }

        .contact-info li {
            color: #888;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .footer-bottom {
            padding-top: 30px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .footer-bottom p {
            color: #888;
        }

        .social-links {
            display: flex;
            gap: 15px;
        }

        .social-links a {
            width: 35px;
            height: 35px;
            background-color: rgba(255, 255, 255, 0.1);
            color: #00ff84;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .social-links a:hover {
            background-color: #00ff84;
            color: #0a0a0a;
        }

        @media (max-width: 768px) {
            .blog-section {
                padding: 50px 20px;
            }

            .newsletter-section {
                padding: 50px 20px;
            }

            .newsletter-form {
                flex-direction: column;
            }

            .footer {
                padding: 50px 20px 30px;
            }

            .footer-content {
                grid-template-columns: 1fr;
            }

            .footer-links {
                grid-template-columns: 1fr;
            }

            .footer-bottom {
                flex-direction: column;
                text-align: center;
                gap: 20px;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header>
        <nav>
            <div class="logo">Cloud</div>
            <div class="nav-links">
                <a href="#home">Home</a>
                <a href="#about">About</a>
                <a href="#services">Services</a>
                <a href="#portfolio">Portfolio</a>
                <a href="#blog">Blog</a>
                <a href="#contact">Contact</a>
            </div>
        </nav>
    </header>

    <!-- Hero Section -->
    <section class="hero" id="home">
        <div class="hero-content">
            <h1>IMAGINATION IS MORE<br>IMPORTANT THAN<br><span>KNOWLEDGE</span></h1>
            <p>Transform your ideas into reality with our cutting-edge solutions<br>that redefine the limits.</p>
            <a href="#contact" class="btn">Get Started Now</a>
        </div>
        <div class="hero-image">
            <img src="https://placehold.co/600x800" alt="Hero Image">
        </div>
    </section>

    <!-- Failure Section -->
    <section class="failure-section" id="about">
        <div class="failure-image">
            <img src="https://placehold.co/600x600" alt="Failure Section Image">
        </div>
        <div class="failure-content">
            <h2 class="section-title">FAILURE IS THE CONDIMENT THAT GIVES <span>SUCCESS</span></h2>
            <p>Success is not final, failure is not fatal: it's the courage to continue that counts. Every setback is a setup for a comeback.</p>
            <a href="#services" class="btn">Learn More</a>
        </div>
    </section>

    <!-- Experience and Skills Section -->
    <section class="experience-section" id="experience">
        <div class="section-header">
            <h2 class="section-title">EXPERIENCE AND <span>SKILL</span></h2>
            <p class="section-description">Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>
        </div>
        
        <div class="skills-grid">
            <div class="skill-item">
                <h3>ABOUT COMPUTERS</h3>
                <p>When an unknown printer took a galley of type and scrambled it to make a type specimen book.</p>
            </div>
            <div class="skill-item">
                <h3>APPS DESIGN</h3>
                <p>When an unknown printer took a galley of type and scrambled it to make a type specimen book.</p>
            </div>
            <div class="skill-item">
                <h3>DEVELOPER SUPPORT</h3>
                <p>When an unknown printer took a galley of type and scrambled it to make a type specimen book.</p>
            </div>
            <div class="skill-item">
                <h3>LEARN NEW JS</h3>
                <p>When an unknown printer took a galley of type and scrambled it to make a type specimen book.</p>
            </div>
        </div>

        <div class="progress-bars">
            <div class="progress-item">
                <div class="circular-progress" data-progress="80">
                    <div class="progress-value">80%</div>
                </div>
                <h4>GRAPHIC DESIGN</h4>
            </div>
            <div class="progress-item">
                <div class="circular-progress" data-progress="70">
                    <div class="progress-value">70%</div>
                </div>
                <h4>UI UX DESIGN</h4>
            </div>
            <div class="progress-item">
                <div class="circular-progress" data-progress="30">
                    <div class="progress-value">30%</div>
                </div>
                <h4>WEB DESIGN</h4>
            </div>
            <div class="progress-item">
                <div class="circular-progress" data-progress="90">
                    <div class="progress-value">90%</div>
                </div>
                <h4>DEVELOPMENT</h4>
            </div>
        </div>
    </section>

    <!-- Services and Solutions Section -->
    <section class="services-section" id="services">
        <div class="section-header">
            <h2 class="section-title">SERVICES AND <span>SOLUTIONS</span></h2>
            <p class="section-description">Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>
        </div>

        <div class="services-grid">
            <div class="service-card">
                <div class="service-icon">
                    <i class="fas fa-laptop-code"></i>
                </div>
                <h3>DESIGN DEVELOPMENT</h3>
                <p>Need a Project? Let's work together! Email <NAME_EMAIL></p>
                <a href="#contact" class="service-btn">Learn More</a>
            </div>
            <div class="service-card active">
                <div class="service-icon">
                    <i class="fas fa-mobile-alt"></i>
                </div>
                <h3>MOBILE DESIGN</h3>
                <p>Need a Project? Let's work together! Email <NAME_EMAIL></p>
                <a href="#contact" class="service-btn">Learn More</a>
            </div>
            <div class="service-card">
                <div class="service-icon">
                    <i class="fas fa-code"></i>
                </div>
                <h3>WEB DEVELOPMENT</h3>
                <p>Need a Project? Let's work together! Email <NAME_EMAIL></p>
                <a href="#contact" class="service-btn">Learn More</a>
            </div>
        </div>
    </section>

    <!-- Latest Working Project Section -->
    <section class="projects-section" id="portfolio">
        <div class="section-header">
            <h2 class="section-title">LATEST WORKING <span>PROJECT</span></h2>
            <p class="section-description">Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>
        </div>

        <div class="projects-grid">
            <div class="project-card">
                <img src="https://placehold.co/600x400" alt="Project 1">
                <div class="project-overlay">
                    <h3>Project Title</h3>
                    <p>Web Design</p>
                    <a href="#" class="project-link"><i class="fas fa-link"></i></a>
                </div>
            </div>
            <div class="project-card">
                <img src="https://placehold.co/600x400" alt="Project 2">
                <div class="project-overlay">
                    <h3>Project Title</h3>
                    <p>App Development</p>
                    <a href="#" class="project-link"><i class="fas fa-link"></i></a>
                </div>
            </div>
            <div class="project-card">
                <img src="https://placehold.co/600x400" alt="Project 3">
                <div class="project-overlay">
                    <h3>Project Title</h3>
                    <p>UI/UX Design</p>
                    <a href="#" class="project-link"><i class="fas fa-link"></i></a>
                </div>
            </div>
        </div>

        <div class="project-navigation">
            <button class="nav-btn prev"><i class="fas fa-chevron-left"></i></button>
            <button class="nav-btn next"><i class="fas fa-chevron-right"></i></button>
        </div>
    </section>

    <!-- Happy Clients Section -->
    <section class="clients-section" id="testimonials">
        <div class="section-header">
            <h2 class="section-title">HAPPY CLIENTS TO <span>SAYS</span></h2>
            <p class="section-description">Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>
        </div>

        <div class="testimonial-container">
            <div class="testimonial">
                <div class="client-image">
                    <img src="https://placehold.co/100x100" alt="Client">
                </div>
                <div class="testimonial-content">
                    <p class="testimonial-text">"Design is a great help super professional and with a fantastic service. I recommend 100% and I will continue working with them."</p>
                    <div class="client-info">
                        <h4>John Smith</h4>
                        <p>CEO at TechCorp</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="testimonial-dots">
            <span class="dot active"></span>
            <span class="dot"></span>
            <span class="dot"></span>
        </div>
    </section>

    <!-- Payment Options Section -->
    <section class="payment-section" id="pricing">
        <div class="section-header">
            <h2 class="section-title">ALL OUR PAYMENT <span>OPTIONS</span></h2>
            <p class="section-description">Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>
        </div>

        <div class="pricing-grid">
            <div class="pricing-card">
                <div class="pricing-icon">
                    <i class="fas fa-user"></i>
                </div>
                <h3>INFLUENCER</h3>
                <div class="price">$194</div>
                <p class="period">Per Month</p>
                <a href="#contact" class="pricing-btn">Purchase</a>
            </div>
            <div class="pricing-card featured">
                <div class="pricing-icon">
                    <i class="fas fa-building"></i>
                </div>
                <h3>AGENCY</h3>
                <div class="price">$294</div>
                <p class="period">Per Month</p>
                <a href="#contact" class="pricing-btn">Purchase</a>
            </div>
            <div class="pricing-card">
                <div class="pricing-icon">
                    <i class="fas fa-briefcase"></i>
                </div>
                <h3>ENTERPRISE</h3>
                <div class="price">Let's Chat</div>
                <p class="period">Custom Plan</p>
                <a href="#contact" class="pricing-btn">Contact Us</a>
            </div>
        </div>
    </section>

    <!-- Latest News & Blogs Section -->
    <section class="blog-section" id="blog">
        <div class="section-header">
            <h2 class="section-title">LATEST NEWS &amp; <span>BLOGS</span></h2>
            <p class="section-description">Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>
        </div>

        <div class="blog-grid">
            <div class="blog-card">
                <div class="blog-image">
                    <img src="https://placehold.co/600x400" alt="Blog Post 1">
                    <div class="blog-date">AUGUST 20, 2024</div>
                </div>
                <div class="blog-content">
                    <h3>EFFECTIVE UX TIPS TO IMPROVE YOUR DESIGN</h3>
                    <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit...</p>
                    <a href="#" class="read-more">Read More</a>
                </div>
            </div>
            <div class="blog-card">
                <div class="blog-image">
                    <img src="https://placehold.co/600x400" alt="Blog Post 2">
                    <div class="blog-date">AUGUST 18, 2024</div>
                </div>
                <div class="blog-content">
                    <h3>MASTERING MOBILE APP DEVELOPMENT</h3>
                    <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit...</p>
                    <a href="#" class="read-more">Read More</a>
                </div>
            </div>
            <div class="blog-card">
                <div class="blog-image">
                    <img src="https://placehold.co/600x400" alt="Blog Post 3">
                    <div class="blog-date">AUGUST 15, 2024</div>
                </div>
                <div class="blog-content">
                    <h3>THE FUTURE OF WEB DESIGN IN 2024</h3>
                    <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit...</p>
                    <a href="#" class="read-more">Read More</a>
                </div>
            </div>
        </div>

        <div class="blog-navigation">
            <button class="nav-btn prev"><i class="fas fa-chevron-left"></i></button>
            <button class="nav-btn next"><i class="fas fa-chevron-right"></i></button>
        </div>
    </section>

    <!-- Newsletter Section -->
    <section class="newsletter-section">
        <div class="section-header">
            <h2 class="section-title">STAY UP TO DATE, SUBSCRIBE<br>TO THE FREE <span>NEWSLETTER!</span></h2>
        </div>
        <form class="newsletter-form">
            <input type="email" placeholder="Enter your email address">
            <button type="submit">Subscribe</button>
        </form>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="footer-content">
            <div class="footer-logo">
                <div class="logo">Cloud</div>
                <p>Transform your ideas into reality with our cutting-edge solutions that redefine the limits.</p>
            </div>
            <div class="footer-links">
                <div class="footer-column">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="#home">Home</a></li>
                        <li><a href="#about">About</a></li>
                        <li><a href="#services">Services</a></li>
                        <li><a href="#portfolio">Portfolio</a></li>
                    </ul>
                </div>
                <div class="footer-column">
                    <h4>Resources</h4>
                    <ul>
                        <li><a href="#blog">Blog</a></li>
                        <li><a href="#pricing">Pricing</a></li>
                        <li><a href="#testimonials">Testimonials</a></li>
                        <li><a href="#contact">Contact</a></li>
                    </ul>
                </div>
                <div class="footer-column">
                    <h4>Contact Info</h4>
                    <ul class="contact-info">
                        <li><i class="fas fa-map-marker-alt"></i> 123 Street, City, Country</li>
                        <li><i class="fas fa-phone"></i> ****** 567 890</li>
                        <li><i class="fas fa-envelope"></i> <EMAIL></li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="footer-bottom">
            <p>© 2024 Cloud. All rights reserved.</p>
            <div class="social-links">
                <a href="#"><i class="fab fa-facebook-f"></i></a>
                <a href="#"><i class="fab fa-twitter"></i></a>
                <a href="#"><i class="fab fa-instagram"></i></a>
                <a href="#"><i class="fab fa-linkedin-in"></i></a>
            </div>
        </div>
    </footer>

    <script>
        // Add scroll event listener for header background
        window.addEventListener('scroll', function() {
            const header = document.querySelector('header');
            if (window.scrollY > 50) {
                header.style.backgroundColor = 'rgba(10, 10, 10, 0.95)';
            } else {
                header.style.backgroundColor = 'transparent';
            }
        });

        // Circular Progress Animation
        function createCircularProgress() {
            const circularProgress = document.querySelectorAll('.circular-progress');
            
            circularProgress.forEach(progress => {
                const percentage = progress.getAttribute('data-progress');
                const color = '#00ff84';
                const radius = progress.offsetWidth / 2;
                const circumference = 2 * Math.PI * (radius - 10);
                const dashOffset = circumference * (1 - percentage / 100);
                
                progress.innerHTML = `
                    <svg class="progress-ring" width="100%" height="100%">
                        <circle
                            class="progress-ring-circle-bg"
                            stroke="#333"
                            stroke-width="10"
                            fill="transparent"
                            r="${radius - 10}"
                            cx="${radius}"
                            cy="${radius}"
                        />
                        <circle
                            class="progress-ring-circle"
                            stroke="${color}"
                            stroke-width="10"
                            fill="transparent"
                            r="${radius - 10}"
                            cx="${radius}"
                            cy="${radius}"
                            style="stroke-dasharray: ${circumference}; stroke-dashoffset: ${circumference}"
                        />
                    </svg>
                    <div class="progress-value">${percentage}%</div>
                `;
                
                setTimeout(() => {
                    const circle = progress.querySelector('.progress-ring-circle');
                    circle.style.strokeDashoffset = dashOffset;
                }, 100);
            });
        }

        // Initialize circular progress when page loads
        window.addEventListener('load', createCircularProgress);

        // Blog Navigation
        const blogPrev = document.querySelector('.blog-section .prev');
        const blogNext = document.querySelector('.blog-section .next');
        const blogCards = document.querySelectorAll('.blog-card');
        let currentBlogIndex = 0;

        function updateBlogVisibility() {
            blogCards.forEach((card, index) => {
                if (index >= currentBlogIndex && index < currentBlogIndex + 3) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });
        }

        blogPrev?.addEventListener('click', () => {
            if (currentBlogIndex > 0) {
                currentBlogIndex--;
                updateBlogVisibility();
            }
        });

        blogNext?.addEventListener('click', () => {
            if (currentBlogIndex < blogCards.length - 3) {
                currentBlogIndex++;
                updateBlogVisibility();
            }
        });

        // Initialize blog visibility
        updateBlogVisibility();
    </script>

 </body></html>