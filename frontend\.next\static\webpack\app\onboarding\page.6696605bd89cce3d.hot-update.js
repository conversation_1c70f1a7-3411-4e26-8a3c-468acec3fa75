"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/onboarding/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js":
/*!**********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/plus.js ***!
  \**********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Plus; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.331.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst Plus = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Plus\", [\n    [\n        \"path\",\n        {\n            d: \"M5 12h14\",\n            key: \"1ays0h\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M12 5v14\",\n            key: \"s699le\"\n        }\n    ]\n]);\n //# sourceMappingURL=plus.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvcGx1cy5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBOzs7OztDQUtDLEdBRXFEO0FBRXRELE1BQU1DLE9BQU9ELGdFQUFnQkEsQ0FBQyxRQUFRO0lBQ3BDO1FBQUM7UUFBUTtZQUFFRSxHQUFHO1lBQVlDLEtBQUs7UUFBUztLQUFFO0lBQzFDO1FBQUM7UUFBUTtZQUFFRCxHQUFHO1lBQVlDLEtBQUs7UUFBUztLQUFFO0NBQzNDO0FBRTBCLENBQzNCLGdDQUFnQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2ljb25zL3BsdXMuanM/YzA0OCJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBsaWNlbnNlIGx1Y2lkZS1yZWFjdCB2MC4zMzEuMCAtIElTQ1xuICpcbiAqIFRoaXMgc291cmNlIGNvZGUgaXMgbGljZW5zZWQgdW5kZXIgdGhlIElTQyBsaWNlbnNlLlxuICogU2VlIHRoZSBMSUNFTlNFIGZpbGUgaW4gdGhlIHJvb3QgZGlyZWN0b3J5IG9mIHRoaXMgc291cmNlIHRyZWUuXG4gKi9cblxuaW1wb3J0IGNyZWF0ZUx1Y2lkZUljb24gZnJvbSAnLi4vY3JlYXRlTHVjaWRlSWNvbi5qcyc7XG5cbmNvbnN0IFBsdXMgPSBjcmVhdGVMdWNpZGVJY29uKFwiUGx1c1wiLCBbXG4gIFtcInBhdGhcIiwgeyBkOiBcIk01IDEyaDE0XCIsIGtleTogXCIxYXlzMGhcIiB9XSxcbiAgW1wicGF0aFwiLCB7IGQ6IFwiTTEyIDV2MTRcIiwga2V5OiBcInM2OTlsZVwiIH1dXG5dKTtcblxuZXhwb3J0IHsgUGx1cyBhcyBkZWZhdWx0IH07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1wbHVzLmpzLm1hcFxuIl0sIm5hbWVzIjpbImNyZWF0ZUx1Y2lkZUljb24iLCJQbHVzIiwiZCIsImtleSIsImRlZmF1bHQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js":
/*!*************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/trash-2.js ***!
  \*************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Trash2; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.331.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst Trash2 = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Trash2\", [\n    [\n        \"path\",\n        {\n            d: \"M3 6h18\",\n            key: \"d0wm0j\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6\",\n            key: \"4alrt4\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2\",\n            key: \"v07s0e\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"10\",\n            x2: \"10\",\n            y1: \"11\",\n            y2: \"17\",\n            key: \"1uufr5\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"14\",\n            x2: \"14\",\n            y1: \"11\",\n            y2: \"17\",\n            key: \"xtxkd\"\n        }\n    ]\n]);\n //# sourceMappingURL=trash-2.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvdHJhc2gtMi5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBOzs7OztDQUtDLEdBRXFEO0FBRXRELE1BQU1DLFNBQVNELGdFQUFnQkEsQ0FBQyxVQUFVO0lBQ3hDO1FBQUM7UUFBUTtZQUFFRSxHQUFHO1lBQVdDLEtBQUs7UUFBUztLQUFFO0lBQ3pDO1FBQUM7UUFBUTtZQUFFRCxHQUFHO1lBQXlDQyxLQUFLO1FBQVM7S0FBRTtJQUN2RTtRQUFDO1FBQVE7WUFBRUQsR0FBRztZQUFzQ0MsS0FBSztRQUFTO0tBQUU7SUFDcEU7UUFBQztRQUFRO1lBQUVDLElBQUk7WUFBTUMsSUFBSTtZQUFNQyxJQUFJO1lBQU1DLElBQUk7WUFBTUosS0FBSztRQUFTO0tBQUU7SUFDbkU7UUFBQztRQUFRO1lBQUVDLElBQUk7WUFBTUMsSUFBSTtZQUFNQyxJQUFJO1lBQU1DLElBQUk7WUFBTUosS0FBSztRQUFRO0tBQUU7Q0FDbkU7QUFFNEIsQ0FDN0IsbUNBQW1DIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvdHJhc2gtMi5qcz8yNjQ2Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGxpY2Vuc2UgbHVjaWRlLXJlYWN0IHYwLjMzMS4wIC0gSVNDXG4gKlxuICogVGhpcyBzb3VyY2UgY29kZSBpcyBsaWNlbnNlZCB1bmRlciB0aGUgSVNDIGxpY2Vuc2UuXG4gKiBTZWUgdGhlIExJQ0VOU0UgZmlsZSBpbiB0aGUgcm9vdCBkaXJlY3Rvcnkgb2YgdGhpcyBzb3VyY2UgdHJlZS5cbiAqL1xuXG5pbXBvcnQgY3JlYXRlTHVjaWRlSWNvbiBmcm9tICcuLi9jcmVhdGVMdWNpZGVJY29uLmpzJztcblxuY29uc3QgVHJhc2gyID0gY3JlYXRlTHVjaWRlSWNvbihcIlRyYXNoMlwiLCBbXG4gIFtcInBhdGhcIiwgeyBkOiBcIk0zIDZoMThcIiwga2V5OiBcImQwd20walwiIH1dLFxuICBbXCJwYXRoXCIsIHsgZDogXCJNMTkgNnYxNGMwIDEtMSAyLTIgMkg3Yy0xIDAtMi0xLTItMlY2XCIsIGtleTogXCI0YWxydDRcIiB9XSxcbiAgW1wicGF0aFwiLCB7IGQ6IFwiTTggNlY0YzAtMSAxLTIgMi0yaDRjMSAwIDIgMSAyIDJ2MlwiLCBrZXk6IFwidjA3czBlXCIgfV0sXG4gIFtcImxpbmVcIiwgeyB4MTogXCIxMFwiLCB4MjogXCIxMFwiLCB5MTogXCIxMVwiLCB5MjogXCIxN1wiLCBrZXk6IFwiMXV1ZnI1XCIgfV0sXG4gIFtcImxpbmVcIiwgeyB4MTogXCIxNFwiLCB4MjogXCIxNFwiLCB5MTogXCIxMVwiLCB5MjogXCIxN1wiLCBrZXk6IFwieHR4a2RcIiB9XVxuXSk7XG5cbmV4cG9ydCB7IFRyYXNoMiBhcyBkZWZhdWx0IH07XG4vLyMgc291cmNlTWFwcGluZ1VSTD10cmFzaC0yLmpzLm1hcFxuIl0sIm5hbWVzIjpbImNyZWF0ZUx1Y2lkZUljb24iLCJUcmFzaDIiLCJkIiwia2V5IiwieDEiLCJ4MiIsInkxIiwieTIiLCJkZWZhdWx0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/onboarding/page.tsx":
/*!*************************************!*\
  !*** ./src/app/onboarding/page.tsx ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ OnboardingPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/../node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_create_steps__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/create/steps */ \"(app-pages-browser)/./src/components/create/steps.tsx\");\n/* harmony import */ var _components_onboarding_profession_selector__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/onboarding/profession-selector */ \"(app-pages-browser)/./src/components/onboarding/profession-selector.tsx\");\n/* harmony import */ var _components_onboarding_template_selector__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/onboarding/template-selector */ \"(app-pages-browser)/./src/components/onboarding/template-selector.tsx\");\n/* harmony import */ var _components_onboarding_color_palette__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/onboarding/color-palette */ \"(app-pages-browser)/./src/components/onboarding/color-palette.tsx\");\n/* harmony import */ var _components_onboarding_section_config__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/onboarding/section-config */ \"(app-pages-browser)/./src/components/onboarding/section-config.tsx\");\n/* harmony import */ var _components_onboarding_simple_content_form__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/onboarding/simple-content-form */ \"(app-pages-browser)/./src/components/onboarding/simple-content-form.tsx\");\n/* harmony import */ var _radix_ui_react_icons__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @radix-ui/react-icons */ \"(app-pages-browser)/../node_modules/@radix-ui/react-icons/dist/react-icons.esm.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst steps = [\n    {\n        id: \"profession\",\n        title: \"Profession\",\n        description: \"Select your profession\"\n    },\n    {\n        id: \"template\",\n        title: \"Template\",\n        description: \"Choose a template\"\n    },\n    {\n        id: \"colors\",\n        title: \"Colors\",\n        description: \"Pick your brand colors\"\n    },\n    {\n        id: \"sections\",\n        title: \"Sections\",\n        description: \"Customize sections\"\n    },\n    {\n        id: \"content\",\n        title: \"Content\",\n        description: \"Add your content\"\n    }\n];\nfunction OnboardingPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [currentStep, setCurrentStep] = react__WEBPACK_IMPORTED_MODULE_1__.useState(0);\n    const [formData, setFormData] = react__WEBPACK_IMPORTED_MODULE_1__.useState({\n        profession: \"\",\n        template: \"\",\n        colors: {\n            primary: \"#0070f3\",\n            secondary: \"#6b7280\",\n            accent: \"#f59e0b\"\n        },\n        sections: [\n            {\n                id: \"hero\",\n                name: \"Hero\",\n                description: \"Introduction and main headline\",\n                isRequired: true,\n                isEnabled: true\n            },\n            {\n                id: \"about\",\n                name: \"About\",\n                description: \"Personal bio and background\",\n                isRequired: true,\n                isEnabled: true\n            },\n            {\n                id: \"experience\",\n                name: \"Experience\",\n                description: \"Work history and achievements\",\n                isEnabled: true\n            },\n            {\n                id: \"projects\",\n                name: \"Projects\",\n                description: \"Showcase of your work\",\n                isEnabled: true\n            },\n            {\n                id: \"skills\",\n                name: \"Skills\",\n                description: \"Technical and professional skills\",\n                isEnabled: true\n            },\n            {\n                id: \"testimonials\",\n                name: \"Testimonials\",\n                description: \"Client and colleague reviews\",\n                isEnabled: false\n            },\n            {\n                id: \"blog\",\n                name: \"Blog\",\n                description: \"Articles and thoughts\",\n                isEnabled: false\n            },\n            {\n                id: \"contact\",\n                name: \"Contact\",\n                description: \"Contact information and form\",\n                isRequired: true,\n                isEnabled: true\n            }\n        ],\n        content: {\n            name: \"\",\n            title: \"\",\n            bio: \"\",\n            skills: [],\n            experience: [],\n            projects: [],\n            email: \"\",\n            phone: \"\",\n            social: {\n                linkedin: \"\",\n                github: \"\",\n                twitter: \"\"\n            }\n        }\n    });\n    const handleNext = ()=>{\n        if (currentStep < steps.length - 1) {\n            setCurrentStep(currentStep + 1);\n        } else {\n            handleSubmit();\n        }\n    };\n    const handleBack = ()=>{\n        if (currentStep > 0) {\n            setCurrentStep(currentStep - 1);\n        }\n    };\n    const handleSubmit = async ()=>{\n        try {\n            console.log(\"Starting portfolio creation...\");\n            console.log(\"Form data:\", formData);\n            // Call the portfolio generation API\n            const response = await fetch(\"/api/portfolio/generate\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    profession: formData.profession,\n                    template: formData.template,\n                    colors: formData.colors,\n                    sections: formData.sections,\n                    content: formData.content\n                })\n            });\n            console.log(\"API response status:\", response.status);\n            const data = await response.json();\n            console.log(\"API response data:\", data);\n            if (data.success) {\n                console.log(\"Portfolio created successfully, redirecting to:\", data.portfolioUrl);\n                // Redirect to the generated portfolio\n                router.push(data.portfolioUrl);\n            } else {\n                console.error(\"Error generating portfolio:\", data.error);\n                alert(\"Error creating portfolio: \" + (data.error || \"Unknown error\"));\n            }\n        } catch (error) {\n            console.error(\"Error saving onboarding data:\", error);\n            alert(\"Error creating portfolio: \" + error.message);\n        }\n    };\n    const isStepValid = ()=>{\n        switch(currentStep){\n            case 0:\n                return !!formData.profession;\n            case 1:\n                return !!formData.template;\n            case 2:\n                return !!formData.colors.primary;\n            case 3:\n                return formData.sections.length > 0;\n            case 4:\n                return !!formData.content.name;\n            default:\n                return false;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"container max-w-4xl py-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                className: \"w-full\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                children: \"Create Your Portfolio\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                children: \"Follow these steps to create your personalized portfolio website\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                        lineNumber: 203,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        className: \"space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_create_steps__WEBPACK_IMPORTED_MODULE_5__.Steps, {\n                                steps: steps,\n                                currentStep: currentStep\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-8\",\n                                children: [\n                                    currentStep === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium\",\n                                                children: \"What's your profession?\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                                lineNumber: 216,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_onboarding_profession_selector__WEBPACK_IMPORTED_MODULE_6__.ProfessionSelector, {\n                                                value: formData.profession,\n                                                onChange: (value)=>setFormData({\n                                                        ...formData,\n                                                        profession: value\n                                                    })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                                lineNumber: 217,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 15\n                                    }, this),\n                                    currentStep === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium\",\n                                                children: \"Choose a template for your portfolio\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                                lineNumber: 227,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-muted-foreground\",\n                                                children: \"Select a template that best represents your professional style.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_onboarding_template_selector__WEBPACK_IMPORTED_MODULE_7__.TemplateSelector, {\n                                                value: formData.template,\n                                                onChange: (value)=>setFormData({\n                                                        ...formData,\n                                                        template: value\n                                                    })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                                lineNumber: 229,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 15\n                                    }, this),\n                                    currentStep === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium\",\n                                                children: \"Choose your brand colors\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                                lineNumber: 239,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-muted-foreground\",\n                                                children: \"Select a color palette or customize your own colors.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                                lineNumber: 240,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_onboarding_color_palette__WEBPACK_IMPORTED_MODULE_8__.ColorPalette, {\n                                                colors: formData.colors,\n                                                onChange: (colors)=>setFormData({\n                                                        ...formData,\n                                                        colors\n                                                    })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 15\n                                    }, this),\n                                    currentStep === 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium\",\n                                                children: \"Customize your portfolio sections\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                                lineNumber: 251,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-muted-foreground\",\n                                                children: \"Select and arrange the sections you want to include.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                                lineNumber: 252,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_onboarding_section_config__WEBPACK_IMPORTED_MODULE_9__.SectionConfig, {\n                                                sections: formData.sections,\n                                                onChange: (sections)=>setFormData({\n                                                        ...formData,\n                                                        sections\n                                                    })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                                lineNumber: 253,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 15\n                                    }, this),\n                                    currentStep === 4 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium\",\n                                                children: \"Add your personal information\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                                lineNumber: 263,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-muted-foreground\",\n                                                children: \"Fill in your details to personalize your portfolio.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                                lineNumber: 264,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_onboarding_simple_content_form__WEBPACK_IMPORTED_MODULE_10__.SimpleContentForm, {\n                                                defaultValues: formData.content,\n                                                onSubmit: (content)=>{\n                                                    console.log(\"=== ONBOARDING RECEIVED CONTENT ===\");\n                                                    console.log(\"Content received:\", content);\n                                                    setFormData({\n                                                        ...formData,\n                                                        content: content\n                                                    });\n                                                    if (currentStep === steps.length - 1) {\n                                                        handleSubmit();\n                                                    } else {\n                                                        handleNext();\n                                                    }\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                                lineNumber: 265,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 9\n                    }, this),\n                    currentStep !== 4 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardFooter, {\n                        className: \"flex justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: handleBack,\n                                disabled: currentStep === 0,\n                                children: \"Back\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                lineNumber: 285,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: handleNext,\n                                disabled: !isStepValid(),\n                                children: currentStep === steps.length - 1 ? \"Create Portfolio\" : \"Next\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                lineNumber: 292,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                        lineNumber: 284,\n                        columnNumber: 11\n                    }, this),\n                    currentStep === 4 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardFooter, {\n                        className: \"flex justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: handleBack,\n                                children: \"Back\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                lineNumber: 302,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-muted-foreground\",\n                                children: 'Click \"Create Portfolio\" button in the form above to generate your portfolio'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                lineNumber: 308,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                        lineNumber: 301,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                lineNumber: 202,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-8 p-4 border rounded-lg bg-slate-50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_icons__WEBPACK_IMPORTED_MODULE_11__.InfoCircledIcon, {\n                                className: \"h-5 w-5 text-blue-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                lineNumber: 318,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-md font-medium\",\n                                children: \"Admin Information\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                lineNumber: 319,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                        lineNumber: 317,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-muted-foreground mb-2\",\n                        children: \"As an admin, you can upload and manage templates at:\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                        lineNumber: 321,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                        className: \"bg-slate-200 p-2 rounded text-sm block mb-2\",\n                        children: \"/templates\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                        lineNumber: 324,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-muted-foreground\",\n                        children: \"This page allows you to upload Envato Elements templates and configure them for use with the portfolio generator.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                        lineNumber: 327,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                lineNumber: 316,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n        lineNumber: 201,\n        columnNumber: 5\n    }, this);\n}\n_s(OnboardingPage, \"9YGt226IZAs450ATaUl2B60h8z8=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = OnboardingPage;\nvar _c;\n$RefreshReg$(_c, \"OnboardingPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/onboarding/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/onboarding/simple-content-form.tsx":
/*!***********************************************************!*\
  !*** ./src/components/onboarding/simple-content-form.tsx ***!
  \***********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SimpleContentForm: function() { return /* binding */ SimpleContentForm; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* __next_internal_client_entry_do_not_use__ SimpleContentForm auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction SimpleContentForm(param) {\n    let { defaultValues, onSubmit } = param;\n    var _defaultValues_social, _defaultValues_social1, _defaultValues_social2;\n    _s();\n    // Simple state management\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: (defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.name) || \"\",\n        title: (defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.title) || \"\",\n        bio: (defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.bio) || \"\",\n        skills: (defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.skills) || [],\n        experience: (defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.experience) || [],\n        projects: (defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.projects) || [],\n        email: (defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.email) || \"\",\n        phone: (defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.phone) || \"\",\n        social: {\n            linkedin: (defaultValues === null || defaultValues === void 0 ? void 0 : (_defaultValues_social = defaultValues.social) === null || _defaultValues_social === void 0 ? void 0 : _defaultValues_social.linkedin) || \"\",\n            github: (defaultValues === null || defaultValues === void 0 ? void 0 : (_defaultValues_social1 = defaultValues.social) === null || _defaultValues_social1 === void 0 ? void 0 : _defaultValues_social1.github) || \"\",\n            twitter: (defaultValues === null || defaultValues === void 0 ? void 0 : (_defaultValues_social2 = defaultValues.social) === null || _defaultValues_social2 === void 0 ? void 0 : _defaultValues_social2.twitter) || \"\"\n        }\n    });\n    const [newSkill, setNewSkill] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const handleInputChange = (field, value)=>{\n        console.log(\"Updating \".concat(field, \" with value:\"), value);\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n    };\n    const handleSocialChange = (platform, value)=>{\n        console.log(\"Updating social.\".concat(platform, \" with value:\"), value);\n        setFormData((prev)=>({\n                ...prev,\n                social: {\n                    ...prev.social,\n                    [platform]: value\n                }\n            }));\n    };\n    const addSkill = ()=>{\n        if (newSkill.trim()) {\n            console.log(\"Adding skill:\", newSkill);\n            setFormData((prev)=>({\n                    ...prev,\n                    skills: [\n                        ...prev.skills,\n                        newSkill.trim()\n                    ]\n                }));\n            setNewSkill(\"\");\n        }\n    };\n    const removeSkill = (index)=>{\n        console.log(\"Removing skill at index:\", index);\n        setFormData((prev)=>({\n                ...prev,\n                skills: prev.skills.filter((_, i)=>i !== index)\n            }));\n    };\n    const handleSubmit = ()=>{\n        console.log(\"=== SIMPLE FORM SUBMIT ===\");\n        console.log(\"Form data being submitted:\", formData);\n        console.log(\"Name:\", formData.name);\n        console.log(\"Title:\", formData.title);\n        console.log(\"Bio:\", formData.bio);\n        console.log(\"Skills:\", formData.skills);\n        console.log(\"Email:\", formData.email);\n        onSubmit(formData);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                    className: \"p-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        htmlFor: \"name\",\n                                        children: \"Full Name *\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\simple-content-form.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                        id: \"name\",\n                                        value: formData.name,\n                                        onChange: (e)=>handleInputChange(\"name\", e.target.value),\n                                        placeholder: \"Enter your full name\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\simple-content-form.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\simple-content-form.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        htmlFor: \"title\",\n                                        children: \"Professional Title *\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\simple-content-form.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                        id: \"title\",\n                                        value: formData.title,\n                                        onChange: (e)=>handleInputChange(\"title\", e.target.value),\n                                        placeholder: \"e.g., Full Stack Developer\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\simple-content-form.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\simple-content-form.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        htmlFor: \"bio\",\n                                        children: \"Bio *\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\simple-content-form.tsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                                        id: \"bio\",\n                                        value: formData.bio,\n                                        onChange: (e)=>handleInputChange(\"bio\", e.target.value),\n                                        placeholder: \"Tell us about yourself...\",\n                                        rows: 4\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\simple-content-form.tsx\",\n                                        lineNumber: 139,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\simple-content-form.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        children: \"Skills *\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\simple-content-form.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2 mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                value: newSkill,\n                                                onChange: (e)=>setNewSkill(e.target.value),\n                                                placeholder: \"Add a skill\",\n                                                onKeyPress: (e)=>e.key === \"Enter\" && addSkill()\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\simple-content-form.tsx\",\n                                                lineNumber: 151,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                type: \"button\",\n                                                onClick: addSkill,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\simple-content-form.tsx\",\n                                                    lineNumber: 158,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\simple-content-form.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\simple-content-form.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-2\",\n                                        children: formData.skills.map((skill, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-1 bg-blue-100 px-2 py-1 rounded\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: skill\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\simple-content-form.tsx\",\n                                                        lineNumber: 164,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: ()=>removeSkill(index),\n                                                        className: \"text-red-500 hover:text-red-700\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\simple-content-form.tsx\",\n                                                            lineNumber: 170,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\simple-content-form.tsx\",\n                                                        lineNumber: 165,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\simple-content-form.tsx\",\n                                                lineNumber: 163,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\simple-content-form.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\simple-content-form.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        htmlFor: \"email\",\n                                        children: \"Email *\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\simple-content-form.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                        id: \"email\",\n                                        type: \"email\",\n                                        value: formData.email,\n                                        onChange: (e)=>handleInputChange(\"email\", e.target.value),\n                                        placeholder: \"<EMAIL>\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\simple-content-form.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\simple-content-form.tsx\",\n                                lineNumber: 177,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        htmlFor: \"phone\",\n                                        children: \"Phone\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\simple-content-form.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                        id: \"phone\",\n                                        value: formData.phone,\n                                        onChange: (e)=>handleInputChange(\"phone\", e.target.value),\n                                        placeholder: \"+****************\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\simple-content-form.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\simple-content-form.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        children: \"Social Links\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\simple-content-form.tsx\",\n                                        lineNumber: 199,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                value: formData.social.linkedin,\n                                                onChange: (e)=>handleSocialChange(\"linkedin\", e.target.value),\n                                                placeholder: \"LinkedIn URL\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\simple-content-form.tsx\",\n                                                lineNumber: 201,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                value: formData.social.github,\n                                                onChange: (e)=>handleSocialChange(\"github\", e.target.value),\n                                                placeholder: \"GitHub URL\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\simple-content-form.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                value: formData.social.twitter,\n                                                onChange: (e)=>handleSocialChange(\"twitter\", e.target.value),\n                                                placeholder: \"Twitter URL\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\simple-content-form.tsx\",\n                                                lineNumber: 211,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\simple-content-form.tsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\simple-content-form.tsx\",\n                                lineNumber: 198,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\simple-content-form.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\simple-content-form.tsx\",\n                    lineNumber: 115,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\simple-content-form.tsx\",\n                lineNumber: 114,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                onClick: handleSubmit,\n                className: \"w-full bg-green-600 hover:bg-green-700\",\n                size: \"lg\",\n                children: \"\\uD83D\\uDE80 Create Portfolio\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\simple-content-form.tsx\",\n                lineNumber: 222,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\simple-content-form.tsx\",\n        lineNumber: 113,\n        columnNumber: 5\n    }, this);\n}\n_s(SimpleContentForm, \"NXJ50w1hU3QiNvLhXrAmqW1tr6E=\");\n_c = SimpleContentForm;\nvar _c;\n$RefreshReg$(_c, \"SimpleContentForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/onboarding/simple-content-form.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/textarea.tsx":
/*!****************************************!*\
  !*** ./src/components/ui/textarea.tsx ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Textarea: function() { return /* binding */ Textarea; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n\n\n\nconst Textarea = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c = (param, ref)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\", className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\ui\\\\textarea.tsx\",\n        lineNumber: 11,\n        columnNumber: 7\n    }, undefined);\n});\n_c1 = Textarea;\nTextarea.displayName = \"Textarea\";\n\nvar _c, _c1;\n$RefreshReg$(_c, \"Textarea$React.forwardRef\");\n$RefreshReg$(_c1, \"Textarea\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3VpL3RleHRhcmVhLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBOEI7QUFFRTtBQUtoQyxNQUFNRSx5QkFBV0YsNkNBQWdCLE1BQy9CLFFBQTBCSTtRQUF6QixFQUFFQyxTQUFTLEVBQUUsR0FBR0MsT0FBTztJQUN0QixxQkFDRSw4REFBQ0M7UUFDQ0YsV0FBV0osOENBQUVBLENBQ1gsd1NBQ0FJO1FBRUZELEtBQUtBO1FBQ0osR0FBR0UsS0FBSzs7Ozs7O0FBR2Y7O0FBRUZKLFNBQVNNLFdBQVcsR0FBRztBQUVKIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9jb21wb25lbnRzL3VpL3RleHRhcmVhLnRzeD81OTMwIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiXG5cbmltcG9ydCB7IGNuIH0gZnJvbSBcIkAvbGliL3V0aWxzXCJcblxuZXhwb3J0IGludGVyZmFjZSBUZXh0YXJlYVByb3BzXG4gIGV4dGVuZHMgUmVhY3QuVGV4dGFyZWFIVE1MQXR0cmlidXRlczxIVE1MVGV4dEFyZWFFbGVtZW50PiB7fVxuXG5jb25zdCBUZXh0YXJlYSA9IFJlYWN0LmZvcndhcmRSZWY8SFRNTFRleHRBcmVhRWxlbWVudCwgVGV4dGFyZWFQcm9wcz4oXG4gICh7IGNsYXNzTmFtZSwgLi4ucHJvcHMgfSwgcmVmKSA9PiB7XG4gICAgcmV0dXJuIChcbiAgICAgIDx0ZXh0YXJlYVxuICAgICAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgICAgIFwiZmxleCBtaW4taC1bODBweF0gdy1mdWxsIHJvdW5kZWQtbWQgYm9yZGVyIGJvcmRlci1pbnB1dCBiZy1iYWNrZ3JvdW5kIHB4LTMgcHktMiB0ZXh0LXNtIHJpbmctb2Zmc2V0LWJhY2tncm91bmQgcGxhY2Vob2xkZXI6dGV4dC1tdXRlZC1mb3JlZ3JvdW5kIGZvY3VzLXZpc2libGU6b3V0bGluZS1ub25lIGZvY3VzLXZpc2libGU6cmluZy0yIGZvY3VzLXZpc2libGU6cmluZy1yaW5nIGZvY3VzLXZpc2libGU6cmluZy1vZmZzZXQtMiBkaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWQgZGlzYWJsZWQ6b3BhY2l0eS01MFwiLFxuICAgICAgICAgIGNsYXNzTmFtZVxuICAgICAgICApfVxuICAgICAgICByZWY9e3JlZn1cbiAgICAgICAgey4uLnByb3BzfVxuICAgICAgLz5cbiAgICApXG4gIH1cbilcblRleHRhcmVhLmRpc3BsYXlOYW1lID0gXCJUZXh0YXJlYVwiXG5cbmV4cG9ydCB7IFRleHRhcmVhIH1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsImNuIiwiVGV4dGFyZWEiLCJmb3J3YXJkUmVmIiwicmVmIiwiY2xhc3NOYW1lIiwicHJvcHMiLCJ0ZXh0YXJlYSIsImRpc3BsYXlOYW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/textarea.tsx\n"));

/***/ })

});