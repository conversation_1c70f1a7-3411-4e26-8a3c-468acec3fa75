# HTML Templates for Portfolio Generator

This directory contains HTML templates that can be used with the Portfolio Generator. Each template is a complete HTML file with CSS and JavaScript included, and uses placeholders that will be replaced with the user's content during portfolio generation.

## Directory Structure

Each template should be in its own directory with the following structure:

```
templates/html-templates/
├── template-id/
│   ├── index.html        # Main HTML file
│   ├── config.json       # Template configuration
│   ├── preview.jpg       # Preview image for the template
│   ├── css/              # CSS files (optional)
│   ├── js/               # JavaScript files (optional)
│   └── images/           # Image files (optional)
```

## Adding a New Template

### Using the Add Template Script

The easiest way to add a new template is to use the provided script:

```bash
npm run add-template
```

This script will guide you through the process of adding a new template, including:
- Setting the template ID, name, and description
- Copying the HTML file and associated assets
- Creating the configuration file

### Manual Addition

If you prefer to add a template manually, follow these steps:

1. Create a new directory in `templates/html-templates/` with your template ID (e.g., `modern`)
2. Add your HTML file as `index.html` in this directory
3. Create a `config.json` file with the following structure:

```json
{
  "id": "template-id",
  "name": "Template Name",
  "description": "Template description",
  "folderPath": "template-id",
  "mainFile": "index.html",
  "previewImage": "/templates/template-id/preview.jpg",
  "placeholders": {
    "name": ["{{NAME}}", "{{name}}", "{{User Name}}"],
    "title": ["{{TITLE}}", "{{title}}", "{{Job Title}}"],
    "bio": ["{{BIO}}", "{{bio}}", "{{About Me}}", "{{about}}"],
    "skills": ["{{SKILLS}}", "{{skills}}"],
    "projects": ["{{PROJECTS}}", "{{projects}}"],
    "experience": ["{{EXPERIENCE}}", "{{experience}}", "{{work}}"],
    "contact": ["{{CONTACT}}", "{{contact}}", "{{email}}", "{{phone}}"],
    "social": ["{{SOCIAL}}", "{{social}}", "{{linkedin}}", "{{github}}", "{{twitter}}"]
  }
}
```

4. Add a preview image named `preview.jpg` to the template directory
5. Copy any additional assets (CSS, JavaScript, images) to the template directory

## Placeholders

Templates should use the following placeholders in the HTML, which will be replaced with the user's content:

- `{{name}}` - User's name
- `{{title}}` - User's professional title
- `{{bio}}` - User's bio or about me text
- `{{email}}` - User's email address
- `{{phone}}` - User's phone number
- `{{linkedin}}` - User's LinkedIn URL
- `{{github}}` - User's GitHub URL
- `{{twitter}}` - User's Twitter URL

For dynamic sections like skills, projects, and experience, use the following structure:

### Skills Section

```html
<div class="skills-container">
  <div class="skill">Skill Name</div>
</div>
```

### Projects Section

```html
<div class="projects-container">
  <div class="project">
    <img src="placeholder-project.jpg" alt="Project Title">
    <div class="project-content">
      <h3 class="project-title">Project Title</h3>
      <p class="project-description">Project Description</p>
      <a href="#" class="project-link">View Project</a>
    </div>
  </div>
</div>
```

### Experience Section

```html
<div class="experience-container">
  <div class="experience-item">
    <h3 class="job-title">Job Title</h3>
    <p class="company-name">Company Name</p>
    <p class="job-date">Start Date - End Date</p>
    <p class="job-description">Job Description</p>
  </div>
</div>
```

## Color Customization

To allow for color customization, use CSS variables in your template:

```css
:root {
  --primary-color: #4a6cf7;
  --secondary-color: #2d3748;
  --accent-color: #f97316;
}
```

These variables will be replaced with the user's selected colors during portfolio generation. 