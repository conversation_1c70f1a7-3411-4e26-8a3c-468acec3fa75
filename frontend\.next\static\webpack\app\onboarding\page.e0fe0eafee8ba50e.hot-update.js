"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/onboarding/page",{

/***/ "(app-pages-browser)/./src/app/onboarding/page.tsx":
/*!*************************************!*\
  !*** ./src/app/onboarding/page.tsx ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ OnboardingPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/../node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_create_steps__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/create/steps */ \"(app-pages-browser)/./src/components/create/steps.tsx\");\n/* harmony import */ var _components_onboarding_profession_selector__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/onboarding/profession-selector */ \"(app-pages-browser)/./src/components/onboarding/profession-selector.tsx\");\n/* harmony import */ var _components_onboarding_template_selector__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/onboarding/template-selector */ \"(app-pages-browser)/./src/components/onboarding/template-selector.tsx\");\n/* harmony import */ var _components_onboarding_color_palette__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/onboarding/color-palette */ \"(app-pages-browser)/./src/components/onboarding/color-palette.tsx\");\n/* harmony import */ var _components_onboarding_section_config__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/onboarding/section-config */ \"(app-pages-browser)/./src/components/onboarding/section-config.tsx\");\n/* harmony import */ var _radix_ui_react_icons__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-icons */ \"(app-pages-browser)/../node_modules/@radix-ui/react-icons/dist/react-icons.esm.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nconst steps = [\n    {\n        id: \"profession\",\n        title: \"Profession\",\n        description: \"Select your profession\"\n    },\n    {\n        id: \"template\",\n        title: \"Template\",\n        description: \"Choose a template\"\n    },\n    {\n        id: \"colors\",\n        title: \"Colors\",\n        description: \"Pick your brand colors\"\n    },\n    {\n        id: \"sections\",\n        title: \"Sections\",\n        description: \"Customize sections\"\n    },\n    {\n        id: \"content\",\n        title: \"Content\",\n        description: \"Add your content\"\n    }\n];\nfunction OnboardingPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [currentStep, setCurrentStep] = react__WEBPACK_IMPORTED_MODULE_1__.useState(0);\n    const [formData, setFormData] = react__WEBPACK_IMPORTED_MODULE_1__.useState({\n        profession: \"\",\n        template: \"\",\n        colors: {\n            primary: \"#0070f3\",\n            secondary: \"#6b7280\",\n            accent: \"#f59e0b\"\n        },\n        sections: [\n            {\n                id: \"hero\",\n                name: \"Hero\",\n                description: \"Introduction and main headline\",\n                isRequired: true,\n                isEnabled: true\n            },\n            {\n                id: \"about\",\n                name: \"About\",\n                description: \"Personal bio and background\",\n                isRequired: true,\n                isEnabled: true\n            },\n            {\n                id: \"experience\",\n                name: \"Experience\",\n                description: \"Work history and achievements\",\n                isEnabled: true\n            },\n            {\n                id: \"projects\",\n                name: \"Projects\",\n                description: \"Showcase of your work\",\n                isEnabled: true\n            },\n            {\n                id: \"skills\",\n                name: \"Skills\",\n                description: \"Technical and professional skills\",\n                isEnabled: true\n            },\n            {\n                id: \"testimonials\",\n                name: \"Testimonials\",\n                description: \"Client and colleague reviews\",\n                isEnabled: false\n            },\n            {\n                id: \"blog\",\n                name: \"Blog\",\n                description: \"Articles and thoughts\",\n                isEnabled: false\n            },\n            {\n                id: \"contact\",\n                name: \"Contact\",\n                description: \"Contact information and form\",\n                isRequired: true,\n                isEnabled: true\n            }\n        ],\n        content: {\n            name: \"\",\n            title: \"\",\n            bio: \"\",\n            skills: [],\n            experience: [],\n            projects: [],\n            email: \"\",\n            phone: \"\",\n            social: {\n                linkedin: \"\",\n                github: \"\",\n                twitter: \"\"\n            }\n        }\n    });\n    const handleNext = ()=>{\n        if (currentStep < steps.length - 1) {\n            setCurrentStep(currentStep + 1);\n        } else {\n            handleSubmit();\n        }\n    };\n    const handleBack = ()=>{\n        if (currentStep > 0) {\n            setCurrentStep(currentStep - 1);\n        }\n    };\n    const handleSubmit = async ()=>{\n        try {\n            console.log(\"Starting portfolio creation...\");\n            console.log(\"Form data:\", formData);\n            // Call the portfolio generation API\n            const response = await fetch(\"/api/portfolio/generate\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    profession: formData.profession,\n                    template: formData.template,\n                    colors: formData.colors,\n                    sections: formData.sections,\n                    content: formData.content\n                })\n            });\n            console.log(\"API response status:\", response.status);\n            const data = await response.json();\n            console.log(\"API response data:\", data);\n            if (data.success) {\n                console.log(\"Portfolio created successfully, redirecting to:\", data.portfolioUrl);\n                // Redirect to the generated portfolio\n                router.push(data.portfolioUrl);\n            } else {\n                console.error(\"Error generating portfolio:\", data.error);\n                alert(\"Error creating portfolio: \" + (data.error || \"Unknown error\"));\n            }\n        } catch (error) {\n            console.error(\"Error saving onboarding data:\", error);\n            alert(\"Error creating portfolio: \" + error.message);\n        }\n    };\n    const isStepValid = ()=>{\n        switch(currentStep){\n            case 0:\n                return !!formData.profession;\n            case 1:\n                return !!formData.template;\n            case 2:\n                return !!formData.colors.primary;\n            case 3:\n                return formData.sections.length > 0;\n            case 4:\n                return !!formData.content.name;\n            default:\n                return false;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"container max-w-4xl py-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                className: \"w-full\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                children: \"Create Your Portfolio\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                children: \"Follow these steps to create your personalized portfolio website\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                        lineNumber: 203,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        className: \"space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_create_steps__WEBPACK_IMPORTED_MODULE_5__.Steps, {\n                                steps: steps,\n                                currentStep: currentStep\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-8\",\n                                children: [\n                                    currentStep === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium\",\n                                                children: \"What's your profession?\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                                lineNumber: 216,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_onboarding_profession_selector__WEBPACK_IMPORTED_MODULE_6__.ProfessionSelector, {\n                                                value: formData.profession,\n                                                onChange: (value)=>setFormData({\n                                                        ...formData,\n                                                        profession: value\n                                                    })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                                lineNumber: 217,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 15\n                                    }, this),\n                                    currentStep === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium\",\n                                                children: \"Choose a template for your portfolio\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                                lineNumber: 227,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-muted-foreground\",\n                                                children: \"Select a template that best represents your professional style.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_onboarding_template_selector__WEBPACK_IMPORTED_MODULE_7__.TemplateSelector, {\n                                                value: formData.template,\n                                                onChange: (value)=>setFormData({\n                                                        ...formData,\n                                                        template: value\n                                                    })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                                lineNumber: 229,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 15\n                                    }, this),\n                                    currentStep === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium\",\n                                                children: \"Choose your brand colors\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                                lineNumber: 239,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-muted-foreground\",\n                                                children: \"Select a color palette or customize your own colors.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                                lineNumber: 240,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_onboarding_color_palette__WEBPACK_IMPORTED_MODULE_8__.ColorPalette, {\n                                                colors: formData.colors,\n                                                onChange: (colors)=>setFormData({\n                                                        ...formData,\n                                                        colors\n                                                    })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 15\n                                    }, this),\n                                    currentStep === 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium\",\n                                                children: \"Customize your portfolio sections\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                                lineNumber: 251,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-muted-foreground\",\n                                                children: \"Select and arrange the sections you want to include.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                                lineNumber: 252,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_onboarding_section_config__WEBPACK_IMPORTED_MODULE_9__.SectionConfig, {\n                                                sections: formData.sections,\n                                                onChange: (sections)=>setFormData({\n                                                        ...formData,\n                                                        sections\n                                                    })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                                lineNumber: 253,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 15\n                                    }, this),\n                                    currentStep === 4 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium\",\n                                                children: \"Add your personal information\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                                lineNumber: 263,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-muted-foreground\",\n                                                children: \"Fill in your details to personalize your portfolio.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                                lineNumber: 264,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ContentForm, {\n                                                defaultValues: formData.content,\n                                                onSubmit: (content)=>{\n                                                    setFormData({\n                                                        ...formData,\n                                                        content: content\n                                                    });\n                                                    if (currentStep === steps.length - 1) {\n                                                        handleSubmit();\n                                                    } else {\n                                                        handleNext();\n                                                    }\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                                lineNumber: 265,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 9\n                    }, this),\n                    currentStep !== 4 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardFooter, {\n                        className: \"flex justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: handleBack,\n                                disabled: currentStep === 0,\n                                children: \"Back\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                lineNumber: 283,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: handleNext,\n                                disabled: !isStepValid(),\n                                children: currentStep === steps.length - 1 ? \"Create Portfolio\" : \"Next\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                lineNumber: 290,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                        lineNumber: 282,\n                        columnNumber: 11\n                    }, this),\n                    currentStep === 4 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardFooter, {\n                        className: \"flex justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: handleBack,\n                                children: \"Back\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                lineNumber: 300,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-muted-foreground\",\n                                children: 'Click \"Create Portfolio\" button in the form above to generate your portfolio'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                lineNumber: 306,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                        lineNumber: 299,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                lineNumber: 202,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-8 p-4 border rounded-lg bg-slate-50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_icons__WEBPACK_IMPORTED_MODULE_10__.InfoCircledIcon, {\n                                className: \"h-5 w-5 text-blue-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-md font-medium\",\n                                children: \"Admin Information\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                lineNumber: 317,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                        lineNumber: 315,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-muted-foreground mb-2\",\n                        children: \"As an admin, you can upload and manage templates at:\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                        lineNumber: 319,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                        className: \"bg-slate-200 p-2 rounded text-sm block mb-2\",\n                        children: \"/templates\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                        lineNumber: 322,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-muted-foreground\",\n                        children: \"This page allows you to upload Envato Elements templates and configure them for use with the portfolio generator.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                        lineNumber: 325,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                lineNumber: 314,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n        lineNumber: 201,\n        columnNumber: 5\n    }, this);\n}\n_s(OnboardingPage, \"9YGt226IZAs450ATaUl2B60h8z8=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = OnboardingPage;\nvar _c;\n$RefreshReg$(_c, \"OnboardingPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvb25ib2FyZGluZy9wYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFOEI7QUFDYTtBQUNJO0FBUWxCO0FBQ29CO0FBQytCO0FBQ0o7QUFDUjtBQUNFO0FBRWY7QUFFdkQsTUFBTWUsUUFBUTtJQUNaO1FBQ0VDLElBQUk7UUFDSkMsT0FBTztRQUNQQyxhQUFhO0lBQ2Y7SUFDQTtRQUNFRixJQUFJO1FBQ0pDLE9BQU87UUFDUEMsYUFBYTtJQUNmO0lBQ0E7UUFDRUYsSUFBSTtRQUNKQyxPQUFPO1FBQ1BDLGFBQWE7SUFDZjtJQUNBO1FBQ0VGLElBQUk7UUFDSkMsT0FBTztRQUNQQyxhQUFhO0lBQ2Y7SUFDQTtRQUNFRixJQUFJO1FBQ0pDLE9BQU87UUFDUEMsYUFBYTtJQUNmO0NBQ0Q7QUFFYyxTQUFTQzs7SUFDdEIsTUFBTUMsU0FBU25CLDBEQUFTQTtJQUN4QixNQUFNLENBQUNvQixhQUFhQyxlQUFlLEdBQUd0QiwyQ0FBYyxDQUFDO0lBQ3JELE1BQU0sQ0FBQ3dCLFVBQVVDLFlBQVksR0FBR3pCLDJDQUFjLENBQUM7UUFDN0MwQixZQUFZO1FBQ1pDLFVBQVU7UUFDVkMsUUFBUTtZQUNOQyxTQUFTO1lBQ1RDLFdBQVc7WUFDWEMsUUFBUTtRQUNWO1FBQ0FDLFVBQVU7WUFDUjtnQkFDRWhCLElBQUk7Z0JBQ0ppQixNQUFNO2dCQUNOZixhQUFhO2dCQUNiZ0IsWUFBWTtnQkFDWkMsV0FBVztZQUNiO1lBQ0E7Z0JBQ0VuQixJQUFJO2dCQUNKaUIsTUFBTTtnQkFDTmYsYUFBYTtnQkFDYmdCLFlBQVk7Z0JBQ1pDLFdBQVc7WUFDYjtZQUNBO2dCQUNFbkIsSUFBSTtnQkFDSmlCLE1BQU07Z0JBQ05mLGFBQWE7Z0JBQ2JpQixXQUFXO1lBQ2I7WUFDQTtnQkFDRW5CLElBQUk7Z0JBQ0ppQixNQUFNO2dCQUNOZixhQUFhO2dCQUNiaUIsV0FBVztZQUNiO1lBQ0E7Z0JBQ0VuQixJQUFJO2dCQUNKaUIsTUFBTTtnQkFDTmYsYUFBYTtnQkFDYmlCLFdBQVc7WUFDYjtZQUNBO2dCQUNFbkIsSUFBSTtnQkFDSmlCLE1BQU07Z0JBQ05mLGFBQWE7Z0JBQ2JpQixXQUFXO1lBQ2I7WUFDQTtnQkFDRW5CLElBQUk7Z0JBQ0ppQixNQUFNO2dCQUNOZixhQUFhO2dCQUNiaUIsV0FBVztZQUNiO1lBQ0E7Z0JBQ0VuQixJQUFJO2dCQUNKaUIsTUFBTTtnQkFDTmYsYUFBYTtnQkFDYmdCLFlBQVk7Z0JBQ1pDLFdBQVc7WUFDYjtTQUNEO1FBQ0RDLFNBQVM7WUFDUEgsTUFBTTtZQUNOaEIsT0FBTztZQUNQb0IsS0FBSztZQUNMQyxRQUFRLEVBQUU7WUFDVkMsWUFBWSxFQUFFO1lBQ2RDLFVBQVUsRUFBRTtZQUNaQyxPQUFPO1lBQ1BDLE9BQU87WUFDUEMsUUFBUTtnQkFDTkMsVUFBVTtnQkFDVkMsUUFBUTtnQkFDUkMsU0FBUztZQUNYO1FBQ0Y7SUFDRjtJQUVBLE1BQU1DLGFBQWE7UUFDakIsSUFBSTFCLGNBQWNOLE1BQU1pQyxNQUFNLEdBQUcsR0FBRztZQUNsQzFCLGVBQWVELGNBQWM7UUFDL0IsT0FBTztZQUNMNEI7UUFDRjtJQUNGO0lBRUEsTUFBTUMsYUFBYTtRQUNqQixJQUFJN0IsY0FBYyxHQUFHO1lBQ25CQyxlQUFlRCxjQUFjO1FBQy9CO0lBQ0Y7SUFFQSxNQUFNNEIsZUFBZTtRQUNuQixJQUFJO1lBQ0ZFLFFBQVFDLEdBQUcsQ0FBQztZQUNaRCxRQUFRQyxHQUFHLENBQUMsY0FBYzVCO1lBRTFCLG9DQUFvQztZQUNwQyxNQUFNNkIsV0FBVyxNQUFNQyxNQUFNLDJCQUEyQjtnQkFDdERDLFFBQVE7Z0JBQ1JDLFNBQVM7b0JBQ1AsZ0JBQWdCO2dCQUNsQjtnQkFDQUMsTUFBTUMsS0FBS0MsU0FBUyxDQUFDO29CQUNuQmpDLFlBQVlGLFNBQVNFLFVBQVU7b0JBQy9CQyxVQUFVSCxTQUFTRyxRQUFRO29CQUMzQkMsUUFBUUosU0FBU0ksTUFBTTtvQkFDdkJJLFVBQVVSLFNBQVNRLFFBQVE7b0JBQzNCSSxTQUFTWixTQUFTWSxPQUFPO2dCQUMzQjtZQUNGO1lBRUFlLFFBQVFDLEdBQUcsQ0FBQyx3QkFBd0JDLFNBQVNPLE1BQU07WUFDbkQsTUFBTUMsT0FBTyxNQUFNUixTQUFTUyxJQUFJO1lBQ2hDWCxRQUFRQyxHQUFHLENBQUMsc0JBQXNCUztZQUVsQyxJQUFJQSxLQUFLRSxPQUFPLEVBQUU7Z0JBQ2hCWixRQUFRQyxHQUFHLENBQUMsbURBQW1EUyxLQUFLRyxZQUFZO2dCQUNoRixzQ0FBc0M7Z0JBQ3RDNUMsT0FBTzZDLElBQUksQ0FBQ0osS0FBS0csWUFBWTtZQUMvQixPQUFPO2dCQUNMYixRQUFRZSxLQUFLLENBQUMsK0JBQStCTCxLQUFLSyxLQUFLO2dCQUN2REMsTUFBTSwrQkFBZ0NOLENBQUFBLEtBQUtLLEtBQUssSUFBSSxlQUFjO1lBQ3BFO1FBQ0YsRUFBRSxPQUFPQSxPQUFPO1lBQ2RmLFFBQVFlLEtBQUssQ0FBQyxpQ0FBaUNBO1lBQy9DQyxNQUFNLCtCQUErQkQsTUFBTUUsT0FBTztRQUNwRDtJQUNGO0lBRUEsTUFBTUMsY0FBYztRQUNsQixPQUFRaEQ7WUFDTixLQUFLO2dCQUNILE9BQU8sQ0FBQyxDQUFDRyxTQUFTRSxVQUFVO1lBQzlCLEtBQUs7Z0JBQ0gsT0FBTyxDQUFDLENBQUNGLFNBQVNHLFFBQVE7WUFDNUIsS0FBSztnQkFDSCxPQUFPLENBQUMsQ0FBQ0gsU0FBU0ksTUFBTSxDQUFDQyxPQUFPO1lBQ2xDLEtBQUs7Z0JBQ0gsT0FBT0wsU0FBU1EsUUFBUSxDQUFDZ0IsTUFBTSxHQUFHO1lBQ3BDLEtBQUs7Z0JBQ0gsT0FBTyxDQUFDLENBQUN4QixTQUFTWSxPQUFPLENBQUNILElBQUk7WUFDaEM7Z0JBQ0UsT0FBTztRQUNYO0lBQ0Y7SUFFQSxxQkFDRSw4REFBQ3FDO1FBQUtDLFdBQVU7OzBCQUNkLDhEQUFDcEUscURBQUlBO2dCQUFDb0UsV0FBVTs7a0NBQ2QsOERBQUNoRSwyREFBVUE7OzBDQUNULDhEQUFDQywwREFBU0E7MENBQUM7Ozs7OzswQ0FDWCw4REFBQ0gsZ0VBQWVBOzBDQUFDOzs7Ozs7Ozs7Ozs7a0NBSW5CLDhEQUFDRCw0REFBV0E7d0JBQUNtRSxXQUFVOzswQ0FDckIsOERBQUM5RCwyREFBS0E7Z0NBQUNNLE9BQU9BO2dDQUFPTSxhQUFhQTs7Ozs7OzBDQUVsQyw4REFBQ21EO2dDQUFJRCxXQUFVOztvQ0FFWmxELGdCQUFnQixtQkFDZiw4REFBQ21EO3dDQUFJRCxXQUFVOzswREFDYiw4REFBQ0U7Z0RBQUdGLFdBQVU7MERBQXNCOzs7Ozs7MERBQ3BDLDhEQUFDN0QsMEZBQWtCQTtnREFDakJnRSxPQUFPbEQsU0FBU0UsVUFBVTtnREFDMUJpRCxVQUFVLENBQUNELFFBQ1RqRCxZQUFZO3dEQUFFLEdBQUdELFFBQVE7d0RBQUVFLFlBQVlnRDtvREFBTTs7Ozs7Ozs7Ozs7O29DQUtwRHJELGdCQUFnQixtQkFDZiw4REFBQ21EO3dDQUFJRCxXQUFVOzswREFDYiw4REFBQ0U7Z0RBQUdGLFdBQVU7MERBQXNCOzs7Ozs7MERBQ3BDLDhEQUFDSztnREFBRUwsV0FBVTswREFBZ0M7Ozs7OzswREFDN0MsOERBQUM1RCxzRkFBZ0JBO2dEQUNmK0QsT0FBT2xELFNBQVNHLFFBQVE7Z0RBQ3hCZ0QsVUFBVSxDQUFDRCxRQUNUakQsWUFBWTt3REFBRSxHQUFHRCxRQUFRO3dEQUFFRyxVQUFVK0M7b0RBQU07Ozs7Ozs7Ozs7OztvQ0FLbERyRCxnQkFBZ0IsbUJBQ2YsOERBQUNtRDt3Q0FBSUQsV0FBVTs7MERBQ2IsOERBQUNFO2dEQUFHRixXQUFVOzBEQUFzQjs7Ozs7OzBEQUNwQyw4REFBQ0s7Z0RBQUVMLFdBQVU7MERBQWdDOzs7Ozs7MERBQzdDLDhEQUFDM0QsOEVBQVlBO2dEQUNYZ0IsUUFBUUosU0FBU0ksTUFBTTtnREFDdkIrQyxVQUFVLENBQUMvQyxTQUNUSCxZQUFZO3dEQUFFLEdBQUdELFFBQVE7d0RBQUVJO29EQUFPOzs7Ozs7Ozs7Ozs7b0NBS3pDUCxnQkFBZ0IsbUJBQ2YsOERBQUNtRDt3Q0FBSUQsV0FBVTs7MERBQ2IsOERBQUNFO2dEQUFHRixXQUFVOzBEQUFzQjs7Ozs7OzBEQUNwQyw4REFBQ0s7Z0RBQUVMLFdBQVU7MERBQWdDOzs7Ozs7MERBQzdDLDhEQUFDMUQsZ0ZBQWFBO2dEQUNabUIsVUFBVVIsU0FBU1EsUUFBUTtnREFDM0IyQyxVQUFVLENBQUMzQyxXQUNUUCxZQUFZO3dEQUFFLEdBQUdELFFBQVE7d0RBQUVRO29EQUFTOzs7Ozs7Ozs7Ozs7b0NBSzNDWCxnQkFBZ0IsbUJBQ2YsOERBQUNtRDt3Q0FBSUQsV0FBVTs7MERBQ2IsOERBQUNFO2dEQUFHRixXQUFVOzBEQUFzQjs7Ozs7OzBEQUNwQyw4REFBQ0s7Z0RBQUVMLFdBQVU7MERBQWdDOzs7Ozs7MERBQzdDLDhEQUFDTTtnREFDQ0MsZUFBZXRELFNBQVNZLE9BQU87Z0RBQy9CMkMsVUFBVSxDQUFDM0M7b0RBQ1RYLFlBQVk7d0RBQUUsR0FBR0QsUUFBUTt3REFBRVksU0FBU0E7b0RBQWU7b0RBQ25ELElBQUlmLGdCQUFnQk4sTUFBTWlDLE1BQU0sR0FBRyxHQUFHO3dEQUNwQ0M7b0RBQ0YsT0FBTzt3REFDTEY7b0RBQ0Y7Z0RBQ0Y7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztvQkFPVDFCLGdCQUFnQixtQkFDZiw4REFBQ2YsMkRBQVVBO3dCQUFDaUUsV0FBVTs7MENBQ3BCLDhEQUFDckUseURBQU1BO2dDQUNMOEUsU0FBUTtnQ0FDUkMsU0FBUy9CO2dDQUNUZ0MsVUFBVTdELGdCQUFnQjswQ0FDM0I7Ozs7OzswQ0FHRCw4REFBQ25CLHlEQUFNQTtnQ0FDTCtFLFNBQVNsQztnQ0FDVG1DLFVBQVUsQ0FBQ2I7MENBRVZoRCxnQkFBZ0JOLE1BQU1pQyxNQUFNLEdBQUcsSUFBSSxxQkFBcUI7Ozs7Ozs7Ozs7OztvQkFJOUQzQixnQkFBZ0IsbUJBQ2YsOERBQUNmLDJEQUFVQTt3QkFBQ2lFLFdBQVU7OzBDQUNwQiw4REFBQ3JFLHlEQUFNQTtnQ0FDTDhFLFNBQVE7Z0NBQ1JDLFNBQVMvQjswQ0FDVjs7Ozs7OzBDQUdELDhEQUFDc0I7Z0NBQUlELFdBQVU7MENBQWdDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBUXJELDhEQUFDQztnQkFBSUQsV0FBVTs7a0NBQ2IsOERBQUNDO3dCQUFJRCxXQUFVOzswQ0FDYiw4REFBQ3pELG1FQUFlQTtnQ0FBQ3lELFdBQVU7Ozs7OzswQ0FDM0IsOERBQUNFO2dDQUFHRixXQUFVOzBDQUFzQjs7Ozs7Ozs7Ozs7O2tDQUV0Qyw4REFBQ0s7d0JBQUVMLFdBQVU7a0NBQXFDOzs7Ozs7a0NBR2xELDhEQUFDWTt3QkFBS1osV0FBVTtrQ0FBOEM7Ozs7OztrQ0FHOUQsOERBQUNLO3dCQUFFTCxXQUFVO2tDQUFnQzs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTXJEO0dBelJ3QnBEOztRQUNQbEIsc0RBQVNBOzs7S0FERmtCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9hcHAvb25ib2FyZGluZy9wYWdlLnRzeD9mNGFjIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiXHJcblxyXG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIlxyXG5pbXBvcnQgeyB1c2VSb3V0ZXIgfSBmcm9tIFwibmV4dC9uYXZpZ2F0aW9uXCJcclxuaW1wb3J0IHsgQnV0dG9uIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9idXR0b25cIlxyXG5pbXBvcnQge1xyXG4gIENhcmQsXHJcbiAgQ2FyZENvbnRlbnQsXHJcbiAgQ2FyZERlc2NyaXB0aW9uLFxyXG4gIENhcmRGb290ZXIsXHJcbiAgQ2FyZEhlYWRlcixcclxuICBDYXJkVGl0bGUsXHJcbn0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9jYXJkXCJcclxuaW1wb3J0IHsgU3RlcHMgfSBmcm9tIFwiQC9jb21wb25lbnRzL2NyZWF0ZS9zdGVwc1wiXHJcbmltcG9ydCB7IFByb2Zlc3Npb25TZWxlY3RvciB9IGZyb20gXCJAL2NvbXBvbmVudHMvb25ib2FyZGluZy9wcm9mZXNzaW9uLXNlbGVjdG9yXCJcclxuaW1wb3J0IHsgVGVtcGxhdGVTZWxlY3RvciB9IGZyb20gXCJAL2NvbXBvbmVudHMvb25ib2FyZGluZy90ZW1wbGF0ZS1zZWxlY3RvclwiXHJcbmltcG9ydCB7IENvbG9yUGFsZXR0ZSB9IGZyb20gXCJAL2NvbXBvbmVudHMvb25ib2FyZGluZy9jb2xvci1wYWxldHRlXCJcclxuaW1wb3J0IHsgU2VjdGlvbkNvbmZpZyB9IGZyb20gXCJAL2NvbXBvbmVudHMvb25ib2FyZGluZy9zZWN0aW9uLWNvbmZpZ1wiXHJcbmltcG9ydCB7IFNpbXBsZUNvbnRlbnRGb3JtIH0gZnJvbSBcIkAvY29tcG9uZW50cy9vbmJvYXJkaW5nL3NpbXBsZS1jb250ZW50LWZvcm1cIlxyXG5pbXBvcnQgeyBJbmZvQ2lyY2xlZEljb24gfSBmcm9tIFwiQHJhZGl4LXVpL3JlYWN0LWljb25zXCJcclxuXHJcbmNvbnN0IHN0ZXBzID0gW1xyXG4gIHtcclxuICAgIGlkOiBcInByb2Zlc3Npb25cIixcclxuICAgIHRpdGxlOiBcIlByb2Zlc3Npb25cIixcclxuICAgIGRlc2NyaXB0aW9uOiBcIlNlbGVjdCB5b3VyIHByb2Zlc3Npb25cIlxyXG4gIH0sXHJcbiAge1xyXG4gICAgaWQ6IFwidGVtcGxhdGVcIixcclxuICAgIHRpdGxlOiBcIlRlbXBsYXRlXCIsXHJcbiAgICBkZXNjcmlwdGlvbjogXCJDaG9vc2UgYSB0ZW1wbGF0ZVwiXHJcbiAgfSxcclxuICB7XHJcbiAgICBpZDogXCJjb2xvcnNcIixcclxuICAgIHRpdGxlOiBcIkNvbG9yc1wiLFxyXG4gICAgZGVzY3JpcHRpb246IFwiUGljayB5b3VyIGJyYW5kIGNvbG9yc1wiXHJcbiAgfSxcclxuICB7XHJcbiAgICBpZDogXCJzZWN0aW9uc1wiLFxyXG4gICAgdGl0bGU6IFwiU2VjdGlvbnNcIixcclxuICAgIGRlc2NyaXB0aW9uOiBcIkN1c3RvbWl6ZSBzZWN0aW9uc1wiXHJcbiAgfSxcclxuICB7XHJcbiAgICBpZDogXCJjb250ZW50XCIsXHJcbiAgICB0aXRsZTogXCJDb250ZW50XCIsXHJcbiAgICBkZXNjcmlwdGlvbjogXCJBZGQgeW91ciBjb250ZW50XCJcclxuICB9XHJcbl1cclxuXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIE9uYm9hcmRpbmdQYWdlKCkge1xyXG4gIGNvbnN0IHJvdXRlciA9IHVzZVJvdXRlcigpXHJcbiAgY29uc3QgW2N1cnJlbnRTdGVwLCBzZXRDdXJyZW50U3RlcF0gPSBSZWFjdC51c2VTdGF0ZSgwKVxyXG4gIGNvbnN0IFtmb3JtRGF0YSwgc2V0Rm9ybURhdGFdID0gUmVhY3QudXNlU3RhdGUoe1xyXG4gICAgcHJvZmVzc2lvbjogXCJcIixcclxuICAgIHRlbXBsYXRlOiBcIlwiLFxyXG4gICAgY29sb3JzOiB7XHJcbiAgICAgIHByaW1hcnk6IFwiIzAwNzBmM1wiLFxyXG4gICAgICBzZWNvbmRhcnk6IFwiIzZiNzI4MFwiLFxyXG4gICAgICBhY2NlbnQ6IFwiI2Y1OWUwYlwiLFxyXG4gICAgfSxcclxuICAgIHNlY3Rpb25zOiBbXHJcbiAgICAgIHtcclxuICAgICAgICBpZDogXCJoZXJvXCIsXHJcbiAgICAgICAgbmFtZTogXCJIZXJvXCIsXHJcbiAgICAgICAgZGVzY3JpcHRpb246IFwiSW50cm9kdWN0aW9uIGFuZCBtYWluIGhlYWRsaW5lXCIsXHJcbiAgICAgICAgaXNSZXF1aXJlZDogdHJ1ZSxcclxuICAgICAgICBpc0VuYWJsZWQ6IHRydWUsXHJcbiAgICAgIH0sXHJcbiAgICAgIHtcclxuICAgICAgICBpZDogXCJhYm91dFwiLFxyXG4gICAgICAgIG5hbWU6IFwiQWJvdXRcIixcclxuICAgICAgICBkZXNjcmlwdGlvbjogXCJQZXJzb25hbCBiaW8gYW5kIGJhY2tncm91bmRcIixcclxuICAgICAgICBpc1JlcXVpcmVkOiB0cnVlLFxyXG4gICAgICAgIGlzRW5hYmxlZDogdHJ1ZSxcclxuICAgICAgfSxcclxuICAgICAge1xyXG4gICAgICAgIGlkOiBcImV4cGVyaWVuY2VcIixcclxuICAgICAgICBuYW1lOiBcIkV4cGVyaWVuY2VcIixcclxuICAgICAgICBkZXNjcmlwdGlvbjogXCJXb3JrIGhpc3RvcnkgYW5kIGFjaGlldmVtZW50c1wiLFxyXG4gICAgICAgIGlzRW5hYmxlZDogdHJ1ZSxcclxuICAgICAgfSxcclxuICAgICAge1xyXG4gICAgICAgIGlkOiBcInByb2plY3RzXCIsXHJcbiAgICAgICAgbmFtZTogXCJQcm9qZWN0c1wiLFxyXG4gICAgICAgIGRlc2NyaXB0aW9uOiBcIlNob3djYXNlIG9mIHlvdXIgd29ya1wiLFxyXG4gICAgICAgIGlzRW5hYmxlZDogdHJ1ZSxcclxuICAgICAgfSxcclxuICAgICAge1xyXG4gICAgICAgIGlkOiBcInNraWxsc1wiLFxyXG4gICAgICAgIG5hbWU6IFwiU2tpbGxzXCIsXHJcbiAgICAgICAgZGVzY3JpcHRpb246IFwiVGVjaG5pY2FsIGFuZCBwcm9mZXNzaW9uYWwgc2tpbGxzXCIsXHJcbiAgICAgICAgaXNFbmFibGVkOiB0cnVlLFxyXG4gICAgICB9LFxyXG4gICAgICB7XHJcbiAgICAgICAgaWQ6IFwidGVzdGltb25pYWxzXCIsXHJcbiAgICAgICAgbmFtZTogXCJUZXN0aW1vbmlhbHNcIixcclxuICAgICAgICBkZXNjcmlwdGlvbjogXCJDbGllbnQgYW5kIGNvbGxlYWd1ZSByZXZpZXdzXCIsXHJcbiAgICAgICAgaXNFbmFibGVkOiBmYWxzZSxcclxuICAgICAgfSxcclxuICAgICAge1xyXG4gICAgICAgIGlkOiBcImJsb2dcIixcclxuICAgICAgICBuYW1lOiBcIkJsb2dcIixcclxuICAgICAgICBkZXNjcmlwdGlvbjogXCJBcnRpY2xlcyBhbmQgdGhvdWdodHNcIixcclxuICAgICAgICBpc0VuYWJsZWQ6IGZhbHNlLFxyXG4gICAgICB9LFxyXG4gICAgICB7XHJcbiAgICAgICAgaWQ6IFwiY29udGFjdFwiLFxyXG4gICAgICAgIG5hbWU6IFwiQ29udGFjdFwiLFxyXG4gICAgICAgIGRlc2NyaXB0aW9uOiBcIkNvbnRhY3QgaW5mb3JtYXRpb24gYW5kIGZvcm1cIixcclxuICAgICAgICBpc1JlcXVpcmVkOiB0cnVlLFxyXG4gICAgICAgIGlzRW5hYmxlZDogdHJ1ZSxcclxuICAgICAgfSxcclxuICAgIF0sXHJcbiAgICBjb250ZW50OiB7XHJcbiAgICAgIG5hbWU6IFwiXCIsXHJcbiAgICAgIHRpdGxlOiBcIlwiLFxyXG4gICAgICBiaW86IFwiXCIsXHJcbiAgICAgIHNraWxsczogW10sXHJcbiAgICAgIGV4cGVyaWVuY2U6IFtdLFxyXG4gICAgICBwcm9qZWN0czogW10sXHJcbiAgICAgIGVtYWlsOiBcIlwiLFxyXG4gICAgICBwaG9uZTogXCJcIixcclxuICAgICAgc29jaWFsOiB7XHJcbiAgICAgICAgbGlua2VkaW46IFwiXCIsXHJcbiAgICAgICAgZ2l0aHViOiBcIlwiLFxyXG4gICAgICAgIHR3aXR0ZXI6IFwiXCIsXHJcbiAgICAgIH0sXHJcbiAgICB9XHJcbiAgfSlcclxuXHJcbiAgY29uc3QgaGFuZGxlTmV4dCA9ICgpID0+IHtcclxuICAgIGlmIChjdXJyZW50U3RlcCA8IHN0ZXBzLmxlbmd0aCAtIDEpIHtcclxuICAgICAgc2V0Q3VycmVudFN0ZXAoY3VycmVudFN0ZXAgKyAxKTtcclxuICAgIH0gZWxzZSB7XHJcbiAgICAgIGhhbmRsZVN1Ym1pdCgpO1xyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgY29uc3QgaGFuZGxlQmFjayA9ICgpID0+IHtcclxuICAgIGlmIChjdXJyZW50U3RlcCA+IDApIHtcclxuICAgICAgc2V0Q3VycmVudFN0ZXAoY3VycmVudFN0ZXAgLSAxKVxyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgY29uc3QgaGFuZGxlU3VibWl0ID0gYXN5bmMgKCkgPT4ge1xyXG4gICAgdHJ5IHtcclxuICAgICAgY29uc29sZS5sb2coXCJTdGFydGluZyBwb3J0Zm9saW8gY3JlYXRpb24uLi5cIik7XHJcbiAgICAgIGNvbnNvbGUubG9nKFwiRm9ybSBkYXRhOlwiLCBmb3JtRGF0YSk7XHJcblxyXG4gICAgICAvLyBDYWxsIHRoZSBwb3J0Zm9saW8gZ2VuZXJhdGlvbiBBUElcclxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnL2FwaS9wb3J0Zm9saW8vZ2VuZXJhdGUnLCB7XHJcbiAgICAgICAgbWV0aG9kOiAnUE9TVCcsXHJcbiAgICAgICAgaGVhZGVyczoge1xyXG4gICAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcclxuICAgICAgICB9LFxyXG4gICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHtcclxuICAgICAgICAgIHByb2Zlc3Npb246IGZvcm1EYXRhLnByb2Zlc3Npb24sXHJcbiAgICAgICAgICB0ZW1wbGF0ZTogZm9ybURhdGEudGVtcGxhdGUsXHJcbiAgICAgICAgICBjb2xvcnM6IGZvcm1EYXRhLmNvbG9ycyxcclxuICAgICAgICAgIHNlY3Rpb25zOiBmb3JtRGF0YS5zZWN0aW9ucyxcclxuICAgICAgICAgIGNvbnRlbnQ6IGZvcm1EYXRhLmNvbnRlbnRcclxuICAgICAgICB9KSxcclxuICAgICAgfSk7XHJcblxyXG4gICAgICBjb25zb2xlLmxvZyhcIkFQSSByZXNwb25zZSBzdGF0dXM6XCIsIHJlc3BvbnNlLnN0YXR1cyk7XHJcbiAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XHJcbiAgICAgIGNvbnNvbGUubG9nKFwiQVBJIHJlc3BvbnNlIGRhdGE6XCIsIGRhdGEpO1xyXG5cclxuICAgICAgaWYgKGRhdGEuc3VjY2Vzcykge1xyXG4gICAgICAgIGNvbnNvbGUubG9nKFwiUG9ydGZvbGlvIGNyZWF0ZWQgc3VjY2Vzc2Z1bGx5LCByZWRpcmVjdGluZyB0bzpcIiwgZGF0YS5wb3J0Zm9saW9VcmwpO1xyXG4gICAgICAgIC8vIFJlZGlyZWN0IHRvIHRoZSBnZW5lcmF0ZWQgcG9ydGZvbGlvXHJcbiAgICAgICAgcm91dGVyLnB1c2goZGF0YS5wb3J0Zm9saW9VcmwpO1xyXG4gICAgICB9IGVsc2Uge1xyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciBnZW5lcmF0aW5nIHBvcnRmb2xpbzpcIiwgZGF0YS5lcnJvcik7XHJcbiAgICAgICAgYWxlcnQoXCJFcnJvciBjcmVhdGluZyBwb3J0Zm9saW86IFwiICsgKGRhdGEuZXJyb3IgfHwgXCJVbmtub3duIGVycm9yXCIpKTtcclxuICAgICAgfVxyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcihcIkVycm9yIHNhdmluZyBvbmJvYXJkaW5nIGRhdGE6XCIsIGVycm9yKTtcclxuICAgICAgYWxlcnQoXCJFcnJvciBjcmVhdGluZyBwb3J0Zm9saW86IFwiICsgZXJyb3IubWVzc2FnZSk7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICBjb25zdCBpc1N0ZXBWYWxpZCA9ICgpID0+IHtcclxuICAgIHN3aXRjaCAoY3VycmVudFN0ZXApIHtcclxuICAgICAgY2FzZSAwOlxyXG4gICAgICAgIHJldHVybiAhIWZvcm1EYXRhLnByb2Zlc3Npb25cclxuICAgICAgY2FzZSAxOlxyXG4gICAgICAgIHJldHVybiAhIWZvcm1EYXRhLnRlbXBsYXRlXHJcbiAgICAgIGNhc2UgMjpcclxuICAgICAgICByZXR1cm4gISFmb3JtRGF0YS5jb2xvcnMucHJpbWFyeVxyXG4gICAgICBjYXNlIDM6XHJcbiAgICAgICAgcmV0dXJuIGZvcm1EYXRhLnNlY3Rpb25zLmxlbmd0aCA+IDBcclxuICAgICAgY2FzZSA0OlxyXG4gICAgICAgIHJldHVybiAhIWZvcm1EYXRhLmNvbnRlbnQubmFtZVxyXG4gICAgICBkZWZhdWx0OlxyXG4gICAgICAgIHJldHVybiBmYWxzZVxyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgcmV0dXJuIChcclxuICAgIDxtYWluIGNsYXNzTmFtZT1cImNvbnRhaW5lciBtYXgtdy00eGwgcHktOFwiPlxyXG4gICAgICA8Q2FyZCBjbGFzc05hbWU9XCJ3LWZ1bGxcIj5cclxuICAgICAgICA8Q2FyZEhlYWRlcj5cclxuICAgICAgICAgIDxDYXJkVGl0bGU+Q3JlYXRlIFlvdXIgUG9ydGZvbGlvPC9DYXJkVGl0bGU+XHJcbiAgICAgICAgICA8Q2FyZERlc2NyaXB0aW9uPlxyXG4gICAgICAgICAgICBGb2xsb3cgdGhlc2Ugc3RlcHMgdG8gY3JlYXRlIHlvdXIgcGVyc29uYWxpemVkIHBvcnRmb2xpbyB3ZWJzaXRlXHJcbiAgICAgICAgICA8L0NhcmREZXNjcmlwdGlvbj5cclxuICAgICAgICA8L0NhcmRIZWFkZXI+XHJcbiAgICAgICAgPENhcmRDb250ZW50IGNsYXNzTmFtZT1cInNwYWNlLXktOFwiPlxyXG4gICAgICAgICAgPFN0ZXBzIHN0ZXBzPXtzdGVwc30gY3VycmVudFN0ZXA9e2N1cnJlbnRTdGVwfSAvPlxyXG4gICAgICAgICAgXHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LThcIj5cclxuICAgICAgICAgICAgey8qIFN0ZXAgY29udGVudCB3aWxsIGJlIGFkZGVkIGhlcmUgKi99XHJcbiAgICAgICAgICAgIHtjdXJyZW50U3RlcCA9PT0gMCAmJiAoXHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cclxuICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtbWVkaXVtXCI+V2hhdCZhcG9zO3MgeW91ciBwcm9mZXNzaW9uPzwvaDM+XHJcbiAgICAgICAgICAgICAgICA8UHJvZmVzc2lvblNlbGVjdG9yXHJcbiAgICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5wcm9mZXNzaW9ufVxyXG4gICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KHZhbHVlKSA9PlxyXG4gICAgICAgICAgICAgICAgICAgIHNldEZvcm1EYXRhKHsgLi4uZm9ybURhdGEsIHByb2Zlc3Npb246IHZhbHVlIH0pXHJcbiAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgIHtjdXJyZW50U3RlcCA9PT0gMSAmJiAoXHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cclxuICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtbWVkaXVtXCI+Q2hvb3NlIGEgdGVtcGxhdGUgZm9yIHlvdXIgcG9ydGZvbGlvPC9oMz5cclxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+U2VsZWN0IGEgdGVtcGxhdGUgdGhhdCBiZXN0IHJlcHJlc2VudHMgeW91ciBwcm9mZXNzaW9uYWwgc3R5bGUuPC9wPlxyXG4gICAgICAgICAgICAgICAgPFRlbXBsYXRlU2VsZWN0b3JcclxuICAgICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLnRlbXBsYXRlfVxyXG4gICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KHZhbHVlKSA9PlxyXG4gICAgICAgICAgICAgICAgICAgIHNldEZvcm1EYXRhKHsgLi4uZm9ybURhdGEsIHRlbXBsYXRlOiB2YWx1ZSB9KVxyXG4gICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICApfVxyXG4gICAgICAgICAgICB7Y3VycmVudFN0ZXAgPT09IDIgJiYgKFxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XHJcbiAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LW1lZGl1bVwiPkNob29zZSB5b3VyIGJyYW5kIGNvbG9yczwvaDM+XHJcbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPlNlbGVjdCBhIGNvbG9yIHBhbGV0dGUgb3IgY3VzdG9taXplIHlvdXIgb3duIGNvbG9ycy48L3A+XHJcbiAgICAgICAgICAgICAgICA8Q29sb3JQYWxldHRlXHJcbiAgICAgICAgICAgICAgICAgIGNvbG9ycz17Zm9ybURhdGEuY29sb3JzfVxyXG4gICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGNvbG9ycykgPT5cclxuICAgICAgICAgICAgICAgICAgICBzZXRGb3JtRGF0YSh7IC4uLmZvcm1EYXRhLCBjb2xvcnMgfSlcclxuICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAge2N1cnJlbnRTdGVwID09PSAzICYmIChcclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxyXG4gICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1tZWRpdW1cIj5DdXN0b21pemUgeW91ciBwb3J0Zm9saW8gc2VjdGlvbnM8L2gzPlxyXG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LW11dGVkLWZvcmVncm91bmRcIj5TZWxlY3QgYW5kIGFycmFuZ2UgdGhlIHNlY3Rpb25zIHlvdSB3YW50IHRvIGluY2x1ZGUuPC9wPlxyXG4gICAgICAgICAgICAgICAgPFNlY3Rpb25Db25maWdcclxuICAgICAgICAgICAgICAgICAgc2VjdGlvbnM9e2Zvcm1EYXRhLnNlY3Rpb25zfVxyXG4gICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KHNlY3Rpb25zKSA9PlxyXG4gICAgICAgICAgICAgICAgICAgIHNldEZvcm1EYXRhKHsgLi4uZm9ybURhdGEsIHNlY3Rpb25zIH0pXHJcbiAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgIHtjdXJyZW50U3RlcCA9PT0gNCAmJiAoXHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cclxuICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtbWVkaXVtXCI+QWRkIHlvdXIgcGVyc29uYWwgaW5mb3JtYXRpb248L2gzPlxyXG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LW11dGVkLWZvcmVncm91bmRcIj5GaWxsIGluIHlvdXIgZGV0YWlscyB0byBwZXJzb25hbGl6ZSB5b3VyIHBvcnRmb2xpby48L3A+XHJcbiAgICAgICAgICAgICAgICA8Q29udGVudEZvcm1cclxuICAgICAgICAgICAgICAgICAgZGVmYXVsdFZhbHVlcz17Zm9ybURhdGEuY29udGVudCBhcyBhbnl9XHJcbiAgICAgICAgICAgICAgICAgIG9uU3VibWl0PXsoY29udGVudCkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgIHNldEZvcm1EYXRhKHsgLi4uZm9ybURhdGEsIGNvbnRlbnQ6IGNvbnRlbnQgYXMgYW55IH0pO1xyXG4gICAgICAgICAgICAgICAgICAgIGlmIChjdXJyZW50U3RlcCA9PT0gc3RlcHMubGVuZ3RoIC0gMSkge1xyXG4gICAgICAgICAgICAgICAgICAgICAgaGFuZGxlU3VibWl0KCk7XHJcbiAgICAgICAgICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAgICAgICAgIGhhbmRsZU5leHQoKTtcclxuICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICApfVxyXG4gICAgICAgICAgICB7LyogQWRkaXRpb25hbCBzdGVwIGNvbnRlbnQgd2lsbCBiZSBhZGRlZCBmb3Igb3RoZXIgc3RlcHMgKi99XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICA8L0NhcmRDb250ZW50PlxyXG4gICAgICAgIHtjdXJyZW50U3RlcCAhPT0gNCAmJiAoXHJcbiAgICAgICAgICA8Q2FyZEZvb3RlciBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlblwiPlxyXG4gICAgICAgICAgICA8QnV0dG9uXHJcbiAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxyXG4gICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZUJhY2t9XHJcbiAgICAgICAgICAgICAgZGlzYWJsZWQ9e2N1cnJlbnRTdGVwID09PSAwfVxyXG4gICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgQmFja1xyXG4gICAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgICAgPEJ1dHRvblxyXG4gICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZU5leHR9XHJcbiAgICAgICAgICAgICAgZGlzYWJsZWQ9eyFpc1N0ZXBWYWxpZCgpfVxyXG4gICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAge2N1cnJlbnRTdGVwID09PSBzdGVwcy5sZW5ndGggLSAxID8gXCJDcmVhdGUgUG9ydGZvbGlvXCIgOiBcIk5leHRcIn1cclxuICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICA8L0NhcmRGb290ZXI+XHJcbiAgICAgICAgKX1cclxuICAgICAgICB7Y3VycmVudFN0ZXAgPT09IDQgJiYgKFxyXG4gICAgICAgICAgPENhcmRGb290ZXIgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW5cIj5cclxuICAgICAgICAgICAgPEJ1dHRvblxyXG4gICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCJcclxuICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVCYWNrfVxyXG4gICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgQmFja1xyXG4gICAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPlxyXG4gICAgICAgICAgICAgIENsaWNrIFwiQ3JlYXRlIFBvcnRmb2xpb1wiIGJ1dHRvbiBpbiB0aGUgZm9ybSBhYm92ZSB0byBnZW5lcmF0ZSB5b3VyIHBvcnRmb2xpb1xyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDwvQ2FyZEZvb3Rlcj5cclxuICAgICAgICApfVxyXG4gICAgICA8L0NhcmQ+XHJcbiAgICAgIFxyXG4gICAgICB7LyogQWRtaW4gSW5mbyBTZWN0aW9uICovfVxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTggcC00IGJvcmRlciByb3VuZGVkLWxnIGJnLXNsYXRlLTUwXCI+XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMiBtYi0yXCI+XHJcbiAgICAgICAgICA8SW5mb0NpcmNsZWRJY29uIGNsYXNzTmFtZT1cImgtNSB3LTUgdGV4dC1ibHVlLTUwMFwiIC8+XHJcbiAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1tZCBmb250LW1lZGl1bVwiPkFkbWluIEluZm9ybWF0aW9uPC9oMz5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtbXV0ZWQtZm9yZWdyb3VuZCBtYi0yXCI+XHJcbiAgICAgICAgICBBcyBhbiBhZG1pbiwgeW91IGNhbiB1cGxvYWQgYW5kIG1hbmFnZSB0ZW1wbGF0ZXMgYXQ6XHJcbiAgICAgICAgPC9wPlxyXG4gICAgICAgIDxjb2RlIGNsYXNzTmFtZT1cImJnLXNsYXRlLTIwMCBwLTIgcm91bmRlZCB0ZXh0LXNtIGJsb2NrIG1iLTJcIj5cclxuICAgICAgICAgIC90ZW1wbGF0ZXNcclxuICAgICAgICA8L2NvZGU+XHJcbiAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LW11dGVkLWZvcmVncm91bmRcIj5cclxuICAgICAgICAgIFRoaXMgcGFnZSBhbGxvd3MgeW91IHRvIHVwbG9hZCBFbnZhdG8gRWxlbWVudHMgdGVtcGxhdGVzIGFuZCBjb25maWd1cmUgdGhlbSBmb3IgdXNlIHdpdGggdGhlIHBvcnRmb2xpbyBnZW5lcmF0b3IuXHJcbiAgICAgICAgPC9wPlxyXG4gICAgICA8L2Rpdj5cclxuICAgIDwvbWFpbj5cclxuICApXHJcbn0gIl0sIm5hbWVzIjpbIlJlYWN0IiwidXNlUm91dGVyIiwiQnV0dG9uIiwiQ2FyZCIsIkNhcmRDb250ZW50IiwiQ2FyZERlc2NyaXB0aW9uIiwiQ2FyZEZvb3RlciIsIkNhcmRIZWFkZXIiLCJDYXJkVGl0bGUiLCJTdGVwcyIsIlByb2Zlc3Npb25TZWxlY3RvciIsIlRlbXBsYXRlU2VsZWN0b3IiLCJDb2xvclBhbGV0dGUiLCJTZWN0aW9uQ29uZmlnIiwiSW5mb0NpcmNsZWRJY29uIiwic3RlcHMiLCJpZCIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJPbmJvYXJkaW5nUGFnZSIsInJvdXRlciIsImN1cnJlbnRTdGVwIiwic2V0Q3VycmVudFN0ZXAiLCJ1c2VTdGF0ZSIsImZvcm1EYXRhIiwic2V0Rm9ybURhdGEiLCJwcm9mZXNzaW9uIiwidGVtcGxhdGUiLCJjb2xvcnMiLCJwcmltYXJ5Iiwic2Vjb25kYXJ5IiwiYWNjZW50Iiwic2VjdGlvbnMiLCJuYW1lIiwiaXNSZXF1aXJlZCIsImlzRW5hYmxlZCIsImNvbnRlbnQiLCJiaW8iLCJza2lsbHMiLCJleHBlcmllbmNlIiwicHJvamVjdHMiLCJlbWFpbCIsInBob25lIiwic29jaWFsIiwibGlua2VkaW4iLCJnaXRodWIiLCJ0d2l0dGVyIiwiaGFuZGxlTmV4dCIsImxlbmd0aCIsImhhbmRsZVN1Ym1pdCIsImhhbmRsZUJhY2siLCJjb25zb2xlIiwibG9nIiwicmVzcG9uc2UiLCJmZXRjaCIsIm1ldGhvZCIsImhlYWRlcnMiLCJib2R5IiwiSlNPTiIsInN0cmluZ2lmeSIsInN0YXR1cyIsImRhdGEiLCJqc29uIiwic3VjY2VzcyIsInBvcnRmb2xpb1VybCIsInB1c2giLCJlcnJvciIsImFsZXJ0IiwibWVzc2FnZSIsImlzU3RlcFZhbGlkIiwibWFpbiIsImNsYXNzTmFtZSIsImRpdiIsImgzIiwidmFsdWUiLCJvbkNoYW5nZSIsInAiLCJDb250ZW50Rm9ybSIsImRlZmF1bHRWYWx1ZXMiLCJvblN1Ym1pdCIsInZhcmlhbnQiLCJvbkNsaWNrIiwiZGlzYWJsZWQiLCJjb2RlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/onboarding/page.tsx\n"));

/***/ })

});