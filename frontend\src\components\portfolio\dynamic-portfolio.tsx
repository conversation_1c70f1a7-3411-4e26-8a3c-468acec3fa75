"use client"

import { useEffect, useState } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Mail, Phone, Linkedin, Github, Twitter, ExternalLink, Calendar } from 'lucide-react'

interface PortfolioContent {
  name: string
  title: string
  bio: string
  skills: string[]
  experience: Array<{
    title: string
    company: string
    startDate: string
    endDate?: string
    description: string
  }>
  projects: Array<{
    title: string
    description: string
    link?: string
    technologies?: string[]
  }>
  email: string
  phone: string
  social: {
    linkedin?: string
    github?: string
    twitter?: string
  }
}

interface PortfolioData {
  id: string
  template: string
  profession: string
  colors: {
    primary: string
    secondary: string
    accent: string
  }
  sections: string[]
  content: PortfolioContent
  status: string
}

interface DynamicPortfolioProps {
  portfolioId: string
}

export function DynamicPortfolio({ portfolioId }: DynamicPortfolioProps) {
  const [portfolio, setPortfolio] = useState<PortfolioData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchPortfolio = async () => {
      try {
        console.log(`Fetching portfolio data for ID: ${portfolioId}`)
        
        const response = await fetch(`/api/portfolio/${portfolioId}/data`)
        const data = await response.json()

        if (!response.ok) {
          throw new Error(data.error || 'Failed to fetch portfolio')
        }

        console.log('Portfolio data received:', data.portfolio)
        setPortfolio(data.portfolio)
      } catch (err) {
        console.error('Error fetching portfolio:', err)
        setError(err instanceof Error ? err.message : 'Failed to load portfolio')
      } finally {
        setLoading(false)
      }
    }

    if (portfolioId) {
      fetchPortfolio()
    }
  }, [portfolioId])

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p>Loading portfolio...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="max-w-md">
          <CardContent className="p-6 text-center">
            <h2 className="text-xl font-bold text-red-600 mb-2">Error</h2>
            <p className="text-gray-600 mb-4">{error}</p>
            <Button onClick={() => window.location.reload()}>
              Try Again
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (!portfolio) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <p>Portfolio not found</p>
      </div>
    )
  }

  const { content, colors } = portfolio

  return (
    <div 
      className="min-h-screen bg-gray-50"
      style={{ 
        '--primary-color': colors.primary,
        '--secondary-color': colors.secondary,
        '--accent-color': colors.accent
      } as React.CSSProperties}
    >
      {/* Hero Section */}
      <section className="bg-white py-20">
        <div className="container mx-auto px-4 text-center">
          <h1 
            className="text-5xl font-bold mb-4"
            style={{ color: colors.primary }}
          >
            {content.name || 'Your Name'}
          </h1>
          <h2 
            className="text-2xl mb-6"
            style={{ color: colors.secondary }}
          >
            {content.title || 'Your Professional Title'}
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto mb-8">
            {content.bio || 'Your professional bio will appear here...'}
          </p>
          
          {/* Contact Links */}
          <div className="flex justify-center space-x-4">
            {content.email && (
              <Button 
                variant="outline" 
                asChild
                style={{ borderColor: colors.primary, color: colors.primary }}
              >
                <a href={`mailto:${content.email}`}>
                  <Mail className="w-4 h-4 mr-2" />
                  Email
                </a>
              </Button>
            )}
            {content.social?.linkedin && (
              <Button variant="outline" asChild>
                <a href={content.social.linkedin} target="_blank" rel="noopener noreferrer">
                  <Linkedin className="w-4 h-4 mr-2" />
                  LinkedIn
                </a>
              </Button>
            )}
            {content.social?.github && (
              <Button variant="outline" asChild>
                <a href={content.social.github} target="_blank" rel="noopener noreferrer">
                  <Github className="w-4 h-4 mr-2" />
                  GitHub
                </a>
              </Button>
            )}
          </div>
        </div>
      </section>

      {/* Skills Section */}
      {content.skills && content.skills.length > 0 && (
        <section className="py-16">
          <div className="container mx-auto px-4">
            <h3 
              className="text-3xl font-bold text-center mb-12"
              style={{ color: colors.primary }}
            >
              Skills
            </h3>
            <div className="flex flex-wrap justify-center gap-3">
              {content.skills.map((skill, index) => (
                <div
                  key={index}
                  className="px-4 py-2 text-sm rounded-full border"
                  style={{
                    backgroundColor: colors.accent + '20',
                    color: colors.primary,
                    borderColor: colors.accent
                  }}
                >
                  {skill}
                </div>
              ))}
            </div>
          </div>
        </section>
      )}

      {/* Experience Section */}
      {content.experience && content.experience.length > 0 && (
        <section className="py-16 bg-white">
          <div className="container mx-auto px-4">
            <h3 
              className="text-3xl font-bold text-center mb-12"
              style={{ color: colors.primary }}
            >
              Experience
            </h3>
            <div className="max-w-4xl mx-auto space-y-8">
              {content.experience.map((exp, index) => (
                <Card key={index}>
                  <CardContent className="p-6">
                    <div className="flex justify-between items-start mb-4">
                      <div>
                        <h4 className="text-xl font-semibold" style={{ color: colors.primary }}>
                          {exp.title}
                        </h4>
                        <p className="text-lg" style={{ color: colors.secondary }}>
                          {exp.company}
                        </p>
                      </div>
                      <div className="text-sm text-gray-500">
                        <Calendar className="w-4 h-4 inline mr-1" />
                        {exp.startDate} - {exp.endDate || 'Present'}
                      </div>
                    </div>
                    <p className="text-gray-600">{exp.description}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>
      )}

      {/* Projects Section */}
      {content.projects && content.projects.length > 0 && (
        <section className="py-16">
          <div className="container mx-auto px-4">
            <h3 
              className="text-3xl font-bold text-center mb-12"
              style={{ color: colors.primary }}
            >
              Projects
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl mx-auto">
              {content.projects.map((project, index) => (
                <Card key={index} className="hover:shadow-lg transition-shadow">
                  <CardContent className="p-6">
                    <h4 className="text-xl font-semibold mb-3" style={{ color: colors.primary }}>
                      {project.title}
                    </h4>
                    <p className="text-gray-600 mb-4">{project.description}</p>
                    {project.link && (
                      <Button 
                        variant="outline" 
                        size="sm" 
                        asChild
                        style={{ borderColor: colors.accent, color: colors.accent }}
                      >
                        <a href={project.link} target="_blank" rel="noopener noreferrer">
                          <ExternalLink className="w-4 h-4 mr-2" />
                          View Project
                        </a>
                      </Button>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>
      )}

      {/* Contact Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4 text-center">
          <h3 
            className="text-3xl font-bold mb-8"
            style={{ color: colors.primary }}
          >
            Get In Touch
          </h3>
          <div className="flex justify-center space-x-6">
            {content.email && (
              <a 
                href={`mailto:${content.email}`}
                className="flex items-center space-x-2 text-gray-600 hover:text-blue-600"
              >
                <Mail className="w-5 h-5" />
                <span>{content.email}</span>
              </a>
            )}
            {content.phone && (
              <a 
                href={`tel:${content.phone}`}
                className="flex items-center space-x-2 text-gray-600 hover:text-blue-600"
              >
                <Phone className="w-5 h-5" />
                <span>{content.phone}</span>
              </a>
            )}
          </div>
        </div>
      </section>
    </div>
  )
}
