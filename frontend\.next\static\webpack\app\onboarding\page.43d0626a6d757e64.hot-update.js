"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/onboarding/page",{

/***/ "(app-pages-browser)/./src/components/onboarding/content-form.tsx":
/*!****************************************************!*\
  !*** ./src/components/onboarding/content-form.tsx ***!
  \****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ContentForm: function() { return /* binding */ ContentForm; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/../node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! zod */ \"(app-pages-browser)/../node_modules/zod/lib/index.mjs\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_form__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/form */ \"(app-pages-browser)/./src/components/ui/form.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* __next_internal_client_entry_do_not_use__ ContentForm auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst contentSchema = zod__WEBPACK_IMPORTED_MODULE_9__.object({\n    name: zod__WEBPACK_IMPORTED_MODULE_9__.string().min(2, \"Name must be at least 2 characters\"),\n    title: zod__WEBPACK_IMPORTED_MODULE_9__.string().min(2, \"Title must be at least 2 characters\"),\n    bio: zod__WEBPACK_IMPORTED_MODULE_9__.string().min(10, \"Bio must be at least 10 characters\"),\n    skills: zod__WEBPACK_IMPORTED_MODULE_9__.array(zod__WEBPACK_IMPORTED_MODULE_9__.string()).min(1, \"Add at least one skill\"),\n    experience: zod__WEBPACK_IMPORTED_MODULE_9__.array(zod__WEBPACK_IMPORTED_MODULE_9__.object({\n        title: zod__WEBPACK_IMPORTED_MODULE_9__.string().min(2, \"Title must be at least 2 characters\"),\n        company: zod__WEBPACK_IMPORTED_MODULE_9__.string().min(2, \"Company must be at least 2 characters\"),\n        startDate: zod__WEBPACK_IMPORTED_MODULE_9__.string().min(2, \"Start date is required\"),\n        endDate: zod__WEBPACK_IMPORTED_MODULE_9__.string().optional(),\n        description: zod__WEBPACK_IMPORTED_MODULE_9__.string().min(10, \"Description must be at least 10 characters\")\n    })).min(0),\n    projects: zod__WEBPACK_IMPORTED_MODULE_9__.array(zod__WEBPACK_IMPORTED_MODULE_9__.object({\n        title: zod__WEBPACK_IMPORTED_MODULE_9__.string().min(2, \"Title must be at least 2 characters\"),\n        description: zod__WEBPACK_IMPORTED_MODULE_9__.string().min(10, \"Description must be at least 10 characters\"),\n        link: zod__WEBPACK_IMPORTED_MODULE_9__.string().url(\"Must be a valid URL\").optional()\n    })).min(0),\n    email: zod__WEBPACK_IMPORTED_MODULE_9__.string().email(\"Must be a valid email\").optional(),\n    phone: zod__WEBPACK_IMPORTED_MODULE_9__.string().optional(),\n    social: zod__WEBPACK_IMPORTED_MODULE_9__.object({\n        linkedin: zod__WEBPACK_IMPORTED_MODULE_9__.string().url(\"Must be a valid URL\").optional(),\n        github: zod__WEBPACK_IMPORTED_MODULE_9__.string().url(\"Must be a valid URL\").optional(),\n        twitter: zod__WEBPACK_IMPORTED_MODULE_9__.string().url(\"Must be a valid URL\").optional()\n    })\n});\nfunction ContentForm(param) {\n    let { defaultValues, onSubmit } = param;\n    _s();\n    const form = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_10__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__.zodResolver)(contentSchema),\n        defaultValues: defaultValues || {\n            name: \"\",\n            title: \"\",\n            bio: \"\",\n            skills: [],\n            experience: [],\n            projects: [],\n            email: \"\",\n            phone: \"\",\n            social: {\n                linkedin: \"\",\n                github: \"\",\n                twitter: \"\"\n            }\n        }\n    });\n    const handleFormSubmit = ()=>{\n        console.log(\"=== FORM SUBMIT FUNCTION CALLED ===\");\n        const currentValues = form.getValues();\n        console.log(\"Current form values:\", currentValues);\n        console.log(\"Form state:\", form.formState);\n        console.log(\"Form watch all:\", form.watch());\n        // Get values directly from form fields\n        const name = form.getValues(\"name\");\n        const title = form.getValues(\"title\");\n        const bio = form.getValues(\"bio\");\n        console.log(\"Individual values - name:\", name, \"title:\", title, \"bio:\", bio);\n        // Submit form with current values\n        console.log(\"Submitting form with values:\", currentValues);\n        onSubmit(currentValues);\n    };\n    const { fields: experienceFields, append: appendExperience, remove: removeExperience } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_10__.useFieldArray)({\n        control: form.control,\n        name: \"experience\"\n    });\n    const { fields: projectFields, append: appendProject, remove: removeProject } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_10__.useFieldArray)({\n        control: form.control,\n        name: \"projects\"\n    });\n    const [newSkill, setNewSkill] = react__WEBPACK_IMPORTED_MODULE_1__.useState(\"\");\n    const addSkill = ()=>{\n        if (newSkill.trim()) {\n            const currentSkills = form.getValues(\"skills\");\n            form.setValue(\"skills\", [\n                ...currentSkills,\n                newSkill.trim()\n            ]);\n            setNewSkill(\"\");\n        }\n    };\n    const removeSkill = (index)=>{\n        const currentSkills = form.getValues(\"skills\");\n        form.setValue(\"skills\", currentSkills.filter((_, i)=>i !== index));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.Form, {\n        ...form,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n            className: \"space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                            control: form.control,\n                            name: \"name\",\n                            render: (param)=>{\n                                let { field } = param;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                            children: \"Full Name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                placeholder: \"John Doe\",\n                                                ...field\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                lineNumber: 134,\n                                                columnNumber: 19\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                            lineNumber: 133,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 17\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 15\n                                }, void 0);\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                            lineNumber: 127,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                            control: form.control,\n                            name: \"title\",\n                            render: (param)=>{\n                                let { field } = param;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                            children: \"Professional Title\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                placeholder: \"Senior Software Engineer\",\n                                                ...field\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                lineNumber: 148,\n                                                columnNumber: 19\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                            lineNumber: 147,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 17\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 15\n                                }, void 0);\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                            control: form.control,\n                            name: \"bio\",\n                            render: (param)=>{\n                                let { field } = param;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                            children: \"Bio\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                                placeholder: \"Tell us about yourself...\",\n                                                ...field\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 19\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                            lineNumber: 161,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 17\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                    lineNumber: 159,\n                                    columnNumber: 15\n                                }, void 0);\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                            lineNumber: 155,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                    children: \"Skills\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                            value: newSkill,\n                                            onChange: (e)=>setNewSkill(e.target.value),\n                                            placeholder: \"Add a skill\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                            lineNumber: 175,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            type: \"button\",\n                                            onClick: addSkill,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                lineNumber: 181,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-2\",\n                                    children: form.watch(\"skills\").map((skill, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-1 bg-secondary text-secondary-foreground px-2 py-1 rounded-md\",\n                                            children: [\n                                                skill,\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    type: \"button\",\n                                                    variant: \"ghost\",\n                                                    size: \"icon\",\n                                                    className: \"h-4 w-4 p-0 hover:bg-transparent\",\n                                                    onClick: ()=>removeSkill(index),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-3 w-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                        lineNumber: 198,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                    lineNumber: 191,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                    lineNumber: 184,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                            lineNumber: 172,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                            children: \"Experience\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            type: \"button\",\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            onClick: ()=>appendExperience({\n                                                    title: \"\",\n                                                    company: \"\",\n                                                    startDate: \"\",\n                                                    endDate: \"\",\n                                                    description: \"\"\n                                                }),\n                                            children: \"Add Experience\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                            lineNumber: 208,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: experienceFields.map((field, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                                className: \"pt-6 space-y-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1 grid gap-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                                                                    control: form.control,\n                                                                    name: \"experience.\".concat(index, \".title\"),\n                                                                    render: (param)=>{\n                                                                        let { field } = param;\n                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                                                    children: \"Title\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 236,\n                                                                                    columnNumber: 31\n                                                                                }, void 0),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                        ...field\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                        lineNumber: 238,\n                                                                                        columnNumber: 33\n                                                                                    }, void 0)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 237,\n                                                                                    columnNumber: 31\n                                                                                }, void 0),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 240,\n                                                                                    columnNumber: 31\n                                                                                }, void 0)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                            lineNumber: 235,\n                                                                            columnNumber: 29\n                                                                        }, void 0);\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                    lineNumber: 231,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                                                                    control: form.control,\n                                                                    name: \"experience.\".concat(index, \".company\"),\n                                                                    render: (param)=>{\n                                                                        let { field } = param;\n                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                                                    children: \"Company\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 249,\n                                                                                    columnNumber: 31\n                                                                                }, void 0),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                        ...field\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                        lineNumber: 251,\n                                                                                        columnNumber: 33\n                                                                                    }, void 0)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 250,\n                                                                                    columnNumber: 31\n                                                                                }, void 0),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 253,\n                                                                                    columnNumber: 31\n                                                                                }, void 0)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                            lineNumber: 248,\n                                                                            columnNumber: 29\n                                                                        }, void 0);\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                    lineNumber: 244,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                                                                    control: form.control,\n                                                                    name: \"experience.\".concat(index, \".startDate\"),\n                                                                    render: (param)=>{\n                                                                        let { field } = param;\n                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                                                    children: \"Start Date\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 262,\n                                                                                    columnNumber: 31\n                                                                                }, void 0),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                        ...field,\n                                                                                        placeholder: \"e.g., January 2020\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                        lineNumber: 264,\n                                                                                        columnNumber: 33\n                                                                                    }, void 0)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 263,\n                                                                                    columnNumber: 31\n                                                                                }, void 0),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 266,\n                                                                                    columnNumber: 31\n                                                                                }, void 0)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                            lineNumber: 261,\n                                                                            columnNumber: 29\n                                                                        }, void 0);\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                    lineNumber: 257,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                                                                    control: form.control,\n                                                                    name: \"experience.\".concat(index, \".endDate\"),\n                                                                    render: (param)=>{\n                                                                        let { field } = param;\n                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                                                    children: \"End Date (Optional)\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 275,\n                                                                                    columnNumber: 31\n                                                                                }, void 0),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                        ...field,\n                                                                                        value: field.value || \"\",\n                                                                                        placeholder: \"e.g., Present or December 2023\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                        lineNumber: 277,\n                                                                                        columnNumber: 33\n                                                                                    }, void 0)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 276,\n                                                                                    columnNumber: 31\n                                                                                }, void 0),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 279,\n                                                                                    columnNumber: 31\n                                                                                }, void 0)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                            lineNumber: 274,\n                                                                            columnNumber: 29\n                                                                        }, void 0);\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                    lineNumber: 270,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                                                                    control: form.control,\n                                                                    name: \"experience.\".concat(index, \".description\"),\n                                                                    render: (param)=>{\n                                                                        let { field } = param;\n                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                                                    children: \"Description\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 288,\n                                                                                    columnNumber: 31\n                                                                                }, void 0),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                                                                        ...field\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                        lineNumber: 290,\n                                                                                        columnNumber: 33\n                                                                                    }, void 0)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 289,\n                                                                                    columnNumber: 31\n                                                                                }, void 0),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 292,\n                                                                                    columnNumber: 31\n                                                                                }, void 0)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                            lineNumber: 287,\n                                                                            columnNumber: 29\n                                                                        }, void 0);\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                    lineNumber: 283,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                            lineNumber: 230,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            type: \"button\",\n                                                            variant: \"ghost\",\n                                                            size: \"icon\",\n                                                            onClick: ()=>removeExperience(index),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                lineNumber: 303,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                            lineNumber: 297,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                    lineNumber: 229,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, field.id, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                            lineNumber: 227,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                    lineNumber: 225,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                            lineNumber: 205,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                            children: \"Projects\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                            lineNumber: 314,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            type: \"button\",\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            onClick: ()=>appendProject({\n                                                    title: \"\",\n                                                    description: \"\",\n                                                    link: \"\"\n                                                }),\n                                            children: \"Add Project\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                            lineNumber: 315,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                    lineNumber: 313,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: projectFields.map((field, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                                className: \"pt-6 space-y-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1 grid gap-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                                                                    control: form.control,\n                                                                    name: \"projects.\".concat(index, \".title\"),\n                                                                    render: (param)=>{\n                                                                        let { field } = param;\n                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                                                    children: \"Title\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 341,\n                                                                                    columnNumber: 31\n                                                                                }, void 0),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                        ...field\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                        lineNumber: 343,\n                                                                                        columnNumber: 33\n                                                                                    }, void 0)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 342,\n                                                                                    columnNumber: 31\n                                                                                }, void 0),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 345,\n                                                                                    columnNumber: 31\n                                                                                }, void 0)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                            lineNumber: 340,\n                                                                            columnNumber: 29\n                                                                        }, void 0);\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                    lineNumber: 336,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                                                                    control: form.control,\n                                                                    name: \"projects.\".concat(index, \".description\"),\n                                                                    render: (param)=>{\n                                                                        let { field } = param;\n                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                                                    children: \"Description\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 354,\n                                                                                    columnNumber: 31\n                                                                                }, void 0),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                                                                        ...field\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                        lineNumber: 356,\n                                                                                        columnNumber: 33\n                                                                                    }, void 0)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 355,\n                                                                                    columnNumber: 31\n                                                                                }, void 0),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 358,\n                                                                                    columnNumber: 31\n                                                                                }, void 0)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                            lineNumber: 353,\n                                                                            columnNumber: 29\n                                                                        }, void 0);\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                    lineNumber: 349,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                                                                    control: form.control,\n                                                                    name: \"projects.\".concat(index, \".link\"),\n                                                                    render: (param)=>{\n                                                                        let { field } = param;\n                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                                                    children: \"Link\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 367,\n                                                                                    columnNumber: 31\n                                                                                }, void 0),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                        type: \"url\",\n                                                                                        ...field,\n                                                                                        value: field.value || \"\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                        lineNumber: 369,\n                                                                                        columnNumber: 33\n                                                                                    }, void 0)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 368,\n                                                                                    columnNumber: 31\n                                                                                }, void 0),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 371,\n                                                                                    columnNumber: 31\n                                                                                }, void 0)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                            lineNumber: 366,\n                                                                            columnNumber: 29\n                                                                        }, void 0);\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                    lineNumber: 362,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                            lineNumber: 335,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            type: \"button\",\n                                                            variant: \"ghost\",\n                                                            size: \"icon\",\n                                                            onClick: ()=>removeProject(index),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                lineNumber: 382,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                            lineNumber: 376,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                    lineNumber: 334,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                lineNumber: 333,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, field.id, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                            lineNumber: 332,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                    lineNumber: 330,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                            lineNumber: 312,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                    children: \"Contact Information\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                    lineNumber: 392,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                                            control: form.control,\n                                            name: \"email\",\n                                            render: (param)=>{\n                                                let { field } = param;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                            children: \"Email\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                            lineNumber: 399,\n                                                            columnNumber: 21\n                                                        }, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                type: \"email\",\n                                                                ...field,\n                                                                value: field.value || \"\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                lineNumber: 401,\n                                                                columnNumber: 23\n                                                            }, void 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                            lineNumber: 400,\n                                                            columnNumber: 21\n                                                        }, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                            lineNumber: 403,\n                                                            columnNumber: 21\n                                                        }, void 0)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                    lineNumber: 398,\n                                                    columnNumber: 19\n                                                }, void 0);\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                            lineNumber: 394,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                                            control: form.control,\n                                            name: \"phone\",\n                                            render: (param)=>{\n                                                let { field } = param;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                            children: \"Phone (Optional)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                            lineNumber: 412,\n                                                            columnNumber: 21\n                                                        }, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                type: \"tel\",\n                                                                ...field,\n                                                                value: field.value || \"\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                lineNumber: 414,\n                                                                columnNumber: 23\n                                                            }, void 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                            lineNumber: 413,\n                                                            columnNumber: 21\n                                                        }, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                            lineNumber: 416,\n                                                            columnNumber: 21\n                                                        }, void 0)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                    lineNumber: 411,\n                                                    columnNumber: 19\n                                                }, void 0);\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                            lineNumber: 407,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                    lineNumber: 393,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                            lineNumber: 391,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                    children: \"Social Links\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                    lineNumber: 424,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                                            control: form.control,\n                                            name: \"social.linkedin\",\n                                            render: (param)=>{\n                                                let { field } = param;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                            children: \"LinkedIn\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                            lineNumber: 431,\n                                                            columnNumber: 21\n                                                        }, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                type: \"url\",\n                                                                ...field,\n                                                                value: field.value || \"\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                lineNumber: 433,\n                                                                columnNumber: 23\n                                                            }, void 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                            lineNumber: 432,\n                                                            columnNumber: 21\n                                                        }, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                            lineNumber: 435,\n                                                            columnNumber: 21\n                                                        }, void 0)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                    lineNumber: 430,\n                                                    columnNumber: 19\n                                                }, void 0);\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                            lineNumber: 426,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                                            control: form.control,\n                                            name: \"social.github\",\n                                            render: (param)=>{\n                                                let { field } = param;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                            children: \"GitHub\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                            lineNumber: 444,\n                                                            columnNumber: 21\n                                                        }, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                type: \"url\",\n                                                                ...field,\n                                                                value: field.value || \"\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                lineNumber: 446,\n                                                                columnNumber: 23\n                                                            }, void 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                            lineNumber: 445,\n                                                            columnNumber: 21\n                                                        }, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                            lineNumber: 448,\n                                                            columnNumber: 21\n                                                        }, void 0)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                    lineNumber: 443,\n                                                    columnNumber: 19\n                                                }, void 0);\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                            lineNumber: 439,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                                            control: form.control,\n                                            name: \"social.twitter\",\n                                            render: (param)=>{\n                                                let { field } = param;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                            children: \"Twitter\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                            lineNumber: 457,\n                                                            columnNumber: 21\n                                                        }, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                type: \"url\",\n                                                                ...field,\n                                                                value: field.value || \"\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                lineNumber: 459,\n                                                                columnNumber: 23\n                                                            }, void 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                            lineNumber: 458,\n                                                            columnNumber: 21\n                                                        }, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                            lineNumber: 461,\n                                                            columnNumber: 21\n                                                        }, void 0)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                    lineNumber: 456,\n                                                    columnNumber: 19\n                                                }, void 0);\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                            lineNumber: 452,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                    lineNumber: 425,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                            lineNumber: 423,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                    lineNumber: 126,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-end\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        type: \"button\",\n                        className: \"bg-green-600 hover:bg-green-700\",\n                        onClick: ()=>{\n                            console.log(\"=== BUTTON CLICKED ===\");\n                            console.log(\"Form errors:\", form.formState.errors);\n                            console.log(\"Form is valid:\", form.formState.isValid);\n                            const formValues = form.getValues();\n                            console.log(\"Form values:\", formValues);\n                            // Call the submit handler\n                            handleFormSubmit();\n                        },\n                        children: \"\\uD83D\\uDE80 Create Portfolio\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                        lineNumber: 470,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                    lineNumber: 469,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n            lineNumber: 125,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n        lineNumber: 124,\n        columnNumber: 5\n    }, this);\n}\n_s(ContentForm, \"CUNbDDiudl1iQFH+kZW4keFRSFc=\", false, function() {\n    return [\n        react_hook_form__WEBPACK_IMPORTED_MODULE_10__.useForm,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_10__.useFieldArray,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_10__.useFieldArray\n    ];\n});\n_c = ContentForm;\nvar _c;\n$RefreshReg$(_c, \"ContentForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/onboarding/content-form.tsx\n"));

/***/ })

});