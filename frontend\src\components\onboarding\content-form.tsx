"use client"

import * as React from "react"
import { useState, useEffect } from "react"
import { Plus, Trash2 } from "lucide-react"
import { useFieldArray, useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Card, CardContent } from "@/components/ui/card"

const contentSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  title: z.string().min(2, "Title must be at least 2 characters"),
  bio: z.string().min(10, "Bio must be at least 10 characters"),
  skills: z.array(z.string()).min(1, "Add at least one skill"),
  experience: z.array(z.object({
    title: z.string().min(2, "Title must be at least 2 characters"),
    company: z.string().min(2, "Company must be at least 2 characters"),
    startDate: z.string().min(2, "Start date is required"),
    endDate: z.string().optional(),
    description: z.string().min(10, "Description must be at least 10 characters"),
  })).min(0),
  projects: z.array(z.object({
    title: z.string().min(2, "Title must be at least 2 characters"),
    description: z.string().min(10, "Description must be at least 10 characters"),
    link: z.string().url("Must be a valid URL").optional(),
  })).min(0),
  email: z.string().email("Must be a valid email").optional(),
  phone: z.string().optional(),
  social: z.object({
    linkedin: z.string().url("Must be a valid URL").optional(),
    github: z.string().url("Must be a valid URL").optional(),
    twitter: z.string().url("Must be a valid URL").optional(),
  }),
})

type ContentFormValues = z.infer<typeof contentSchema>

interface ContentFormProps {
  defaultValues?: ContentFormValues
  onSubmit: (values: ContentFormValues) => void
}

export function ContentForm({ defaultValues, onSubmit }: ContentFormProps) {
  // Simple state-based approach instead of complex form validation
  const [formData, setFormData] = useState({
    name: defaultValues?.name || "",
    title: defaultValues?.title || "",
    bio: defaultValues?.bio || "",
    skills: defaultValues?.skills || [],
    experience: defaultValues?.experience || [],
    projects: defaultValues?.projects || [],
    email: defaultValues?.email || "",
    phone: defaultValues?.phone || "",
    social: {
      linkedin: defaultValues?.social?.linkedin || "",
      github: defaultValues?.social?.github || "",
      twitter: defaultValues?.social?.twitter || "",
    },
  })

  const form = useForm<ContentFormValues>({
    resolver: zodResolver(contentSchema),
    defaultValues: formData,
  })

  const handleFormSubmit = () => {
    console.log("=== FORM SUBMIT FUNCTION CALLED ===");

    // Get fresh data from form
    const formValues = form.getValues();
    console.log("Form values:", formValues);

    // Merge form values with state data
    const finalData = {
      ...formData,
      ...formValues
    };

    console.log("Final merged data:", finalData);
    console.log("Name:", finalData.name);
    console.log("Title:", finalData.title);
    console.log("Bio:", finalData.bio);
    console.log("Skills:", finalData.skills);
    console.log("Projects:", finalData.projects);
    console.log("Experience:", finalData.experience);
    console.log("Email:", finalData.email);

    // Validate required fields
    if (!finalData.name?.trim()) {
      alert("Please enter your name");
      return;
    }
    if (!finalData.title?.trim()) {
      alert("Please enter your professional title");
      return;
    }
    if (!finalData.bio?.trim()) {
      alert("Please enter your bio");
      return;
    }
    if (!finalData.email?.trim()) {
      alert("Please enter your email");
      return;
    }

    // Submit with merged data
    console.log("Submitting form with final data:", finalData);
    onSubmit(finalData as any);
  }

  // Update form data when input changes
  const updateFormData = (field: string, value: any) => {
    console.log(`Updating ${field} with:`, value);
    const newFormData = {
      ...formData,
      [field]: value
    };
    setFormData(newFormData);
    console.log("Updated formData:", newFormData);
  }

  const { fields: experienceFields, append: appendExperience, remove: removeExperience } = 
    useFieldArray({
      control: form.control,
      name: "experience",
    })

  const { fields: projectFields, append: appendProject, remove: removeProject } = 
    useFieldArray({
      control: form.control,
      name: "projects",
    })

  const [newSkill, setNewSkill] = React.useState("")

  const addSkill = () => {
    if (newSkill.trim()) {
      const currentSkills = formData.skills || []
      const newSkills = [...currentSkills, newSkill.trim()]

      // Update both form and state
      form.setValue("skills", newSkills)
      updateFormData("skills", newSkills)
      setNewSkill("")

      console.log("Added skill:", newSkill.trim())
      console.log("Updated skills:", newSkills)
    }
  }

  const removeSkill = (index: number) => {
    const currentSkills = formData.skills || []
    const newSkills = currentSkills.filter((_, i) => i !== index)

    // Update both form and state
    form.setValue("skills", newSkills)
    updateFormData("skills", newSkills)

    console.log("Removed skill at index:", index)
    console.log("Updated skills:", newSkills)
  }

  return (
    <Form {...form}>
      <form className="space-y-8">
        <div className="grid gap-6">
          <div>
            <Label htmlFor="name">Full Name</Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) => updateFormData('name', e.target.value)}
              placeholder="John Doe"
            />
          </div>

          <div>
            <Label htmlFor="title">Professional Title</Label>
            <Input
              id="title"
              value={formData.title}
              onChange={(e) => updateFormData('title', e.target.value)}
              placeholder="Senior Software Engineer"
            />
          </div>

          <div>
            <Label htmlFor="bio">Bio</Label>
            <Textarea
              id="bio"
              value={formData.bio}
              onChange={(e) => updateFormData('bio', e.target.value)}
              placeholder="Tell us about yourself..."
              className="min-h-[100px]"
            />
          </div>

          <div className="space-y-4">
            <Label>Skills</Label>
            <div className="flex gap-2">
              <Input
                value={newSkill}
                onChange={(e) => setNewSkill(e.target.value)}
                placeholder="Add a skill"
              />
              <Button type="button" onClick={addSkill}>
                <Plus className="h-4 w-4" />
              </Button>
            </div>
            <div className="flex flex-wrap gap-2">
              {(formData.skills || []).map((skill, index) => (
                <div
                  key={index}
                  className="flex items-center gap-1 bg-secondary text-secondary-foreground px-2 py-1 rounded-md"
                >
                  {skill}
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    className="h-4 w-4 p-0 hover:bg-transparent"
                    onClick={() => removeSkill(index)}
                  >
                    <Trash2 className="h-3 w-3" />
                  </Button>
                </div>
              ))}
            </div>
          </div>

          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label>Experience</Label>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() =>
                  appendExperience({
                    title: "",
                    company: "",
                    startDate: "",
                    endDate: "",
                    description: "",
                  })
                }
              >
                Add Experience
              </Button>
            </div>
            <div className="space-y-4">
              {experienceFields.map((field, index) => (
                <Card key={field.id}>
                  <CardContent className="pt-6 space-y-4">
                    <div className="flex justify-between items-start">
                      <div className="flex-1 grid gap-4">
                        <FormField
                          control={form.control}
                          name={`experience.${index}.title`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Title</FormLabel>
                              <FormControl>
                                <Input {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name={`experience.${index}.company`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Company</FormLabel>
                              <FormControl>
                                <Input {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name={`experience.${index}.startDate`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Start Date</FormLabel>
                              <FormControl>
                                <Input {...field} placeholder="e.g., January 2020" />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name={`experience.${index}.endDate`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>End Date (Optional)</FormLabel>
                              <FormControl>
                                <Input {...field} value={field.value || ""} placeholder="e.g., Present or December 2023" />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name={`experience.${index}.description`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Description</FormLabel>
                              <FormControl>
                                <Textarea {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                      <Button
                        type="button"
                        variant="ghost"
                        size="icon"
                        onClick={() => removeExperience(index)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label>Projects</Label>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() =>
                  appendProject({
                    title: "",
                    description: "",
                    link: "",
                  })
                }
              >
                Add Project
              </Button>
            </div>
            <div className="space-y-4">
              {projectFields.map((field, index) => (
                <Card key={field.id}>
                  <CardContent className="pt-6 space-y-4">
                    <div className="flex justify-between items-start">
                      <div className="flex-1 grid gap-4">
                        <FormField
                          control={form.control}
                          name={`projects.${index}.title`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Title</FormLabel>
                              <FormControl>
                                <Input {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name={`projects.${index}.description`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Description</FormLabel>
                              <FormControl>
                                <Textarea {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name={`projects.${index}.link`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Link</FormLabel>
                              <FormControl>
                                <Input type="url" {...field} value={field.value || ""} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                      <Button
                        type="button"
                        variant="ghost"
                        size="icon"
                        onClick={() => removeProject(index)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          <div className="space-y-4">
            <Label>Contact Information</Label>
            <div className="grid gap-4">
              <div>
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => updateFormData('email', e.target.value)}
                  placeholder="<EMAIL>"
                />
              </div>
              <FormField
                control={form.control}
                name="phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Phone (Optional)</FormLabel>
                    <FormControl>
                      <Input type="tel" {...field} value={field.value || ""} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>

          <div className="space-y-4">
            <Label>Social Links</Label>
            <div className="grid gap-4">
              <FormField
                control={form.control}
                name="social.linkedin"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>LinkedIn</FormLabel>
                    <FormControl>
                      <Input type="url" {...field} value={field.value || ""} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="social.github"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>GitHub</FormLabel>
                    <FormControl>
                      <Input type="url" {...field} value={field.value || ""} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="social.twitter"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Twitter</FormLabel>
                    <FormControl>
                      <Input type="url" {...field} value={field.value || ""} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>
        </div>
        
        <div className="flex justify-end">
          <Button
            type="button"
            className="bg-green-600 hover:bg-green-700"
            onClick={() => {
              console.log("=== BUTTON CLICKED ===");
              console.log("Form errors:", form.formState.errors);
              console.log("Form is valid:", form.formState.isValid);
              const formValues = form.getValues();
              console.log("Form values:", formValues);

              // Call the submit handler
              handleFormSubmit();
            }}
          >
            🚀 Create Portfolio
          </Button>
        </div>
      </form>
    </Form>
  )
} 