"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/onboarding/page",{

/***/ "(app-pages-browser)/./src/app/onboarding/page.tsx":
/*!*************************************!*\
  !*** ./src/app/onboarding/page.tsx ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ OnboardingPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/../node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_create_steps__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/create/steps */ \"(app-pages-browser)/./src/components/create/steps.tsx\");\n/* harmony import */ var _components_onboarding_profession_selector__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/onboarding/profession-selector */ \"(app-pages-browser)/./src/components/onboarding/profession-selector.tsx\");\n/* harmony import */ var _components_onboarding_template_selector__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/onboarding/template-selector */ \"(app-pages-browser)/./src/components/onboarding/template-selector.tsx\");\n/* harmony import */ var _components_onboarding_color_palette__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/onboarding/color-palette */ \"(app-pages-browser)/./src/components/onboarding/color-palette.tsx\");\n/* harmony import */ var _components_onboarding_section_config__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/onboarding/section-config */ \"(app-pages-browser)/./src/components/onboarding/section-config.tsx\");\n/* harmony import */ var _components_onboarding_content_form__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/onboarding/content-form */ \"(app-pages-browser)/./src/components/onboarding/content-form.tsx\");\n/* harmony import */ var _radix_ui_react_icons__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @radix-ui/react-icons */ \"(app-pages-browser)/../node_modules/@radix-ui/react-icons/dist/react-icons.esm.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst steps = [\n    {\n        id: \"profession\",\n        title: \"Profession\",\n        description: \"Select your profession\"\n    },\n    {\n        id: \"template\",\n        title: \"Template\",\n        description: \"Choose a template\"\n    },\n    {\n        id: \"colors\",\n        title: \"Colors\",\n        description: \"Pick your brand colors\"\n    },\n    {\n        id: \"sections\",\n        title: \"Sections\",\n        description: \"Customize sections\"\n    },\n    {\n        id: \"content\",\n        title: \"Content\",\n        description: \"Add your content\"\n    }\n];\nfunction OnboardingPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [currentStep, setCurrentStep] = react__WEBPACK_IMPORTED_MODULE_1__.useState(0);\n    const [formData, setFormData] = react__WEBPACK_IMPORTED_MODULE_1__.useState({\n        profession: \"\",\n        template: \"\",\n        colors: {\n            primary: \"#0070f3\",\n            secondary: \"#6b7280\",\n            accent: \"#f59e0b\"\n        },\n        sections: [\n            {\n                id: \"hero\",\n                name: \"Hero\",\n                description: \"Introduction and main headline\",\n                isRequired: true,\n                isEnabled: true\n            },\n            {\n                id: \"about\",\n                name: \"About\",\n                description: \"Personal bio and background\",\n                isRequired: true,\n                isEnabled: true\n            },\n            {\n                id: \"experience\",\n                name: \"Experience\",\n                description: \"Work history and achievements\",\n                isEnabled: true\n            },\n            {\n                id: \"projects\",\n                name: \"Projects\",\n                description: \"Showcase of your work\",\n                isEnabled: true\n            },\n            {\n                id: \"skills\",\n                name: \"Skills\",\n                description: \"Technical and professional skills\",\n                isEnabled: true\n            },\n            {\n                id: \"testimonials\",\n                name: \"Testimonials\",\n                description: \"Client and colleague reviews\",\n                isEnabled: false\n            },\n            {\n                id: \"blog\",\n                name: \"Blog\",\n                description: \"Articles and thoughts\",\n                isEnabled: false\n            },\n            {\n                id: \"contact\",\n                name: \"Contact\",\n                description: \"Contact information and form\",\n                isRequired: true,\n                isEnabled: true\n            }\n        ],\n        content: {\n            name: \"\",\n            title: \"\",\n            bio: \"\",\n            skills: [],\n            experience: [],\n            projects: [],\n            email: \"\",\n            phone: \"\",\n            social: {\n                linkedin: \"\",\n                github: \"\",\n                twitter: \"\"\n            }\n        }\n    });\n    const handleNext = ()=>{\n        if (currentStep < steps.length - 1) {\n            setCurrentStep(currentStep + 1);\n        } else {\n            handleSubmit();\n        }\n    };\n    const handleBack = ()=>{\n        if (currentStep > 0) {\n            setCurrentStep(currentStep - 1);\n        }\n    };\n    const handleSubmit = async ()=>{\n        try {\n            console.log(\"=== HANDLE SUBMIT CALLED ===\");\n            console.log(\"Starting portfolio creation...\");\n            console.log(\"Form data:\", formData);\n            // Call the portfolio generation API\n            const response = await fetch(\"/api/portfolio/generate\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    profession: formData.profession,\n                    template: formData.template,\n                    colors: formData.colors,\n                    sections: formData.sections,\n                    content: formData.content\n                })\n            });\n            console.log(\"API response status:\", response.status);\n            const data = await response.json();\n            console.log(\"API response data:\", data);\n            if (data.success) {\n                console.log(\"Portfolio created successfully, redirecting to:\", data.portfolioUrl);\n                // Redirect to the generated portfolio\n                router.push(data.portfolioUrl);\n            } else {\n                console.error(\"Error generating portfolio:\", data.error);\n                alert(\"Error creating portfolio: \" + (data.error || \"Unknown error\"));\n            }\n        } catch (error) {\n            console.error(\"Error saving onboarding data:\", error);\n            alert(\"Error creating portfolio: \" + error.message);\n        }\n    };\n    const isStepValid = ()=>{\n        switch(currentStep){\n            case 0:\n                return !!formData.profession;\n            case 1:\n                return !!formData.template;\n            case 2:\n                return !!formData.colors.primary;\n            case 3:\n                return formData.sections.length > 0;\n            case 4:\n                return !!formData.content.name;\n            default:\n                return false;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"container max-w-4xl py-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                className: \"w-full\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                children: \"Create Your Portfolio\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                children: \"Follow these steps to create your personalized portfolio website\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                        lineNumber: 204,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        className: \"space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_create_steps__WEBPACK_IMPORTED_MODULE_5__.Steps, {\n                                steps: steps,\n                                currentStep: currentStep\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                lineNumber: 211,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-8\",\n                                children: [\n                                    currentStep === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium\",\n                                                children: \"What's your profession?\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                                lineNumber: 217,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_onboarding_profession_selector__WEBPACK_IMPORTED_MODULE_6__.ProfessionSelector, {\n                                                value: formData.profession,\n                                                onChange: (value)=>setFormData({\n                                                        ...formData,\n                                                        profession: value\n                                                    })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                                lineNumber: 218,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 15\n                                    }, this),\n                                    currentStep === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium\",\n                                                children: \"Choose a template for your portfolio\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-muted-foreground\",\n                                                children: \"Select a template that best represents your professional style.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                                lineNumber: 229,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_onboarding_template_selector__WEBPACK_IMPORTED_MODULE_7__.TemplateSelector, {\n                                                value: formData.template,\n                                                onChange: (value)=>setFormData({\n                                                        ...formData,\n                                                        template: value\n                                                    })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                                lineNumber: 230,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                        lineNumber: 227,\n                                        columnNumber: 15\n                                    }, this),\n                                    currentStep === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium\",\n                                                children: \"Choose your brand colors\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                                lineNumber: 240,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-muted-foreground\",\n                                                children: \"Select a color palette or customize your own colors.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_onboarding_color_palette__WEBPACK_IMPORTED_MODULE_8__.ColorPalette, {\n                                                colors: formData.colors,\n                                                onChange: (colors)=>setFormData({\n                                                        ...formData,\n                                                        colors\n                                                    })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 15\n                                    }, this),\n                                    currentStep === 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium\",\n                                                children: \"Customize your portfolio sections\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                                lineNumber: 252,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-muted-foreground\",\n                                                children: \"Select and arrange the sections you want to include.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                                lineNumber: 253,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_onboarding_section_config__WEBPACK_IMPORTED_MODULE_9__.SectionConfig, {\n                                                sections: formData.sections,\n                                                onChange: (sections)=>setFormData({\n                                                        ...formData,\n                                                        sections\n                                                    })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                                lineNumber: 254,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 15\n                                    }, this),\n                                    currentStep === 4 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium\",\n                                                children: \"Add your personal information\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                                lineNumber: 264,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-muted-foreground\",\n                                                children: \"Fill in your details to personalize your portfolio.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                                lineNumber: 265,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_onboarding_content_form__WEBPACK_IMPORTED_MODULE_10__.ContentForm, {\n                                                defaultValues: formData.content,\n                                                onSubmit: (content)=>{\n                                                    console.log(\"ContentForm onSubmit called with:\", content);\n                                                    console.log(\"Current step:\", currentStep);\n                                                    console.log(\"Steps length:\", steps.length);\n                                                    setFormData({\n                                                        ...formData,\n                                                        content: content\n                                                    });\n                                                    if (currentStep === steps.length - 1) {\n                                                        console.log(\"Calling handleSubmit...\");\n                                                        handleSubmit();\n                                                    } else {\n                                                        console.log(\"Calling handleNext...\");\n                                                        handleNext();\n                                                    }\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                        lineNumber: 263,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                        lineNumber: 210,\n                        columnNumber: 9\n                    }, this),\n                    currentStep !== 4 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardFooter, {\n                        className: \"flex justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: handleBack,\n                                disabled: currentStep === 0,\n                                children: \"Back\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                lineNumber: 289,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: handleNext,\n                                disabled: !isStepValid(),\n                                children: currentStep === steps.length - 1 ? \"Create Portfolio\" : \"Next\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                lineNumber: 296,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                        lineNumber: 288,\n                        columnNumber: 11\n                    }, this),\n                    currentStep === 4 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardFooter, {\n                        className: \"flex justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: handleBack,\n                                children: \"Back\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                lineNumber: 306,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-muted-foreground\",\n                                children: 'Click \"Create Portfolio\" button in the form above to generate your portfolio'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                lineNumber: 312,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                        lineNumber: 305,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                lineNumber: 203,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-8 p-4 border rounded-lg bg-slate-50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_icons__WEBPACK_IMPORTED_MODULE_11__.InfoCircledIcon, {\n                                className: \"h-5 w-5 text-blue-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                lineNumber: 322,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-md font-medium\",\n                                children: \"Admin Information\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                lineNumber: 323,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                        lineNumber: 321,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-muted-foreground mb-2\",\n                        children: \"As an admin, you can upload and manage templates at:\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                        lineNumber: 325,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                        className: \"bg-slate-200 p-2 rounded text-sm block mb-2\",\n                        children: \"/templates\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                        lineNumber: 328,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-muted-foreground\",\n                        children: \"This page allows you to upload Envato Elements templates and configure them for use with the portfolio generator.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                        lineNumber: 331,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                lineNumber: 320,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n        lineNumber: 202,\n        columnNumber: 5\n    }, this);\n}\n_s(OnboardingPage, \"9YGt226IZAs450ATaUl2B60h8z8=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = OnboardingPage;\nvar _c;\n$RefreshReg$(_c, \"OnboardingPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/onboarding/page.tsx\n"));

/***/ })

});