import { NextRequest, NextResponse } from 'next/server';
import { getPortfolioById } from '@/services/portfolio-generator';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    
    if (!id) {
      return NextResponse.json(
        { error: 'Portfolio ID is required' },
        { status: 400 }
      );
    }
    
    console.log(`Fetching portfolio data for ID: ${id}`);
    
    const portfolio = await getPortfolioById(id);
    
    if (!portfolio) {
      console.log(`Portfolio not found: ${id}`);
      return NextResponse.json(
        { error: 'Portfolio not found' },
        { status: 404 }
      );
    }
    
    console.log(`Portfolio found:`, {
      id: portfolio.id,
      name: portfolio.content?.name || 'No name',
      title: portfolio.content?.title || 'No title',
      hasContent: !!portfolio.content
    });
    
    return NextResponse.json({
      success: true,
      portfolio: {
        id: portfolio.id,
        template: portfolio.template,
        profession: portfolio.profession,
        colors: portfolio.colors,
        sections: portfolio.sections,
        content: portfolio.content,
        status: portfolio.status,
        createdAt: portfolio.createdAt,
        updatedAt: portfolio.updatedAt
      }
    });
    
  } catch (error) {
    console.error('Error fetching portfolio data:', error);
    return NextResponse.json(
      { error: 'Failed to fetch portfolio data' },
      { status: 500 }
    );
  }
}
