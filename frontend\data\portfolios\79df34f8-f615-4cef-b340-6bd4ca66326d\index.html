<!DOCTYPE html><html lang="en"><head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>sssjacjnsashbh - Portfolio</title>
    <style>
        :root {
            --primary-color: #059669;
            --secondary-color: #4b5563;
            --accent-color: #d97706;
            --text-color: #333;
            --light-bg: #f8f9fa;
            --dark-bg: #1a202c;
            --border-color: #e2e8f0;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            background-color: var(--light-bg);
        }
        
        a {
            color: var(--primary-color);
            text-decoration: none;
        }
        
        a:hover {
            text-decoration: underline;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        header {
            padding: 80px 0;
            text-align: center;
            background-color: var(--primary-color);
            color: white;
        }
        
        header h1 {
            font-size: 3rem;
            margin-bottom: 10px;
        }
        
        header p {
            font-size: 1.5rem;
            opacity: 0.9;
        }
        
        section {
            padding: 80px 0;
        }
        
        section h2 {
            font-size: 2rem;
            margin-bottom: 40px;
            text-align: center;
            color: var(--secondary-color);
        }
        
        .about {
            background-color: white;
        }
        
        .about-content {
            max-width: 800px;
            margin: 0 auto;
            text-align: center;
        }
        
        .skills-container {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 15px;
            margin-top: 30px;
        }
        
        .skill {
            background-color: var(--primary-color);
            color: white;
            padding: 10px 20px;
            border-radius: 30px;
            font-weight: 500;
        }
        
        .projects-container {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 30px;
            margin-top: 30px;
        }
        
        .project {
            background-color: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        
        .project:hover {
            transform: translateY(-5px);
        }
        
        .project img {
            width: 100%;
            height: 200px;
            object-fit: cover;
        }
        
        .project-content {
            padding: 20px;
        }
        
        .project-title {
            font-size: 1.25rem;
            margin-bottom: 10px;
            color: var(--secondary-color);
        }
        
        .project-description {
            color: #666;
            margin-bottom: 15px;
        }
        
        .project-link {
            display: inline-block;
            padding: 8px 16px;
            background-color: var(--primary-color);
            color: white;
            border-radius: 4px;
            font-weight: 500;
        }
        
        .experience-container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .experience-item {
            margin-bottom: 40px;
            padding-bottom: 40px;
            border-bottom: 1px solid var(--border-color);
        }
        
        .experience-item:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }
        
        .job-title {
            font-size: 1.5rem;
            color: var(--secondary-color);
            margin-bottom: 5px;
        }
        
        .company-name {
            font-size: 1.25rem;
            color: var(--primary-color);
            margin-bottom: 10px;
        }
        
        .job-date {
            font-size: 0.9rem;
            color: #666;
            margin-bottom: 15px;
        }
        
        .contact {
            background-color: var(--secondary-color);
            color: white;
            text-align: center;
        }
        
        .contact-info {
            margin-top: 30px;
        }
        
        .contact-item {
            margin-bottom: 15px;
        }
        
        .contact-item a {
            color: white;
        }
        
        .social-links {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 30px;
        }
        
        .social-link {
            display: inline-block;
            width: 40px;
            height: 40px;
            background-color: white;
            border-radius: 50%;
            color: var(--secondary-color);
            line-height: 40px;
            text-align: center;
            transition: background-color 0.3s ease;
        }
        
        .social-link:hover {
            background-color: var(--accent-color);
            color: white;
        }
        
        footer {
            background-color: var(--dark-bg);
            color: white;
            text-align: center;
            padding: 20px 0;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <h1>sssjacjnsashbh</h1>
            <p>sjksa</p>
        </div>
    </header>
    
    <section class="about" id="about">
        <div class="container">
            <h2>About Me</h2>
            <div class="about-content">
                <p>nj;fjdnsjkbvlbjcbjnfdjsnjnjk</p>
                
                <div class="skills-container">
                    <div class="skill">JavaScript</div>
                    <div class="skill">React</div>
                    <div class="skill">Node.js</div>
                    <div class="skill">HTML/CSS</div>
                    <div class="skill">UI/UX Design</div>
                </div>
            </div>
        </div>
    </section>
    
    <section class="projects" id="projects">
        <div class="container">
            <h2>Projects</h2>
            <div class="projects-container">
                <div class="project">
                    <img src="placeholder-project.jpg" alt="Project 1">
                    <div class="project-content">
                        <h3 class="project-title">Portfolio Website</h3>
                        <p class="project-description">A personal portfolio website built with React and Next.js</p>
                        <a href="#" class="project-link">View Project</a>
                    </div>
                </div>
                
                <div class="project">
                    <img src="placeholder-project.jpg" alt="Project 2">
                    <div class="project-content">
                        <h3 class="project-title">E-commerce Platform</h3>
                        <p class="project-description">A full-stack e-commerce solution with payment integration</p>
                        <a href="#" class="project-link">View Project</a>
                    </div>
                </div>
                
                <div class="project">
                    <img src="placeholder-project.jpg" alt="Project 3">
                    <div class="project-content">
                        <h3 class="project-title">Task Management App</h3>
                        <p class="project-description">A productivity app for managing tasks and projects</p>
                        <a href="#" class="project-link">View Project</a>
                    </div>
                </div>
            </div>
        </div>
    </section>
    
    <section class="experience" id="experience">
        <div class="container">
            <h2>Experience</h2>
            <div class="experience-container"><div class="experience-item">
        <h3 class="job-title">Kuber Industry data analysis dashboard</h3>
        <p class="company-name">egfjnfnjenrjnin</p>
        <p class="job-date">efwejf - ewfjnjwnkjf</p>
        <p class="job-description">sancjsnjnjcnjdnsjkncjknjdsknjkcnjnsldajlcnjnadsjlnj</p>
      </div></div>
        </div>
    </section>
    
    <section class="contact" id="contact">
        <div class="container">
            <h2>Contact Me</h2>
            <div class="contact-info">
                <div class="contact-item">
                    <p>Email: <a href="mailto:<EMAIL>"><EMAIL></a></p>
                </div>
                <div class="contact-item">
                    <p>Phone: 09782203052</p>
                </div>
                <div class="contact-item">
                    <p>Location: San Francisco, CA</p>
                </div>
            </div>
            
            <div class="social-links">
                <a href="https://youtube.com" class="social-link" target="_blank">
                    <i class="fab fa-linkedin-in"></i>
                </a>
                <a href="https://youtube.com" class="social-link" target="_blank">
                    <i class="fab fa-github"></i>
                </a>
                <a href="https://youtube.com" class="social-link" target="_blank">
                    <i class="fab fa-twitter"></i>
                </a>
            </div>
        </div>
    </section>
    
    <footer>
        <div class="container">
            <p>© 2023 sssjacjnsashbh. All rights reserved.</p>
        </div>
    </footer>
    
    <!-- Font Awesome for social icons -->
    <script src="https://kit.fontawesome.com/a076d05399.js" crossorigin="anonymous"></script>

 </body></html>