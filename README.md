# AI-Powered Portfolio SaaS Platform

A modern SaaS platform that generates personalized portfolio websites using AI. Built with Next.js, Node.js, and AI integration.

## Features

- AI-powered portfolio generation
- Customizable templates and themes
- Custom domain integration
- Analytics dashboard
- Premium features and subscription plans

## Project Structure

```
frontend/    - Next.js application
backend/     - Node.js + Express API
shared/      - Shared utilities and types
```

## Prerequisites

- Node.js >= 18
- npm >= 9
- PostgreSQL >= 14

## Getting Started

1. Clone the repository
```bash
git clone [repository-url]
```

2. Install dependencies
```bash
# Install root dependencies
npm install

# Install frontend dependencies
cd frontend
npm install

# Install backend dependencies
cd ../backend
npm install
```

3. Set up environment variables
```bash
# Frontend
cp frontend/.env.example frontend/.env.local

# Backend
cp backend/.env.example backend/.env
```

4. Start development servers
```bash
# Start frontend (from root directory)
npm run dev:frontend

# Start backend (from root directory)
npm run dev:backend
```

## Development

- Frontend runs on: `http://localhost:3000`
- Backend runs on: `http://localhost:8000`

## Contributing

1. Create a new branch for your feature
2. Make your changes
3. Submit a pull request

## License

MIT 