import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    
    if (!id) {
      return NextResponse.json(
        { error: 'Portfolio ID is required' },
        { status: 400 }
      );
    }
    
    // Try to find the HTML file in the data directory
    console.log('Current working directory:', process.cwd());

    // Try different path combinations
    const paths = [
      path.join(process.cwd(), 'data', 'portfolios', id, 'index.html'),
      path.join(process.cwd(), 'frontend', 'data', 'portfolios', id, 'index.html'),
      path.join(process.cwd(), '..', 'data', 'portfolios', id, 'index.html'),
      path.join(process.cwd(), '..', 'frontend', 'data', 'portfolios', id, 'index.html')
    ];

    let htmlPath = null;
    for (const testPath of paths) {
      console.log('Testing path:', testPath);
      if (fs.existsSync(testPath)) {
        htmlPath = testPath;
        console.log('Found HTML file at:', htmlPath);
        break;
      }
    }

    if (!htmlPath) {
      console.log('HTML file not found in any of the tested paths');
      console.log('Looking for HTML file at:', paths.map(p => `${p} (exists: ${fs.existsSync(p)})`));
    }
    
    if (!htmlPath) {
      return NextResponse.json(
        { error: 'Portfolio HTML not found' },
        { status: 404 }
      );
    }

    // Read the HTML file
    const htmlContent = fs.readFileSync(htmlPath, 'utf-8');
    
    // Return the HTML content
    return new NextResponse(htmlContent, {
      headers: {
        'Content-Type': 'text/html',
      },
    });
    
  } catch (error) {
    console.error('Error serving portfolio HTML:', error);
    return NextResponse.json(
      { error: 'Failed to serve portfolio' },
      { status: 500 }
    );
  }
}
