import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    
    if (!id) {
      return NextResponse.json(
        { error: 'Portfolio ID is required' },
        { status: 400 }
      );
    }
    
    // Try to find the HTML file in the data directory
    const htmlPath = path.join(process.cwd(), 'frontend', 'data', 'portfolios', id, 'index.html');
    
    if (!fs.existsSync(htmlPath)) {
      return NextResponse.json(
        { error: 'Portfolio HTML not found' },
        { status: 404 }
      );
    }
    
    // Read the HTML file
    const htmlContent = fs.readFileSync(htmlPath, 'utf-8');
    
    // Return the HTML content
    return new NextResponse(htmlContent, {
      headers: {
        'Content-Type': 'text/html',
      },
    });
    
  } catch (error) {
    console.error('Error serving portfolio HTML:', error);
    return NextResponse.json(
      { error: 'Failed to serve portfolio' },
      { status: 500 }
    );
  }
}
