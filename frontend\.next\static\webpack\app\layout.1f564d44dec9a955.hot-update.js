/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CHIMANSHU%5COneDrive%5CDesktop%5Cportfolio%20saas%20-%20Copy%5Cfrontend%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CHIMANSHU%5COneDrive%5CDesktop%5Cportfolio%20saas%20-%20Copy%5Cfrontend%5Csrc%5Ccomponents%5Cnavbar.tsx&modules=C%3A%5CUsers%5CHIMANSHU%5COneDrive%5CDesktop%5Cportfolio%20saas%20-%20Copy%5Cfrontend%5Csrc%5Ccomponents%5Ctheme-provider.tsx&modules=C%3A%5CUsers%5CHIMANSHU%5COneDrive%5CDesktop%5Cportfolio%20saas%20-%20Copy%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&server=false!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CHIMANSHU%5COneDrive%5CDesktop%5Cportfolio%20saas%20-%20Copy%5Cfrontend%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CHIMANSHU%5COneDrive%5CDesktop%5Cportfolio%20saas%20-%20Copy%5Cfrontend%5Csrc%5Ccomponents%5Cnavbar.tsx&modules=C%3A%5CUsers%5CHIMANSHU%5COneDrive%5CDesktop%5Cportfolio%20saas%20-%20Copy%5Cfrontend%5Csrc%5Ccomponents%5Ctheme-provider.tsx&modules=C%3A%5CUsers%5CHIMANSHU%5COneDrive%5CDesktop%5Cportfolio%20saas%20-%20Copy%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&server=false! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/globals.css */ \"(app-pages-browser)/./src/app/globals.css\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/navbar.tsx */ \"(app-pages-browser)/./src/components/navbar.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/theme-provider.tsx */ \"(app-pages-browser)/./src/components/theme-provider.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(app-pages-browser)/../node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CHIMANSHU%5COneDrive%5CDesktop%5Cportfolio%20saas%20-%20Copy%5Cfrontend%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CHIMANSHU%5COneDrive%5CDesktop%5Cportfolio%20saas%20-%20Copy%5Cfrontend%5Csrc%5Ccomponents%5Cnavbar.tsx&modules=C%3A%5CUsers%5CHIMANSHU%5COneDrive%5CDesktop%5Cportfolio%20saas%20-%20Copy%5Cfrontend%5Csrc%5Ccomponents%5Ctheme-provider.tsx&modules=C%3A%5CUsers%5CHIMANSHU%5COneDrive%5CDesktop%5Cportfolio%20saas%20-%20Copy%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&server=false!\n"));

/***/ })

});