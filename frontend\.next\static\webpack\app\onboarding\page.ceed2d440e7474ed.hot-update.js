"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/onboarding/page",{

/***/ "(app-pages-browser)/./src/components/onboarding/content-form.tsx":
/*!****************************************************!*\
  !*** ./src/components/onboarding/content-form.tsx ***!
  \****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ContentForm: function() { return /* binding */ ContentForm; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/../node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! zod */ \"(app-pages-browser)/../node_modules/zod/lib/index.mjs\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_form__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/form */ \"(app-pages-browser)/./src/components/ui/form.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* __next_internal_client_entry_do_not_use__ ContentForm auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst contentSchema = zod__WEBPACK_IMPORTED_MODULE_9__.object({\n    name: zod__WEBPACK_IMPORTED_MODULE_9__.string().min(2, \"Name must be at least 2 characters\"),\n    title: zod__WEBPACK_IMPORTED_MODULE_9__.string().min(2, \"Title must be at least 2 characters\"),\n    bio: zod__WEBPACK_IMPORTED_MODULE_9__.string().min(10, \"Bio must be at least 10 characters\"),\n    skills: zod__WEBPACK_IMPORTED_MODULE_9__.array(zod__WEBPACK_IMPORTED_MODULE_9__.string()).min(1, \"Add at least one skill\"),\n    experience: zod__WEBPACK_IMPORTED_MODULE_9__.array(zod__WEBPACK_IMPORTED_MODULE_9__.object({\n        title: zod__WEBPACK_IMPORTED_MODULE_9__.string().min(2, \"Title must be at least 2 characters\"),\n        company: zod__WEBPACK_IMPORTED_MODULE_9__.string().min(2, \"Company must be at least 2 characters\"),\n        startDate: zod__WEBPACK_IMPORTED_MODULE_9__.string().min(2, \"Start date is required\"),\n        endDate: zod__WEBPACK_IMPORTED_MODULE_9__.string().optional(),\n        description: zod__WEBPACK_IMPORTED_MODULE_9__.string().min(10, \"Description must be at least 10 characters\")\n    })).min(0),\n    projects: zod__WEBPACK_IMPORTED_MODULE_9__.array(zod__WEBPACK_IMPORTED_MODULE_9__.object({\n        title: zod__WEBPACK_IMPORTED_MODULE_9__.string().min(2, \"Title must be at least 2 characters\"),\n        description: zod__WEBPACK_IMPORTED_MODULE_9__.string().min(10, \"Description must be at least 10 characters\"),\n        link: zod__WEBPACK_IMPORTED_MODULE_9__.string().url(\"Must be a valid URL\").optional()\n    })).min(0),\n    email: zod__WEBPACK_IMPORTED_MODULE_9__.string().email(\"Must be a valid email\").optional(),\n    phone: zod__WEBPACK_IMPORTED_MODULE_9__.string().optional(),\n    social: zod__WEBPACK_IMPORTED_MODULE_9__.object({\n        linkedin: zod__WEBPACK_IMPORTED_MODULE_9__.string().url(\"Must be a valid URL\").optional(),\n        github: zod__WEBPACK_IMPORTED_MODULE_9__.string().url(\"Must be a valid URL\").optional(),\n        twitter: zod__WEBPACK_IMPORTED_MODULE_9__.string().url(\"Must be a valid URL\").optional()\n    })\n});\nfunction ContentForm(param) {\n    let { defaultValues, onSubmit } = param;\n    _s();\n    const form = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_10__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__.zodResolver)(contentSchema),\n        defaultValues: defaultValues || {\n            name: \"\",\n            title: \"\",\n            bio: \"\",\n            skills: [],\n            experience: [],\n            projects: [],\n            email: \"\",\n            phone: \"\",\n            social: {\n                linkedin: \"\",\n                github: \"\",\n                twitter: \"\"\n            }\n        }\n    });\n    const handleFormSubmit = ()=>{\n        console.log(\"=== FORM SUBMIT FUNCTION CALLED ===\");\n        const currentValues = form.getValues();\n        console.log(\"Current form values:\", currentValues);\n        // Validate form\n        const isValid = form.trigger();\n        console.log(\"Form is valid:\", isValid);\n        if (isValid) {\n            console.log(\"Submitting form with values:\", currentValues);\n            onSubmit(currentValues);\n        } else {\n            console.log(\"Form validation failed\");\n        }\n    };\n    const { fields: experienceFields, append: appendExperience, remove: removeExperience } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_10__.useFieldArray)({\n        control: form.control,\n        name: \"experience\"\n    });\n    const { fields: projectFields, append: appendProject, remove: removeProject } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_10__.useFieldArray)({\n        control: form.control,\n        name: \"projects\"\n    });\n    const [newSkill, setNewSkill] = react__WEBPACK_IMPORTED_MODULE_1__.useState(\"\");\n    const addSkill = ()=>{\n        if (newSkill.trim()) {\n            const currentSkills = form.getValues(\"skills\");\n            form.setValue(\"skills\", [\n                ...currentSkills,\n                newSkill.trim()\n            ]);\n            setNewSkill(\"\");\n        }\n    };\n    const removeSkill = (index)=>{\n        const currentSkills = form.getValues(\"skills\");\n        form.setValue(\"skills\", currentSkills.filter((_, i)=>i !== index));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.Form, {\n        ...form,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n            onSubmit: form.handleSubmit(handleFormSubmit),\n            className: \"space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                            control: form.control,\n                            name: \"name\",\n                            render: (param)=>{\n                                let { field } = param;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                            children: \"Full Name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                            lineNumber: 131,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                placeholder: \"John Doe\",\n                                                ...field\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                lineNumber: 133,\n                                                columnNumber: 19\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                            lineNumber: 135,\n                                            columnNumber: 17\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 15\n                                }, void 0);\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                            control: form.control,\n                            name: \"title\",\n                            render: (param)=>{\n                                let { field } = param;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                            children: \"Professional Title\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                placeholder: \"Senior Software Engineer\",\n                                                ...field\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                lineNumber: 147,\n                                                columnNumber: 19\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 17\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 15\n                                }, void 0);\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                            lineNumber: 140,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                            control: form.control,\n                            name: \"bio\",\n                            render: (param)=>{\n                                let { field } = param;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                            children: \"Bio\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                                placeholder: \"Tell us about yourself...\",\n                                                ...field\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                lineNumber: 161,\n                                                columnNumber: 19\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 17\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 15\n                                }, void 0);\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                            lineNumber: 154,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                    children: \"Skills\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                            value: newSkill,\n                                            onChange: (e)=>setNewSkill(e.target.value),\n                                            placeholder: \"Add a skill\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            type: \"button\",\n                                            onClick: addSkill,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-2\",\n                                    children: form.watch(\"skills\").map((skill, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-1 bg-secondary text-secondary-foreground px-2 py-1 rounded-md\",\n                                            children: [\n                                                skill,\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    type: \"button\",\n                                                    variant: \"ghost\",\n                                                    size: \"icon\",\n                                                    className: \"h-4 w-4 p-0 hover:bg-transparent\",\n                                                    onClick: ()=>removeSkill(index),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-3 w-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                        lineNumber: 197,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                    lineNumber: 190,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                            children: \"Experience\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            type: \"button\",\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            onClick: ()=>appendExperience({\n                                                    title: \"\",\n                                                    company: \"\",\n                                                    startDate: \"\",\n                                                    endDate: \"\",\n                                                    description: \"\"\n                                                }),\n                                            children: \"Add Experience\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                    lineNumber: 205,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: experienceFields.map((field, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                                className: \"pt-6 space-y-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1 grid gap-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                                                                    control: form.control,\n                                                                    name: \"experience.\".concat(index, \".title\"),\n                                                                    render: (param)=>{\n                                                                        let { field } = param;\n                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                                                    children: \"Title\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 235,\n                                                                                    columnNumber: 31\n                                                                                }, void 0),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                        ...field\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                        lineNumber: 237,\n                                                                                        columnNumber: 33\n                                                                                    }, void 0)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 236,\n                                                                                    columnNumber: 31\n                                                                                }, void 0),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 239,\n                                                                                    columnNumber: 31\n                                                                                }, void 0)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                            lineNumber: 234,\n                                                                            columnNumber: 29\n                                                                        }, void 0);\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                    lineNumber: 230,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                                                                    control: form.control,\n                                                                    name: \"experience.\".concat(index, \".company\"),\n                                                                    render: (param)=>{\n                                                                        let { field } = param;\n                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                                                    children: \"Company\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 248,\n                                                                                    columnNumber: 31\n                                                                                }, void 0),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                        ...field\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                        lineNumber: 250,\n                                                                                        columnNumber: 33\n                                                                                    }, void 0)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 249,\n                                                                                    columnNumber: 31\n                                                                                }, void 0),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 252,\n                                                                                    columnNumber: 31\n                                                                                }, void 0)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                            lineNumber: 247,\n                                                                            columnNumber: 29\n                                                                        }, void 0);\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                    lineNumber: 243,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                                                                    control: form.control,\n                                                                    name: \"experience.\".concat(index, \".startDate\"),\n                                                                    render: (param)=>{\n                                                                        let { field } = param;\n                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                                                    children: \"Start Date\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 261,\n                                                                                    columnNumber: 31\n                                                                                }, void 0),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                        ...field,\n                                                                                        placeholder: \"e.g., January 2020\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                        lineNumber: 263,\n                                                                                        columnNumber: 33\n                                                                                    }, void 0)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 262,\n                                                                                    columnNumber: 31\n                                                                                }, void 0),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 265,\n                                                                                    columnNumber: 31\n                                                                                }, void 0)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                            lineNumber: 260,\n                                                                            columnNumber: 29\n                                                                        }, void 0);\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                    lineNumber: 256,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                                                                    control: form.control,\n                                                                    name: \"experience.\".concat(index, \".endDate\"),\n                                                                    render: (param)=>{\n                                                                        let { field } = param;\n                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                                                    children: \"End Date (Optional)\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 274,\n                                                                                    columnNumber: 31\n                                                                                }, void 0),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                        ...field,\n                                                                                        value: field.value || \"\",\n                                                                                        placeholder: \"e.g., Present or December 2023\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                        lineNumber: 276,\n                                                                                        columnNumber: 33\n                                                                                    }, void 0)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 275,\n                                                                                    columnNumber: 31\n                                                                                }, void 0),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 278,\n                                                                                    columnNumber: 31\n                                                                                }, void 0)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                            lineNumber: 273,\n                                                                            columnNumber: 29\n                                                                        }, void 0);\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                    lineNumber: 269,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                                                                    control: form.control,\n                                                                    name: \"experience.\".concat(index, \".description\"),\n                                                                    render: (param)=>{\n                                                                        let { field } = param;\n                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                                                    children: \"Description\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 287,\n                                                                                    columnNumber: 31\n                                                                                }, void 0),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                                                                        ...field\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                        lineNumber: 289,\n                                                                                        columnNumber: 33\n                                                                                    }, void 0)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 288,\n                                                                                    columnNumber: 31\n                                                                                }, void 0),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 291,\n                                                                                    columnNumber: 31\n                                                                                }, void 0)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                            lineNumber: 286,\n                                                                            columnNumber: 29\n                                                                        }, void 0);\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                    lineNumber: 282,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                            lineNumber: 229,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            type: \"button\",\n                                                            variant: \"ghost\",\n                                                            size: \"icon\",\n                                                            onClick: ()=>removeExperience(index),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                lineNumber: 302,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                            lineNumber: 296,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                    lineNumber: 228,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                lineNumber: 227,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, field.id, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                            lineNumber: 226,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                    lineNumber: 224,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                            lineNumber: 204,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                            children: \"Projects\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                            lineNumber: 313,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            type: \"button\",\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            onClick: ()=>appendProject({\n                                                    title: \"\",\n                                                    description: \"\",\n                                                    link: \"\"\n                                                }),\n                                            children: \"Add Project\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                            lineNumber: 314,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                    lineNumber: 312,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: projectFields.map((field, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                                className: \"pt-6 space-y-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1 grid gap-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                                                                    control: form.control,\n                                                                    name: \"projects.\".concat(index, \".title\"),\n                                                                    render: (param)=>{\n                                                                        let { field } = param;\n                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                                                    children: \"Title\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 340,\n                                                                                    columnNumber: 31\n                                                                                }, void 0),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                        ...field\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                        lineNumber: 342,\n                                                                                        columnNumber: 33\n                                                                                    }, void 0)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 341,\n                                                                                    columnNumber: 31\n                                                                                }, void 0),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 344,\n                                                                                    columnNumber: 31\n                                                                                }, void 0)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                            lineNumber: 339,\n                                                                            columnNumber: 29\n                                                                        }, void 0);\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                    lineNumber: 335,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                                                                    control: form.control,\n                                                                    name: \"projects.\".concat(index, \".description\"),\n                                                                    render: (param)=>{\n                                                                        let { field } = param;\n                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                                                    children: \"Description\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 353,\n                                                                                    columnNumber: 31\n                                                                                }, void 0),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                                                                        ...field\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                        lineNumber: 355,\n                                                                                        columnNumber: 33\n                                                                                    }, void 0)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 354,\n                                                                                    columnNumber: 31\n                                                                                }, void 0),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 357,\n                                                                                    columnNumber: 31\n                                                                                }, void 0)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                            lineNumber: 352,\n                                                                            columnNumber: 29\n                                                                        }, void 0);\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                    lineNumber: 348,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                                                                    control: form.control,\n                                                                    name: \"projects.\".concat(index, \".link\"),\n                                                                    render: (param)=>{\n                                                                        let { field } = param;\n                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                                                    children: \"Link\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 366,\n                                                                                    columnNumber: 31\n                                                                                }, void 0),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                        type: \"url\",\n                                                                                        ...field,\n                                                                                        value: field.value || \"\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                        lineNumber: 368,\n                                                                                        columnNumber: 33\n                                                                                    }, void 0)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 367,\n                                                                                    columnNumber: 31\n                                                                                }, void 0),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 370,\n                                                                                    columnNumber: 31\n                                                                                }, void 0)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                            lineNumber: 365,\n                                                                            columnNumber: 29\n                                                                        }, void 0);\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                    lineNumber: 361,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                            lineNumber: 334,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            type: \"button\",\n                                                            variant: \"ghost\",\n                                                            size: \"icon\",\n                                                            onClick: ()=>removeProject(index),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                lineNumber: 381,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                            lineNumber: 375,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                    lineNumber: 333,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                lineNumber: 332,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, field.id, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                            lineNumber: 331,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                    lineNumber: 329,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                            lineNumber: 311,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                    children: \"Contact Information\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                    lineNumber: 391,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                                            control: form.control,\n                                            name: \"email\",\n                                            render: (param)=>{\n                                                let { field } = param;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                            children: \"Email\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                            lineNumber: 398,\n                                                            columnNumber: 21\n                                                        }, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                type: \"email\",\n                                                                ...field,\n                                                                value: field.value || \"\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                lineNumber: 400,\n                                                                columnNumber: 23\n                                                            }, void 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                            lineNumber: 399,\n                                                            columnNumber: 21\n                                                        }, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                            lineNumber: 402,\n                                                            columnNumber: 21\n                                                        }, void 0)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                    lineNumber: 397,\n                                                    columnNumber: 19\n                                                }, void 0);\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                            lineNumber: 393,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                                            control: form.control,\n                                            name: \"phone\",\n                                            render: (param)=>{\n                                                let { field } = param;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                            children: \"Phone (Optional)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                            lineNumber: 411,\n                                                            columnNumber: 21\n                                                        }, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                type: \"tel\",\n                                                                ...field,\n                                                                value: field.value || \"\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                lineNumber: 413,\n                                                                columnNumber: 23\n                                                            }, void 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                            lineNumber: 412,\n                                                            columnNumber: 21\n                                                        }, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                            lineNumber: 415,\n                                                            columnNumber: 21\n                                                        }, void 0)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                    lineNumber: 410,\n                                                    columnNumber: 19\n                                                }, void 0);\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                            lineNumber: 406,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                    lineNumber: 392,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                            lineNumber: 390,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                    children: \"Social Links\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                    lineNumber: 423,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                                            control: form.control,\n                                            name: \"social.linkedin\",\n                                            render: (param)=>{\n                                                let { field } = param;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                            children: \"LinkedIn\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                            lineNumber: 430,\n                                                            columnNumber: 21\n                                                        }, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                type: \"url\",\n                                                                ...field,\n                                                                value: field.value || \"\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                lineNumber: 432,\n                                                                columnNumber: 23\n                                                            }, void 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                            lineNumber: 431,\n                                                            columnNumber: 21\n                                                        }, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                            lineNumber: 434,\n                                                            columnNumber: 21\n                                                        }, void 0)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                    lineNumber: 429,\n                                                    columnNumber: 19\n                                                }, void 0);\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                            lineNumber: 425,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                                            control: form.control,\n                                            name: \"social.github\",\n                                            render: (param)=>{\n                                                let { field } = param;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                            children: \"GitHub\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                            lineNumber: 443,\n                                                            columnNumber: 21\n                                                        }, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                type: \"url\",\n                                                                ...field,\n                                                                value: field.value || \"\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                lineNumber: 445,\n                                                                columnNumber: 23\n                                                            }, void 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                            lineNumber: 444,\n                                                            columnNumber: 21\n                                                        }, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                            lineNumber: 447,\n                                                            columnNumber: 21\n                                                        }, void 0)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                    lineNumber: 442,\n                                                    columnNumber: 19\n                                                }, void 0);\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                            lineNumber: 438,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                                            control: form.control,\n                                            name: \"social.twitter\",\n                                            render: (param)=>{\n                                                let { field } = param;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                            children: \"Twitter\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                            lineNumber: 456,\n                                                            columnNumber: 21\n                                                        }, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                type: \"url\",\n                                                                ...field,\n                                                                value: field.value || \"\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                lineNumber: 458,\n                                                                columnNumber: 23\n                                                            }, void 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                            lineNumber: 457,\n                                                            columnNumber: 21\n                                                        }, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                            lineNumber: 460,\n                                                            columnNumber: 21\n                                                        }, void 0)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                    lineNumber: 455,\n                                                    columnNumber: 19\n                                                }, void 0);\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                            lineNumber: 451,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                    lineNumber: 424,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                            lineNumber: 422,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                    lineNumber: 125,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-end\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        type: \"button\",\n                        className: \"bg-green-600 hover:bg-green-700\",\n                        onClick: (e)=>{\n                            console.log(\"=== BUTTON CLICKED ===\");\n                            console.log(\"Form errors:\", form.formState.errors);\n                            console.log(\"Form is valid:\", form.formState.isValid);\n                            const formValues = form.getValues();\n                            console.log(\"Form values:\", formValues);\n                            // Manually trigger form submission with current values\n                            handleFormSubmit(formValues);\n                        },\n                        children: \"\\uD83D\\uDE80 Create Portfolio\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                        lineNumber: 469,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                    lineNumber: 468,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n            lineNumber: 124,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n        lineNumber: 123,\n        columnNumber: 5\n    }, this);\n}\n_s(ContentForm, \"CUNbDDiudl1iQFH+kZW4keFRSFc=\", false, function() {\n    return [\n        react_hook_form__WEBPACK_IMPORTED_MODULE_10__.useForm,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_10__.useFieldArray,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_10__.useFieldArray\n    ];\n});\n_c = ContentForm;\nvar _c;\n$RefreshReg$(_c, \"ContentForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/onboarding/content-form.tsx\n"));

/***/ })

});