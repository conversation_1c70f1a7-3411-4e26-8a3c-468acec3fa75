"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/onboarding/page",{

/***/ "(app-pages-browser)/./src/app/onboarding/page.tsx":
/*!*************************************!*\
  !*** ./src/app/onboarding/page.tsx ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ OnboardingPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/../node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_create_steps__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/create/steps */ \"(app-pages-browser)/./src/components/create/steps.tsx\");\n/* harmony import */ var _components_onboarding_profession_selector__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/onboarding/profession-selector */ \"(app-pages-browser)/./src/components/onboarding/profession-selector.tsx\");\n/* harmony import */ var _components_onboarding_template_selector__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/onboarding/template-selector */ \"(app-pages-browser)/./src/components/onboarding/template-selector.tsx\");\n/* harmony import */ var _components_onboarding_color_palette__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/onboarding/color-palette */ \"(app-pages-browser)/./src/components/onboarding/color-palette.tsx\");\n/* harmony import */ var _components_onboarding_section_config__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/onboarding/section-config */ \"(app-pages-browser)/./src/components/onboarding/section-config.tsx\");\n/* harmony import */ var _components_onboarding_content_form__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/onboarding/content-form */ \"(app-pages-browser)/./src/components/onboarding/content-form.tsx\");\n/* harmony import */ var _radix_ui_react_icons__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @radix-ui/react-icons */ \"(app-pages-browser)/../node_modules/@radix-ui/react-icons/dist/react-icons.esm.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst steps = [\n    {\n        id: \"profession\",\n        title: \"Profession\",\n        description: \"Select your profession\"\n    },\n    {\n        id: \"template\",\n        title: \"Template\",\n        description: \"Choose a template\"\n    },\n    {\n        id: \"colors\",\n        title: \"Colors\",\n        description: \"Pick your brand colors\"\n    },\n    {\n        id: \"sections\",\n        title: \"Sections\",\n        description: \"Customize sections\"\n    },\n    {\n        id: \"content\",\n        title: \"Content\",\n        description: \"Add your content\"\n    }\n];\nfunction OnboardingPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [currentStep, setCurrentStep] = react__WEBPACK_IMPORTED_MODULE_1__.useState(0);\n    const [formData, setFormData] = react__WEBPACK_IMPORTED_MODULE_1__.useState({\n        profession: \"\",\n        template: \"\",\n        colors: {\n            primary: \"#0070f3\",\n            secondary: \"#6b7280\",\n            accent: \"#f59e0b\"\n        },\n        sections: [\n            {\n                id: \"hero\",\n                name: \"Hero\",\n                description: \"Introduction and main headline\",\n                isRequired: true,\n                isEnabled: true\n            },\n            {\n                id: \"about\",\n                name: \"About\",\n                description: \"Personal bio and background\",\n                isRequired: true,\n                isEnabled: true\n            },\n            {\n                id: \"experience\",\n                name: \"Experience\",\n                description: \"Work history and achievements\",\n                isEnabled: true\n            },\n            {\n                id: \"projects\",\n                name: \"Projects\",\n                description: \"Showcase of your work\",\n                isEnabled: true\n            },\n            {\n                id: \"skills\",\n                name: \"Skills\",\n                description: \"Technical and professional skills\",\n                isEnabled: true\n            },\n            {\n                id: \"testimonials\",\n                name: \"Testimonials\",\n                description: \"Client and colleague reviews\",\n                isEnabled: false\n            },\n            {\n                id: \"blog\",\n                name: \"Blog\",\n                description: \"Articles and thoughts\",\n                isEnabled: false\n            },\n            {\n                id: \"contact\",\n                name: \"Contact\",\n                description: \"Contact information and form\",\n                isRequired: true,\n                isEnabled: true\n            }\n        ],\n        content: {\n            name: \"\",\n            title: \"\",\n            bio: \"\",\n            skills: [],\n            experience: [],\n            projects: [],\n            email: \"\",\n            phone: \"\",\n            social: {\n                linkedin: \"\",\n                github: \"\",\n                twitter: \"\"\n            }\n        }\n    });\n    const handleNext = ()=>{\n        if (currentStep < steps.length - 1) {\n            setCurrentStep(currentStep + 1);\n        } else {\n            handleSubmit();\n        }\n    };\n    const handleBack = ()=>{\n        if (currentStep > 0) {\n            setCurrentStep(currentStep - 1);\n        }\n    };\n    const handleSubmit = async ()=>{\n        try {\n            console.log(\"Starting portfolio creation...\");\n            console.log(\"Form data:\", formData);\n            // Call the portfolio generation API\n            const response = await fetch(\"/api/portfolio/generate\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    profession: formData.profession,\n                    template: formData.template,\n                    colors: formData.colors,\n                    sections: formData.sections,\n                    content: formData.content\n                })\n            });\n            console.log(\"API response status:\", response.status);\n            const data = await response.json();\n            console.log(\"API response data:\", data);\n            if (data.success) {\n                console.log(\"Portfolio created successfully, redirecting to:\", data.portfolioUrl);\n                // Redirect to the generated portfolio\n                router.push(data.portfolioUrl);\n            } else {\n                console.error(\"Error generating portfolio:\", data.error);\n                alert(\"Error creating portfolio: \" + (data.error || \"Unknown error\"));\n            }\n        } catch (error) {\n            console.error(\"Error saving onboarding data:\", error);\n            alert(\"Error creating portfolio: \" + error.message);\n        }\n    };\n    const isStepValid = ()=>{\n        switch(currentStep){\n            case 0:\n                return !!formData.profession;\n            case 1:\n                return !!formData.template;\n            case 2:\n                return !!formData.colors.primary;\n            case 3:\n                return formData.sections.length > 0;\n            case 4:\n                return !!formData.content.name;\n            default:\n                return false;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"container max-w-4xl py-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                className: \"w-full\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                children: \"Create Your Portfolio\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                children: \"Follow these steps to create your personalized portfolio website\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                        lineNumber: 203,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        className: \"space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_create_steps__WEBPACK_IMPORTED_MODULE_5__.Steps, {\n                                steps: steps,\n                                currentStep: currentStep\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-8\",\n                                children: [\n                                    currentStep === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium\",\n                                                children: \"What's your profession?\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                                lineNumber: 216,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_onboarding_profession_selector__WEBPACK_IMPORTED_MODULE_6__.ProfessionSelector, {\n                                                value: formData.profession,\n                                                onChange: (value)=>setFormData({\n                                                        ...formData,\n                                                        profession: value\n                                                    })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                                lineNumber: 217,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 15\n                                    }, this),\n                                    currentStep === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium\",\n                                                children: \"Choose a template for your portfolio\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                                lineNumber: 227,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-muted-foreground\",\n                                                children: \"Select a template that best represents your professional style.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_onboarding_template_selector__WEBPACK_IMPORTED_MODULE_7__.TemplateSelector, {\n                                                value: formData.template,\n                                                onChange: (value)=>setFormData({\n                                                        ...formData,\n                                                        template: value\n                                                    })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                                lineNumber: 229,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 15\n                                    }, this),\n                                    currentStep === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium\",\n                                                children: \"Choose your brand colors\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                                lineNumber: 239,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-muted-foreground\",\n                                                children: \"Select a color palette or customize your own colors.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                                lineNumber: 240,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_onboarding_color_palette__WEBPACK_IMPORTED_MODULE_8__.ColorPalette, {\n                                                colors: formData.colors,\n                                                onChange: (colors)=>setFormData({\n                                                        ...formData,\n                                                        colors\n                                                    })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 15\n                                    }, this),\n                                    currentStep === 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium\",\n                                                children: \"Customize your portfolio sections\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                                lineNumber: 251,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-muted-foreground\",\n                                                children: \"Select and arrange the sections you want to include.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                                lineNumber: 252,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_onboarding_section_config__WEBPACK_IMPORTED_MODULE_9__.SectionConfig, {\n                                                sections: formData.sections,\n                                                onChange: (sections)=>setFormData({\n                                                        ...formData,\n                                                        sections\n                                                    })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                                lineNumber: 253,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 15\n                                    }, this),\n                                    currentStep === 4 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium\",\n                                                children: \"Add your personal information\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                                lineNumber: 263,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-muted-foreground\",\n                                                children: \"Fill in your details to personalize your portfolio.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                                lineNumber: 264,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_onboarding_content_form__WEBPACK_IMPORTED_MODULE_10__.ContentForm, {\n                                                defaultValues: formData.content,\n                                                onSubmit: (content)=>{\n                                                    setFormData({\n                                                        ...formData,\n                                                        content: content\n                                                    });\n                                                    if (currentStep === steps.length - 1) {\n                                                        handleSubmit();\n                                                    } else {\n                                                        handleNext();\n                                                    }\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                                lineNumber: 265,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 9\n                    }, this),\n                    currentStep !== 4 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardFooter, {\n                        className: \"flex justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: handleBack,\n                                disabled: currentStep === 0,\n                                children: \"Back\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                lineNumber: 283,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: handleNext,\n                                disabled: !isStepValid(),\n                                children: currentStep === steps.length - 1 ? \"Create Portfolio\" : \"Next\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                lineNumber: 290,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                        lineNumber: 282,\n                        columnNumber: 11\n                    }, this),\n                    currentStep === 4 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardFooter, {\n                        className: \"flex justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: handleBack,\n                                children: \"Back\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                lineNumber: 300,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-muted-foreground\",\n                                children: 'Click \"Create Portfolio\" button in the form above to generate your portfolio'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                lineNumber: 306,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                        lineNumber: 299,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                lineNumber: 202,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-8 p-4 border rounded-lg bg-slate-50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_icons__WEBPACK_IMPORTED_MODULE_11__.InfoCircledIcon, {\n                                className: \"h-5 w-5 text-blue-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-md font-medium\",\n                                children: \"Admin Information\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                lineNumber: 317,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                        lineNumber: 315,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-muted-foreground mb-2\",\n                        children: \"As an admin, you can upload and manage templates at:\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                        lineNumber: 319,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                        className: \"bg-slate-200 p-2 rounded text-sm block mb-2\",\n                        children: \"/templates\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                        lineNumber: 322,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-muted-foreground\",\n                        children: \"This page allows you to upload Envato Elements templates and configure them for use with the portfolio generator.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                        lineNumber: 325,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                lineNumber: 314,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n        lineNumber: 201,\n        columnNumber: 5\n    }, this);\n}\n_s(OnboardingPage, \"9YGt226IZAs450ATaUl2B60h8z8=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = OnboardingPage;\nvar _c;\n$RefreshReg$(_c, \"OnboardingPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/onboarding/page.tsx\n"));

/***/ })

});