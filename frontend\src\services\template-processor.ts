import fs from 'fs';
import path from 'path';
import { JSDOM } from 'jsdom';
import { v4 as uuidv4 } from 'uuid';

interface TemplateConfig {
  id: string;
  name: string;
  description: string;
  folderPath: string;
  mainFile: string;
  previewImage: string;
  placeholders: {
    name: string[];
    title: string[];
    bio: string[];
    skills: string[];
    projects: string[];
    experience: string[];
    contact: string[];
    social: string[];
    [key: string]: string[];
  };
}

interface PortfolioContent {
  name: string;
  title: string;
  bio: string;
  skills: string[];
  experience: Array<{
    title: string;
    company: string;
    description: string;
    startDate: string;
    endDate?: string;
  }>;
  projects: Array<{
    title: string;
    description: string;
    image?: string;
    link?: string;
  }>;
  social: {
    linkedin?: string;
    github?: string;
    twitter?: string;
    other?: string;
  };
  [key: string]: any;
}

interface ColorScheme {
  primary: string;
  secondary: string;
  accent: string;
}

/**
 * Loads template configuration from the template directory
 */
export async function loadTemplateConfig(templateId: string): Promise<TemplateConfig | null> {
  try {
    // Try different possible paths for the template config
    const possiblePaths = [
      // From project root
      path.join(process.cwd(), 'templates', 'html-templates', templateId, 'config.json'),
      // From parent directory (when running from frontend folder)
      path.join(process.cwd(), '..', 'templates', 'html-templates', templateId, 'config.json'),
      // From frontend public directory
      path.join(process.cwd(), 'frontend', 'public', 'templates', 'html-templates', templateId, 'config.json'),
      // Alternative frontend path
      path.join(process.cwd(), 'frontend', 'templates', 'html-templates', templateId, 'config.json')
    ];

    for (const configPath of possiblePaths) {
      if (fs.existsSync(configPath)) {
        console.log(`Loading template config from: ${configPath}`);
        const configData = fs.readFileSync(configPath, 'utf-8');
        return JSON.parse(configData) as TemplateConfig;
      }
    }

    console.error(`Template config not found for ${templateId} in any of the following paths:`, possiblePaths);
    return null;
  } catch (error) {
    console.error(`Error loading template config for ${templateId}:`, error);
    return null;
  }
}

/**
 * Loads all available templates
 */
export async function loadAllTemplates(): Promise<TemplateConfig[]> {
  try {
    // Try different possible paths for templates directory
    const possibleTemplatesDirs = [
      path.join(process.cwd(), 'templates', 'html-templates'),
      path.join(process.cwd(), '..', 'templates', 'html-templates'),
      path.join(process.cwd(), 'frontend', 'templates', 'html-templates')
    ];

    let templatesDir = null;
    for (const testDir of possibleTemplatesDirs) {
      console.log('Testing templates directory:', testDir);
      if (fs.existsSync(testDir)) {
        templatesDir = testDir;
        console.log('Found templates directory at:', templatesDir);
        break;
      }
    }

    if (!templatesDir) {
      console.warn('Templates directory not found in any of the expected locations:', possibleTemplatesDirs);
      return [];
    }

    const templateFolders = fs.readdirSync(templatesDir, { withFileTypes: true })
      .filter(dirent => dirent.isDirectory())
      .map(dirent => dirent.name);

    console.log('Found template folders:', templateFolders);

    const templates: TemplateConfig[] = [];

    for (const folder of templateFolders) {
      const config = await loadTemplateConfig(folder);
      if (config) {
        templates.push(config);
        console.log(`Loaded template: ${config.name} (${config.id})`);
      }
    }

    return templates;
  } catch (error) {
    console.error('Error loading templates:', error);
    return [];
  }
}

/**
 * Processes an HTML template by replacing placeholders with actual content
 */
export async function processTemplate(
  templateId: string,
  content: PortfolioContent,
  colors: ColorScheme
): Promise<string | null> {
  try {
    const config = await loadTemplateConfig(templateId);
    if (!config) {
      throw new Error(`Template config not found for ${templateId}`);
    }

    // Try different possible paths for the template HTML file
    const possibleTemplatePaths = [
      path.join(process.cwd(), 'templates', 'html-templates', templateId, config.mainFile),
      path.join(process.cwd(), '..', 'templates', 'html-templates', templateId, config.mainFile),
      path.join(process.cwd(), 'frontend', 'templates', 'html-templates', templateId, config.mainFile)
    ];

    let templatePath = null;
    for (const testPath of possibleTemplatePaths) {
      if (fs.existsSync(testPath)) {
        templatePath = testPath;
        console.log(`Found template file at: ${templatePath}`);
        break;
      }
    }

    if (!templatePath) {
      throw new Error(`Template file not found for ${templateId} in any of the following paths: ${possibleTemplatePaths.join(', ')}`);
    }

    // Read the HTML template
    let htmlContent = fs.readFileSync(templatePath, 'utf-8');

    // Replace content placeholders
    htmlContent = replaceContentPlaceholders(htmlContent, content, config.placeholders);

    // Process the HTML with JSDOM to modify styles and other elements
    const dom = new JSDOM(htmlContent);
    const document = dom.window.document;

    // Apply color scheme
    applyColorScheme(document, colors);

    // Process any dynamic sections (skills, projects, experience)
    processDynamicSections(document, content, config);

    // Return the processed HTML
    return dom.serialize();
  } catch (error) {
    console.error(`Error processing template ${templateId}:`, error);
    return null;
  }
}

/**
 * Replaces content placeholders in the HTML
 */
function replaceContentPlaceholders(
  html: string,
  content: PortfolioContent,
  placeholders: TemplateConfig['placeholders']
): string {
  let processedHtml = html;

  // Process each content type
  for (const [key, placeholderList] of Object.entries(placeholders)) {
    if (content[key] && typeof content[key] === 'string') {
      // Replace simple string placeholders
      for (const placeholder of placeholderList) {
        processedHtml = processedHtml.replace(
          new RegExp(placeholder, 'g'),
          content[key] as string
        );
      }
    }
  }

  // Handle social media links
  if (content.social) {
    processedHtml = processedHtml.replace(/\{\{linkedin\}\}/g, content.social.linkedin || '#');
    processedHtml = processedHtml.replace(/\{\{github\}\}/g, content.social.github || '#');
    processedHtml = processedHtml.replace(/\{\{twitter\}\}/g, content.social.twitter || '#');
  }

  // Handle email and phone if they exist
  processedHtml = processedHtml.replace(/\{\{email\}\}/g, content.email || '<EMAIL>');
  processedHtml = processedHtml.replace(/\{\{phone\}\}/g, content.phone || '+****************');

  return processedHtml;
}

/**
 * Applies the color scheme to the HTML document
 */
function applyColorScheme(document: Document, colors: ColorScheme): void {
  // Find all style tags
  const styleTags = document.querySelectorAll('style');
  
  styleTags.forEach(styleTag => {
    let cssContent = styleTag.textContent || '';
    
    // Replace color variables or specific color values
    cssContent = cssContent
      .replace(/--primary-color:\s*[^;]+;/g, `--primary-color: ${colors.primary};`)
      .replace(/--secondary-color:\s*[^;]+;/g, `--secondary-color: ${colors.secondary};`)
      .replace(/--accent-color:\s*[^;]+;/g, `--accent-color: ${colors.accent};`);
    
    styleTag.textContent = cssContent;
  });
  
  // Also look for inline styles with color properties
  const elementsWithStyle = document.querySelectorAll('[style*="color"]');
  elementsWithStyle.forEach(element => {
    const style = element.getAttribute('style') || '';
    
    // Replace color values in inline styles
    const updatedStyle = style
      .replace(/color:\s*var\(--primary-color\)/g, `color: ${colors.primary}`)
      .replace(/color:\s*var\(--secondary-color\)/g, `color: ${colors.secondary}`)
      .replace(/color:\s*var\(--accent-color\)/g, `color: ${colors.accent}`)
      .replace(/background-color:\s*var\(--primary-color\)/g, `background-color: ${colors.primary}`)
      .replace(/background-color:\s*var\(--secondary-color\)/g, `background-color: ${colors.secondary}`)
      .replace(/background-color:\s*var\(--accent-color\)/g, `background-color: ${colors.accent}`);
    
    element.setAttribute('style', updatedStyle);
  });
}

/**
 * Processes dynamic sections like skills, projects, and experience
 */
function processDynamicSections(
  document: Document, 
  content: PortfolioContent, 
  config: TemplateConfig
): void {
  // Process skills section
  processSkillsSection(document, content.skills);
  
  // Process projects section
  processProjectsSection(document, content.projects);
  
  // Process experience section
  processExperienceSection(document, content.experience);
}

/**
 * Processes the skills section
 */
function processSkillsSection(document: Document, skills: string[]): void {
  const skillsContainer = document.querySelector('.skills-container, #skills-container, [data-section="skills"]');

  if (skillsContainer && skills.length > 0) {
    // Clear existing skills
    skillsContainer.innerHTML = '';

    // Create skill elements for each skill
    skills.forEach(skill => {
      const skillElement = document.createElement('div');
      skillElement.className = 'skill';
      skillElement.textContent = skill;
      skillsContainer.appendChild(skillElement);
    });
  }
}

/**
 * Processes the projects section
 */
function processProjectsSection(
  document: Document,
  projects: PortfolioContent['projects']
): void {
  const projectsContainer = document.querySelector('.projects-container, #projects-container, [data-section="projects"]');

  if (projectsContainer && projects.length > 0) {
    // Clear existing projects
    projectsContainer.innerHTML = '';

    // Create project elements for each project
    projects.forEach(project => {
      const projectElement = document.createElement('div');
      projectElement.className = 'project';

      // Create project content
      projectElement.innerHTML = `
        <img src="${project.image || '/placeholder-project.jpg'}" alt="${project.title}">
        <div class="project-content">
          <h3 class="project-title">${project.title}</h3>
          <p class="project-description">${project.description}</p>
          ${project.link ? `<a href="${project.link}" class="project-link" target="_blank">View Project</a>` : ''}
        </div>
      `;

      projectsContainer.appendChild(projectElement);
    });
  }
}

/**
 * Processes the experience section
 */
function processExperienceSection(
  document: Document,
  experiences: PortfolioContent['experience']
): void {
  const experienceContainer = document.querySelector('.experience-container, #experience-container, [data-section="experience"]');

  if (experienceContainer && experiences.length > 0) {
    // Clear existing experiences
    experienceContainer.innerHTML = '';

    // Create experience elements for each experience
    experiences.forEach(exp => {
      const expElement = document.createElement('div');
      expElement.className = 'experience-item';

      const dateText = exp.endDate
        ? `${exp.startDate} - ${exp.endDate}`
        : `${exp.startDate} - Present`;

      // Create experience content
      expElement.innerHTML = `
        <h3 class="job-title">${exp.title}</h3>
        <p class="company-name">${exp.company}</p>
        <p class="job-date">${dateText}</p>
        <p class="job-description">${exp.description}</p>
      `;

      experienceContainer.appendChild(expElement);
    });
  }
}

/**
 * Saves the processed HTML to the output directory
 */
export async function saveProcessedTemplate(
  portfolioId: string,
  html: string,
  templateId: string
): Promise<string> {
  try {
    // Try different possible output directories
    const possibleOutputDirs = [
      path.join(process.cwd(), 'data', 'portfolios', portfolioId),
      path.join(process.cwd(), 'frontend', 'data', 'portfolios', portfolioId),
      path.join(process.cwd(), '..', 'data', 'portfolios', portfolioId)
    ];

    // Use the first directory that we can create or that already exists
    let outputDir = possibleOutputDirs[0]; // Default to first option

    // Try to create the directory
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    console.log(`Saving portfolio to: ${outputDir}`);

    // Save the HTML file
    const htmlPath = path.join(outputDir, 'index.html');
    fs.writeFileSync(htmlPath, html);
    console.log(`HTML file saved to: ${htmlPath}`);

    // Find the template directory and copy assets
    const possibleTemplateDirs = [
      path.join(process.cwd(), 'templates', 'html-templates', templateId),
      path.join(process.cwd(), '..', 'templates', 'html-templates', templateId),
      path.join(process.cwd(), 'frontend', 'templates', 'html-templates', templateId)
    ];

    let templateDir = null;
    for (const testDir of possibleTemplateDirs) {
      if (fs.existsSync(testDir)) {
        templateDir = testDir;
        console.log(`Found template directory at: ${templateDir}`);
        break;
      }
    }

    if (templateDir) {
      copyTemplateAssets(templateDir, outputDir);
    } else {
      console.warn(`Template directory not found for ${templateId}`);
    }

    return outputDir;
  } catch (error) {
    console.error(`Error saving processed template for portfolio ${portfolioId}:`, error);
    throw new Error('Failed to save processed template');
  }
}

/**
 * Copies all assets from the template directory to the output directory
 */
function copyTemplateAssets(sourceDir: string, targetDir: string): void {
  // Read all files and directories in the source directory
  const items = fs.readdirSync(sourceDir, { withFileTypes: true });
  
  for (const item of items) {
    const sourcePath = path.join(sourceDir, item.name);
    const targetPath = path.join(targetDir, item.name);
    
    if (item.isDirectory()) {
      // Create the directory in the target
      if (!fs.existsSync(targetPath)) {
        fs.mkdirSync(targetPath, { recursive: true });
      }
      
      // Recursively copy contents
      copyTemplateAssets(sourcePath, targetPath);
    } else if (item.isFile() && !item.name.endsWith('.html') && item.name !== 'config.json') {
      // Copy the file (excluding HTML and config files)
      fs.copyFileSync(sourcePath, targetPath);
    }
  }
}

/**
 * Creates a sample template config file
 */
export function createSampleTemplateConfig(
  templateId: string,
  templateName: string,
  description: string
): TemplateConfig {
  return {
    id: templateId,
    name: templateName,
    description: description,
    folderPath: templateId,
    mainFile: 'index.html',
    previewImage: `/${templateId}/preview.jpg`,
    placeholders: {
      name: ['{{NAME}}', '{{name}}', '{{User Name}}'],
      title: ['{{TITLE}}', '{{title}}', '{{Job Title}}'],
      bio: ['{{BIO}}', '{{bio}}', '{{About Me}}', '{{about}}'],
      skills: ['{{SKILLS}}', '{{skills}}'],
      projects: ['{{PROJECTS}}', '{{projects}}'],
      experience: ['{{EXPERIENCE}}', '{{experience}}', '{{work}}'],
      contact: ['{{CONTACT}}', '{{contact}}', '{{email}}', '{{phone}}'],
      social: ['{{SOCIAL}}', '{{social}}', '{{linkedin}}', '{{github}}', '{{twitter}}']
    }
  };
} 