{"name": "portfolio-saas", "version": "1.0.0", "description": "AI-powered portfolio website generator", "private": true, "workspaces": ["frontend", "backend", "shared"], "scripts": {"dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd backend && npm run dev", "dev": "concurrently \"npm run dev:frontend\" \"npm run dev:backend\"", "build": "npm run build:frontend && npm run build:backend", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && npm run build", "test": "npm run test:frontend && npm run test:backend", "test:frontend": "cd frontend && npm test", "test:backend": "cd backend && npm test", "lint": "npm run lint:frontend && npm run lint:backend", "lint:frontend": "cd frontend && npm run lint", "lint:backend": "cd backend && npm run lint", "add-template": "node scripts/add-template.js"}, "keywords": ["portfolio", "saas", "ai", "website-generator"], "author": "", "license": "MIT", "devDependencies": {"@types/jsdom": "^21.1.6", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.20", "concurrently": "^8.2.2", "postcss": "^8.5.3", "prettier": "^3.2.5", "tailwindcss": "^4.0.7", "typescript": "^5.3.3"}, "dependencies": {"@hookform/resolvers": "^4.1.0", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@tailwindcss/vite": "^4.0.7", "@types/cheerio": "^0.22.35", "cheerio": "^1.0.0-rc.12", "jsdom": "^24.0.0", "lucide-react": "^0.294.0", "markdown-loader": "^8.0.0", "next-themes": "^0.4.4", "openai": "^4.85.2", "raw-loader": "^4.0.2", "react-hook-form": "^7.54.2", "uuid": "^11.1.0", "zod": "^3.24.2"}}