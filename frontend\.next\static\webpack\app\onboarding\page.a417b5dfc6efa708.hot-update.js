"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/onboarding/page",{

/***/ "(app-pages-browser)/./src/app/onboarding/page.tsx":
/*!*************************************!*\
  !*** ./src/app/onboarding/page.tsx ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ OnboardingPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/../node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_create_steps__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/create/steps */ \"(app-pages-browser)/./src/components/create/steps.tsx\");\n/* harmony import */ var _components_onboarding_profession_selector__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/onboarding/profession-selector */ \"(app-pages-browser)/./src/components/onboarding/profession-selector.tsx\");\n/* harmony import */ var _components_onboarding_template_selector__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/onboarding/template-selector */ \"(app-pages-browser)/./src/components/onboarding/template-selector.tsx\");\n/* harmony import */ var _components_onboarding_color_palette__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/onboarding/color-palette */ \"(app-pages-browser)/./src/components/onboarding/color-palette.tsx\");\n/* harmony import */ var _components_onboarding_section_config__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/onboarding/section-config */ \"(app-pages-browser)/./src/components/onboarding/section-config.tsx\");\n/* harmony import */ var _components_onboarding_content_form__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/onboarding/content-form */ \"(app-pages-browser)/./src/components/onboarding/content-form.tsx\");\n/* harmony import */ var _radix_ui_react_icons__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @radix-ui/react-icons */ \"(app-pages-browser)/../node_modules/@radix-ui/react-icons/dist/react-icons.esm.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst steps = [\n    {\n        id: \"profession\",\n        title: \"Profession\",\n        description: \"Select your profession\"\n    },\n    {\n        id: \"template\",\n        title: \"Template\",\n        description: \"Choose a template\"\n    },\n    {\n        id: \"colors\",\n        title: \"Colors\",\n        description: \"Pick your brand colors\"\n    },\n    {\n        id: \"sections\",\n        title: \"Sections\",\n        description: \"Customize sections\"\n    },\n    {\n        id: \"content\",\n        title: \"Content\",\n        description: \"Add your content\"\n    }\n];\nfunction OnboardingPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [currentStep, setCurrentStep] = react__WEBPACK_IMPORTED_MODULE_1__.useState(0);\n    const [formData, setFormData] = react__WEBPACK_IMPORTED_MODULE_1__.useState({\n        profession: \"\",\n        template: \"\",\n        colors: {\n            primary: \"#0070f3\",\n            secondary: \"#6b7280\",\n            accent: \"#f59e0b\"\n        },\n        sections: [\n            {\n                id: \"hero\",\n                name: \"Hero\",\n                description: \"Introduction and main headline\",\n                isRequired: true,\n                isEnabled: true\n            },\n            {\n                id: \"about\",\n                name: \"About\",\n                description: \"Personal bio and background\",\n                isRequired: true,\n                isEnabled: true\n            },\n            {\n                id: \"experience\",\n                name: \"Experience\",\n                description: \"Work history and achievements\",\n                isEnabled: true\n            },\n            {\n                id: \"projects\",\n                name: \"Projects\",\n                description: \"Showcase of your work\",\n                isEnabled: true\n            },\n            {\n                id: \"skills\",\n                name: \"Skills\",\n                description: \"Technical and professional skills\",\n                isEnabled: true\n            },\n            {\n                id: \"testimonials\",\n                name: \"Testimonials\",\n                description: \"Client and colleague reviews\",\n                isEnabled: false\n            },\n            {\n                id: \"blog\",\n                name: \"Blog\",\n                description: \"Articles and thoughts\",\n                isEnabled: false\n            },\n            {\n                id: \"contact\",\n                name: \"Contact\",\n                description: \"Contact information and form\",\n                isRequired: true,\n                isEnabled: true\n            }\n        ],\n        content: {\n            name: \"\",\n            title: \"\",\n            bio: \"\",\n            skills: [],\n            experience: [],\n            projects: [],\n            email: \"\",\n            phone: \"\",\n            social: {\n                linkedin: \"\",\n                github: \"\",\n                twitter: \"\"\n            }\n        }\n    });\n    const handleNext = ()=>{\n        // For the content form (last step), the form submission should be triggered by the form's button\n        if (currentStep === 4) {\n            var // The form's onSubmit handler will handle navigation, so we don't need to do anything here\n            // The ContentForm will automatically submit and call its onSubmit when its submit button is clicked\n            _document_querySelector;\n            (_document_querySelector = document.querySelector(\"form\")) === null || _document_querySelector === void 0 ? void 0 : _document_querySelector.dispatchEvent(new Event(\"submit\", {\n                cancelable: true,\n                bubbles: true\n            }));\n            return;\n        }\n        if (currentStep < steps.length - 1) {\n            setCurrentStep(currentStep + 1);\n        } else {\n            handleSubmit();\n        }\n    };\n    const handleBack = ()=>{\n        if (currentStep > 0) {\n            setCurrentStep(currentStep - 1);\n        }\n    };\n    const handleSubmit = async ()=>{\n        try {\n            console.log(\"Starting portfolio creation...\");\n            console.log(\"Form data:\", formData);\n            // Call the portfolio generation API\n            const response = await fetch(\"/api/portfolio/generate\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    profession: formData.profession,\n                    template: formData.template,\n                    colors: formData.colors,\n                    sections: formData.sections,\n                    content: formData.content\n                })\n            });\n            console.log(\"API response status:\", response.status);\n            const data = await response.json();\n            console.log(\"API response data:\", data);\n            if (data.success) {\n                console.log(\"Portfolio created successfully, redirecting to:\", data.portfolioUrl);\n                // Redirect to the generated portfolio\n                router.push(data.portfolioUrl);\n            } else {\n                console.error(\"Error generating portfolio:\", data.error);\n                alert(\"Error creating portfolio: \" + (data.error || \"Unknown error\"));\n            }\n        } catch (error) {\n            console.error(\"Error saving onboarding data:\", error);\n            alert(\"Error creating portfolio: \" + error.message);\n        }\n    };\n    const isStepValid = ()=>{\n        switch(currentStep){\n            case 0:\n                return !!formData.profession;\n            case 1:\n                return !!formData.template;\n            case 2:\n                return !!formData.colors.primary;\n            case 3:\n                return formData.sections.length > 0;\n            case 4:\n                return !!formData.content.name;\n            default:\n                return false;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"container max-w-4xl py-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                className: \"w-full\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                children: \"Create Your Portfolio\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                children: \"Follow these steps to create your personalized portfolio website\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        className: \"space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_create_steps__WEBPACK_IMPORTED_MODULE_5__.Steps, {\n                                steps: steps,\n                                currentStep: currentStep\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-8\",\n                                children: [\n                                    currentStep === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium\",\n                                                children: \"What's your profession?\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                                lineNumber: 224,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_onboarding_profession_selector__WEBPACK_IMPORTED_MODULE_6__.ProfessionSelector, {\n                                                value: formData.profession,\n                                                onChange: (value)=>setFormData({\n                                                        ...formData,\n                                                        profession: value\n                                                    })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                                lineNumber: 225,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 15\n                                    }, this),\n                                    currentStep === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium\",\n                                                children: \"Choose a template for your portfolio\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                                lineNumber: 235,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-muted-foreground\",\n                                                children: \"Select a template that best represents your professional style.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                                lineNumber: 236,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_onboarding_template_selector__WEBPACK_IMPORTED_MODULE_7__.TemplateSelector, {\n                                                value: formData.template,\n                                                onChange: (value)=>setFormData({\n                                                        ...formData,\n                                                        template: value\n                                                    })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                                lineNumber: 237,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                        lineNumber: 234,\n                                        columnNumber: 15\n                                    }, this),\n                                    currentStep === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium\",\n                                                children: \"Choose your brand colors\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                                lineNumber: 247,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-muted-foreground\",\n                                                children: \"Select a color palette or customize your own colors.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                                lineNumber: 248,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_onboarding_color_palette__WEBPACK_IMPORTED_MODULE_8__.ColorPalette, {\n                                                colors: formData.colors,\n                                                onChange: (colors)=>setFormData({\n                                                        ...formData,\n                                                        colors\n                                                    })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                                lineNumber: 249,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 15\n                                    }, this),\n                                    currentStep === 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium\",\n                                                children: \"Customize your portfolio sections\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                                lineNumber: 259,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-muted-foreground\",\n                                                children: \"Select and arrange the sections you want to include.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                                lineNumber: 260,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_onboarding_section_config__WEBPACK_IMPORTED_MODULE_9__.SectionConfig, {\n                                                sections: formData.sections,\n                                                onChange: (sections)=>setFormData({\n                                                        ...formData,\n                                                        sections\n                                                    })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                                lineNumber: 261,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 15\n                                    }, this),\n                                    currentStep === 4 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium\",\n                                                children: \"Add your personal information\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                                lineNumber: 271,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-muted-foreground\",\n                                                children: \"Fill in your details to personalize your portfolio.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                                lineNumber: 272,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_onboarding_content_form__WEBPACK_IMPORTED_MODULE_10__.ContentForm, {\n                                                defaultValues: formData.content,\n                                                onSubmit: (content)=>{\n                                                    setFormData({\n                                                        ...formData,\n                                                        content: content\n                                                    });\n                                                    if (currentStep === steps.length - 1) {\n                                                        handleSubmit();\n                                                    } else {\n                                                        handleNext();\n                                                    }\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                                lineNumber: 273,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                        lineNumber: 217,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardFooter, {\n                        className: \"flex justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: handleBack,\n                                disabled: currentStep === 0,\n                                children: \"Back\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                lineNumber: 290,\n                                columnNumber: 11\n                            }, this),\n                            currentStep !== 4 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: handleNext,\n                                disabled: !isStepValid(),\n                                children: currentStep === steps.length - 1 ? \"Create Portfolio\" : \"Next\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                lineNumber: 298,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: handleSubmit,\n                                disabled: !isStepValid(),\n                                className: \"bg-green-600 hover:bg-green-700\",\n                                children: \"Create Portfolio\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                lineNumber: 305,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                        lineNumber: 289,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                lineNumber: 210,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-8 p-4 border rounded-lg bg-slate-50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_icons__WEBPACK_IMPORTED_MODULE_11__.InfoCircledIcon, {\n                                className: \"h-5 w-5 text-blue-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                lineNumber: 319,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-md font-medium\",\n                                children: \"Admin Information\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                                lineNumber: 320,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                        lineNumber: 318,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-muted-foreground mb-2\",\n                        children: \"As an admin, you can upload and manage templates at:\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                        lineNumber: 322,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                        className: \"bg-slate-200 p-2 rounded text-sm block mb-2\",\n                        children: \"/templates\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                        lineNumber: 325,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-muted-foreground\",\n                        children: \"This page allows you to upload Envato Elements templates and configure them for use with the portfolio generator.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                        lineNumber: 328,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n                lineNumber: 317,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\onboarding\\\\page.tsx\",\n        lineNumber: 209,\n        columnNumber: 5\n    }, this);\n}\n_s(OnboardingPage, \"9YGt226IZAs450ATaUl2B60h8z8=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = OnboardingPage;\nvar _c;\n$RefreshReg$(_c, \"OnboardingPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/onboarding/page.tsx\n"));

/***/ })

});