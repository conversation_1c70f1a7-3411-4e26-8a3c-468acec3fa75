"use client"

import * as React from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON>,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Steps } from "@/components/create/steps"
import { ProfessionSelector } from "@/components/onboarding/profession-selector"
import { TemplateSelector } from "@/components/onboarding/template-selector"
import { ColorPalette } from "@/components/onboarding/color-palette"
import { SectionConfig } from "@/components/onboarding/section-config"
import { ContentForm } from "@/components/onboarding/content-form"
import { InfoCircledIcon } from "@radix-ui/react-icons"

const steps = [
  {
    id: "profession",
    title: "Profession",
    description: "Select your profession"
  },
  {
    id: "template",
    title: "Template",
    description: "Choose a template"
  },
  {
    id: "colors",
    title: "Colors",
    description: "Pick your brand colors"
  },
  {
    id: "sections",
    title: "Sections",
    description: "Customize sections"
  },
  {
    id: "content",
    title: "Content",
    description: "Add your content"
  }
]

export default function OnboardingPage() {
  const router = useRouter()
  const [currentStep, setCurrentStep] = React.useState(0)
  const [formData, setFormData] = React.useState({
    profession: "",
    template: "",
    colors: {
      primary: "#0070f3",
      secondary: "#6b7280",
      accent: "#f59e0b",
    },
    sections: [
      {
        id: "hero",
        name: "Hero",
        description: "Introduction and main headline",
        isRequired: true,
        isEnabled: true,
      },
      {
        id: "about",
        name: "About",
        description: "Personal bio and background",
        isRequired: true,
        isEnabled: true,
      },
      {
        id: "experience",
        name: "Experience",
        description: "Work history and achievements",
        isEnabled: true,
      },
      {
        id: "projects",
        name: "Projects",
        description: "Showcase of your work",
        isEnabled: true,
      },
      {
        id: "skills",
        name: "Skills",
        description: "Technical and professional skills",
        isEnabled: true,
      },
      {
        id: "testimonials",
        name: "Testimonials",
        description: "Client and colleague reviews",
        isEnabled: false,
      },
      {
        id: "blog",
        name: "Blog",
        description: "Articles and thoughts",
        isEnabled: false,
      },
      {
        id: "contact",
        name: "Contact",
        description: "Contact information and form",
        isRequired: true,
        isEnabled: true,
      },
    ],
    content: {
      name: "",
      title: "",
      bio: "",
      skills: [],
      experience: [],
      projects: [],
      social: {
        linkedin: "",
        github: "",
        twitter: "",
      },
    }
  })

  const handleNext = () => {
    // For the content form (last step), the form submission should be triggered by the form's button
    if (currentStep === 4) {
      // The form's onSubmit handler will handle navigation, so we don't need to do anything here
      // The ContentForm will automatically submit and call its onSubmit when its submit button is clicked
      document.querySelector('form')?.dispatchEvent(new Event('submit', { cancelable: true, bubbles: true }));
      return;
    }
    
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      handleSubmit();
    }
  }

  const handleBack = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1)
    }
  }

  const handleSubmit = async () => {
    try {
      console.log("Starting portfolio creation...");
      console.log("Form data:", formData);

      // Call the portfolio generation API
      const response = await fetch('/api/portfolio/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          profession: formData.profession,
          template: formData.template,
          colors: formData.colors,
          sections: formData.sections,
          content: formData.content
        }),
      });

      console.log("API response status:", response.status);
      const data = await response.json();
      console.log("API response data:", data);

      if (data.success) {
        console.log("Portfolio created successfully, redirecting to:", data.portfolioUrl);
        // Redirect to the generated portfolio
        router.push(data.portfolioUrl);
      } else {
        console.error("Error generating portfolio:", data.error);
        alert("Error creating portfolio: " + (data.error || "Unknown error"));
      }
    } catch (error) {
      console.error("Error saving onboarding data:", error);
      alert("Error creating portfolio: " + error.message);
    }
  }

  const isStepValid = () => {
    switch (currentStep) {
      case 0:
        return !!formData.profession
      case 1:
        return !!formData.template
      case 2:
        return !!formData.colors.primary
      case 3:
        return formData.sections.length > 0
      case 4:
        return !!formData.content.name
      default:
        return false
    }
  }

  return (
    <main className="container max-w-4xl py-8">
      <Card className="w-full">
        <CardHeader>
          <CardTitle>Create Your Portfolio</CardTitle>
          <CardDescription>
            Follow these steps to create your personalized portfolio website
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-8">
          <Steps steps={steps} currentStep={currentStep} />
          
          <div className="mt-8">
            {/* Step content will be added here */}
            {currentStep === 0 && (
              <div className="space-y-4">
                <h3 className="text-lg font-medium">What&apos;s your profession?</h3>
                <ProfessionSelector
                  value={formData.profession}
                  onChange={(value) =>
                    setFormData({ ...formData, profession: value })
                  }
                />
              </div>
            )}
            {currentStep === 1 && (
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Choose a template for your portfolio</h3>
                <p className="text-sm text-muted-foreground">Select a template that best represents your professional style.</p>
                <TemplateSelector
                  value={formData.template}
                  onChange={(value) =>
                    setFormData({ ...formData, template: value })
                  }
                />
              </div>
            )}
            {currentStep === 2 && (
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Choose your brand colors</h3>
                <p className="text-sm text-muted-foreground">Select a color palette or customize your own colors.</p>
                <ColorPalette
                  colors={formData.colors}
                  onChange={(colors) =>
                    setFormData({ ...formData, colors })
                  }
                />
              </div>
            )}
            {currentStep === 3 && (
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Customize your portfolio sections</h3>
                <p className="text-sm text-muted-foreground">Select and arrange the sections you want to include.</p>
                <SectionConfig
                  sections={formData.sections}
                  onChange={(sections) =>
                    setFormData({ ...formData, sections })
                  }
                />
              </div>
            )}
            {currentStep === 4 && (
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Add your personal information</h3>
                <p className="text-sm text-muted-foreground">Fill in your details to personalize your portfolio.</p>
                <ContentForm
                  defaultValues={formData.content as any}
                  onSubmit={(content) => {
                    setFormData({ ...formData, content: content as any });
                    if (currentStep === steps.length - 1) {
                      handleSubmit();
                    } else {
                      handleNext();
                    }
                  }}
                />
              </div>
            )}
            {/* Additional step content will be added for other steps */}
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button
            variant="outline"
            onClick={handleBack}
            disabled={currentStep === 0}
          >
            Back
          </Button>
          {currentStep !== 4 ? (
            <Button
              onClick={handleNext}
              disabled={!isStepValid()}
            >
              {currentStep === steps.length - 1 ? "Create Portfolio" : "Next"}
            </Button>
          ) : (
            <Button 
              onClick={handleSubmit}
              disabled={!isStepValid()}
              className="bg-green-600 hover:bg-green-700"
            >
              Create Portfolio
            </Button>
          )}
        </CardFooter>
      </Card>
      
      {/* Admin Info Section */}
      <div className="mt-8 p-4 border rounded-lg bg-slate-50">
        <div className="flex items-center gap-2 mb-2">
          <InfoCircledIcon className="h-5 w-5 text-blue-500" />
          <h3 className="text-md font-medium">Admin Information</h3>
        </div>
        <p className="text-sm text-muted-foreground mb-2">
          As an admin, you can upload and manage templates at:
        </p>
        <code className="bg-slate-200 p-2 rounded text-sm block mb-2">
          /templates
        </code>
        <p className="text-sm text-muted-foreground">
          This page allows you to upload Envato Elements templates and configure them for use with the portfolio generator.
        </p>
      </div>
    </main>
  )
} 