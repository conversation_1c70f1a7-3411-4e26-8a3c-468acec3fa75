"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/portfolio/[id]/route";
exports.ids = ["app/api/portfolio/[id]/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "(rsc)/../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fportfolio%2F%5Bid%5D%2Froute&page=%2Fapi%2Fportfolio%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fportfolio%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5CHIMANSHU%5COneDrive%5CDesktop%5Cportfolio%20saas%20-%20Copy%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHIMANSHU%5COneDrive%5CDesktop%5Cportfolio%20saas%20-%20Copy%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fportfolio%2F%5Bid%5D%2Froute&page=%2Fapi%2Fportfolio%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fportfolio%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5CHIMANSHU%5COneDrive%5CDesktop%5Cportfolio%20saas%20-%20Copy%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHIMANSHU%5COneDrive%5CDesktop%5Cportfolio%20saas%20-%20Copy%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/../node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/../node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/../node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_HIMANSHU_OneDrive_Desktop_portfolio_saas_Copy_frontend_src_app_api_portfolio_id_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/portfolio/[id]/route.ts */ \"(rsc)/./src/app/api/portfolio/[id]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/portfolio/[id]/route\",\n        pathname: \"/api/portfolio/[id]\",\n        filename: \"route\",\n        bundlePath: \"app/api/portfolio/[id]/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\api\\\\portfolio\\\\[id]\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_HIMANSHU_OneDrive_Desktop_portfolio_saas_Copy_frontend_src_app_api_portfolio_id_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/portfolio/[id]/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1hcHAtbG9hZGVyLmpzP25hbWU9YXBwJTJGYXBpJTJGcG9ydGZvbGlvJTJGJTVCaWQlNUQlMkZyb3V0ZSZwYWdlPSUyRmFwaSUyRnBvcnRmb2xpbyUyRiU1QmlkJTVEJTJGcm91dGUmYXBwUGF0aHM9JnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGYXBpJTJGcG9ydGZvbGlvJTJGJTVCaWQlNUQlMkZyb3V0ZS50cyZhcHBEaXI9QyUzQSU1Q1VzZXJzJTVDSElNQU5TSFUlNUNPbmVEcml2ZSU1Q0Rlc2t0b3AlNUNwb3J0Zm9saW8lMjBzYWFzJTIwLSUyMENvcHklNUNmcm9udGVuZCU1Q3NyYyU1Q2FwcCZwYWdlRXh0ZW5zaW9ucz10c3gmcGFnZUV4dGVuc2lvbnM9dHMmcGFnZUV4dGVuc2lvbnM9anN4JnBhZ2VFeHRlbnNpb25zPWpzJnJvb3REaXI9QyUzQSU1Q1VzZXJzJTVDSElNQU5TSFUlNUNPbmVEcml2ZSU1Q0Rlc2t0b3AlNUNwb3J0Zm9saW8lMjBzYWFzJTIwLSUyMENvcHklNUNmcm9udGVuZCZpc0Rldj10cnVlJnRzY29uZmlnUGF0aD10c2NvbmZpZy5qc29uJmJhc2VQYXRoPSZhc3NldFByZWZpeD0mbmV4dENvbmZpZ091dHB1dD0mcHJlZmVycmVkUmVnaW9uPSZtaWRkbGV3YXJlQ29uZmlnPWUzMCUzRCEiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBc0c7QUFDdkM7QUFDYztBQUNpRTtBQUM5STtBQUNBO0FBQ0E7QUFDQSx3QkFBd0IsZ0hBQW1CO0FBQzNDO0FBQ0EsY0FBYyx5RUFBUztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsWUFBWTtBQUNaLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQSxRQUFRLHVHQUF1RztBQUMvRztBQUNBO0FBQ0EsV0FBVyw0RUFBVztBQUN0QjtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQzZKOztBQUU3SiIsInNvdXJjZXMiOlsid2VicGFjazovL0Bwb3J0Zm9saW8tc2Fhcy9mcm9udGVuZC8/ZjE4MiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBBcHBSb3V0ZVJvdXRlTW9kdWxlIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvZnV0dXJlL3JvdXRlLW1vZHVsZXMvYXBwLXJvdXRlL21vZHVsZS5jb21waWxlZFwiO1xuaW1wb3J0IHsgUm91dGVLaW5kIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvZnV0dXJlL3JvdXRlLWtpbmRcIjtcbmltcG9ydCB7IHBhdGNoRmV0Y2ggYXMgX3BhdGNoRmV0Y2ggfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9saWIvcGF0Y2gtZmV0Y2hcIjtcbmltcG9ydCAqIGFzIHVzZXJsYW5kIGZyb20gXCJDOlxcXFxVc2Vyc1xcXFxISU1BTlNIVVxcXFxPbmVEcml2ZVxcXFxEZXNrdG9wXFxcXHBvcnRmb2xpbyBzYWFzIC0gQ29weVxcXFxmcm9udGVuZFxcXFxzcmNcXFxcYXBwXFxcXGFwaVxcXFxwb3J0Zm9saW9cXFxcW2lkXVxcXFxyb3V0ZS50c1wiO1xuLy8gV2UgaW5qZWN0IHRoZSBuZXh0Q29uZmlnT3V0cHV0IGhlcmUgc28gdGhhdCB3ZSBjYW4gdXNlIHRoZW0gaW4gdGhlIHJvdXRlXG4vLyBtb2R1bGUuXG5jb25zdCBuZXh0Q29uZmlnT3V0cHV0ID0gXCJcIlxuY29uc3Qgcm91dGVNb2R1bGUgPSBuZXcgQXBwUm91dGVSb3V0ZU1vZHVsZSh7XG4gICAgZGVmaW5pdGlvbjoge1xuICAgICAgICBraW5kOiBSb3V0ZUtpbmQuQVBQX1JPVVRFLFxuICAgICAgICBwYWdlOiBcIi9hcGkvcG9ydGZvbGlvL1tpZF0vcm91dGVcIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL2FwaS9wb3J0Zm9saW8vW2lkXVwiLFxuICAgICAgICBmaWxlbmFtZTogXCJyb3V0ZVwiLFxuICAgICAgICBidW5kbGVQYXRoOiBcImFwcC9hcGkvcG9ydGZvbGlvL1tpZF0vcm91dGVcIlxuICAgIH0sXG4gICAgcmVzb2x2ZWRQYWdlUGF0aDogXCJDOlxcXFxVc2Vyc1xcXFxISU1BTlNIVVxcXFxPbmVEcml2ZVxcXFxEZXNrdG9wXFxcXHBvcnRmb2xpbyBzYWFzIC0gQ29weVxcXFxmcm9udGVuZFxcXFxzcmNcXFxcYXBwXFxcXGFwaVxcXFxwb3J0Zm9saW9cXFxcW2lkXVxcXFxyb3V0ZS50c1wiLFxuICAgIG5leHRDb25maWdPdXRwdXQsXG4gICAgdXNlcmxhbmRcbn0pO1xuLy8gUHVsbCBvdXQgdGhlIGV4cG9ydHMgdGhhdCB3ZSBuZWVkIHRvIGV4cG9zZSBmcm9tIHRoZSBtb2R1bGUuIFRoaXMgc2hvdWxkXG4vLyBiZSBlbGltaW5hdGVkIHdoZW4gd2UndmUgbW92ZWQgdGhlIG90aGVyIHJvdXRlcyB0byB0aGUgbmV3IGZvcm1hdC4gVGhlc2Vcbi8vIGFyZSB1c2VkIHRvIGhvb2sgaW50byB0aGUgcm91dGUuXG5jb25zdCB7IHJlcXVlc3RBc3luY1N0b3JhZ2UsIHN0YXRpY0dlbmVyYXRpb25Bc3luY1N0b3JhZ2UsIHNlcnZlckhvb2tzLCBoZWFkZXJIb29rcywgc3RhdGljR2VuZXJhdGlvbkJhaWxvdXQgfSA9IHJvdXRlTW9kdWxlO1xuY29uc3Qgb3JpZ2luYWxQYXRobmFtZSA9IFwiL2FwaS9wb3J0Zm9saW8vW2lkXS9yb3V0ZVwiO1xuZnVuY3Rpb24gcGF0Y2hGZXRjaCgpIHtcbiAgICByZXR1cm4gX3BhdGNoRmV0Y2goe1xuICAgICAgICBzZXJ2ZXJIb29rcyxcbiAgICAgICAgc3RhdGljR2VuZXJhdGlvbkFzeW5jU3RvcmFnZVxuICAgIH0pO1xufVxuZXhwb3J0IHsgcm91dGVNb2R1bGUsIHJlcXVlc3RBc3luY1N0b3JhZ2UsIHN0YXRpY0dlbmVyYXRpb25Bc3luY1N0b3JhZ2UsIHNlcnZlckhvb2tzLCBoZWFkZXJIb29rcywgc3RhdGljR2VuZXJhdGlvbkJhaWxvdXQsIG9yaWdpbmFsUGF0aG5hbWUsIHBhdGNoRmV0Y2gsICB9O1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1hcHAtcm91dGUuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fportfolio%2F%5Bid%5D%2Froute&page=%2Fapi%2Fportfolio%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fportfolio%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5CHIMANSHU%5COneDrive%5CDesktop%5Cportfolio%20saas%20-%20Copy%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHIMANSHU%5COneDrive%5CDesktop%5Cportfolio%20saas%20-%20Copy%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/portfolio/[id]/route.ts":
/*!*********************************************!*\
  !*** ./src/app/api/portfolio/[id]/route.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/../node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _services_portfolio_generator__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/services/portfolio-generator */ \"(rsc)/./src/services/portfolio-generator.ts\");\n\n\nasync function GET(request, { params }) {\n    try {\n        const { id } = params;\n        if (!id) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Portfolio ID is required\"\n            }, {\n                status: 400\n            });\n        }\n        const portfolio = await (0,_services_portfolio_generator__WEBPACK_IMPORTED_MODULE_1__.getPortfolioById)(id);\n        if (!portfolio) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Portfolio not found\"\n            }, {\n                status: 404\n            });\n        }\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: true,\n            portfolio\n        });\n    } catch (error) {\n        console.error(\"Error fetching portfolio:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Failed to fetch portfolio\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/portfolio/[id]/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/services/portfolio-generator.ts":
/*!*********************************************!*\
  !*** ./src/services/portfolio-generator.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generatePortfolioContent: () => (/* binding */ generatePortfolioContent),\n/* harmony export */   getPortfolioById: () => (/* binding */ getPortfolioById),\n/* harmony export */   publishPortfolio: () => (/* binding */ publishPortfolio),\n/* harmony export */   updatePortfolio: () => (/* binding */ updatePortfolio)\n/* harmony export */ });\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! uuid */ \"(rsc)/../node_modules/uuid/dist/esm/v4.js\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_1__);\n\n\n\n// Portfolio storage directory\nconst PORTFOLIOS_DIR = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"frontend\", \"data\", \"portfolios\");\n/**\r\n * Generates the portfolio content based on the user's configuration\r\n */ async function generatePortfolioContent(config) {\n    try {\n        // Generate a unique ID for the portfolio\n        const portfolioId = (0,uuid__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n        // Create the portfolio object\n        const portfolio = {\n            id: portfolioId,\n            createdAt: new Date(),\n            updatedAt: new Date(),\n            template: config.template.id,\n            profession: config.profession,\n            colors: config.colors,\n            sections: config.sections.map((section)=>section.id),\n            content: {\n                ...config.content\n            },\n            status: \"draft\"\n        };\n        // Save the portfolio to file system\n        savePortfolioToFile(portfolio);\n        console.log(\"Portfolio saved successfully:\", portfolioId);\n        // Return the portfolio data\n        return portfolio;\n    } catch (error) {\n        console.error(\"Error generating portfolio content:\", error);\n        throw new Error(\"Failed to generate portfolio content\");\n    }\n}\n/**\r\n * Ensures the portfolios directory exists\r\n */ function ensurePortfoliosDir() {\n    if (!fs__WEBPACK_IMPORTED_MODULE_0___default().existsSync(PORTFOLIOS_DIR)) {\n        fs__WEBPACK_IMPORTED_MODULE_0___default().mkdirSync(PORTFOLIOS_DIR, {\n            recursive: true\n        });\n    }\n}\n/**\r\n * Saves a portfolio to a file\r\n */ function savePortfolioToFile(portfolio) {\n    try {\n        ensurePortfoliosDir();\n        const filePath = path__WEBPACK_IMPORTED_MODULE_1___default().join(PORTFOLIOS_DIR, `${portfolio.id}.json`);\n        fs__WEBPACK_IMPORTED_MODULE_0___default().writeFileSync(filePath, JSON.stringify(portfolio, null, 2));\n        console.log(`Portfolio saved to: ${filePath}`);\n    } catch (error) {\n        console.error(\"Error saving portfolio to file:\", error);\n        throw error;\n    }\n}\n/**\r\n * Retrieves a portfolio by ID\r\n */ async function getPortfolioById(id) {\n    try {\n        const filePath = path__WEBPACK_IMPORTED_MODULE_1___default().join(PORTFOLIOS_DIR, `${id}.json`);\n        if (!fs__WEBPACK_IMPORTED_MODULE_0___default().existsSync(filePath)) {\n            console.log(`Portfolio not found: ${id}`);\n            return null;\n        }\n        const fileContent = fs__WEBPACK_IMPORTED_MODULE_0___default().readFileSync(filePath, \"utf-8\");\n        const portfolio = JSON.parse(fileContent);\n        // Convert date strings back to Date objects\n        portfolio.createdAt = new Date(portfolio.createdAt);\n        portfolio.updatedAt = new Date(portfolio.updatedAt);\n        console.log(`Portfolio loaded: ${id}`);\n        return portfolio;\n    } catch (error) {\n        console.error(\"Error loading portfolio:\", error);\n        return null;\n    }\n}\n/**\r\n * Updates a portfolio\r\n */ async function updatePortfolio(id, updates) {\n    try {\n        // Get the existing portfolio\n        const portfolio = await getPortfolioById(id);\n        if (!portfolio) {\n            return null;\n        }\n        // Apply the updates\n        const updatedPortfolio = {\n            ...portfolio,\n            ...updates,\n            updatedAt: new Date()\n        };\n        // Save the updated portfolio\n        savePortfolioToFile(updatedPortfolio);\n        return updatedPortfolio;\n    } catch (error) {\n        console.error(\"Error updating portfolio:\", error);\n        return null;\n    }\n}\n/**\r\n * Publishes a portfolio\r\n */ async function publishPortfolio(id) {\n    try {\n        // Update the portfolio status to published\n        return await updatePortfolio(id, {\n            status: \"published\",\n            deploymentUrl: `https://portfolio.example.com/${id}`\n        });\n    } catch (error) {\n        console.error(\"Error publishing portfolio:\", error);\n        return null;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/services/portfolio-generator.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/uuid"], () => (__webpack_exec__("(rsc)/../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fportfolio%2F%5Bid%5D%2Froute&page=%2Fapi%2Fportfolio%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fportfolio%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5CHIMANSHU%5COneDrive%5CDesktop%5Cportfolio%20saas%20-%20Copy%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHIMANSHU%5COneDrive%5CDesktop%5Cportfolio%20saas%20-%20Copy%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();