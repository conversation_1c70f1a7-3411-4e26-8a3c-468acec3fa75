"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/portfolio/generate/route";
exports.ids = ["app/api/portfolio/generate/route"];
exports.modules = {

/***/ "jsdom":
/*!************************!*\
  !*** external "jsdom" ***!
  \************************/
/***/ ((module) => {

module.exports = require("jsdom");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "(rsc)/../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fportfolio%2Fgenerate%2Froute&page=%2Fapi%2Fportfolio%2Fgenerate%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fportfolio%2Fgenerate%2Froute.ts&appDir=C%3A%5CUsers%5CHIMANSHU%5COneDrive%5CDesktop%5Cportfolio%20saas%20-%20Copy%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHIMANSHU%5COneDrive%5CDesktop%5Cportfolio%20saas%20-%20Copy%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fportfolio%2Fgenerate%2Froute&page=%2Fapi%2Fportfolio%2Fgenerate%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fportfolio%2Fgenerate%2Froute.ts&appDir=C%3A%5CUsers%5CHIMANSHU%5COneDrive%5CDesktop%5Cportfolio%20saas%20-%20Copy%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHIMANSHU%5COneDrive%5CDesktop%5Cportfolio%20saas%20-%20Copy%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/../node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/../node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/../node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_HIMANSHU_OneDrive_Desktop_portfolio_saas_Copy_frontend_src_app_api_portfolio_generate_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/portfolio/generate/route.ts */ \"(rsc)/./src/app/api/portfolio/generate/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/portfolio/generate/route\",\n        pathname: \"/api/portfolio/generate\",\n        filename: \"route\",\n        bundlePath: \"app/api/portfolio/generate/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\api\\\\portfolio\\\\generate\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_HIMANSHU_OneDrive_Desktop_portfolio_saas_Copy_frontend_src_app_api_portfolio_generate_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/portfolio/generate/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fportfolio%2Fgenerate%2Froute&page=%2Fapi%2Fportfolio%2Fgenerate%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fportfolio%2Fgenerate%2Froute.ts&appDir=C%3A%5CUsers%5CHIMANSHU%5COneDrive%5CDesktop%5Cportfolio%20saas%20-%20Copy%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHIMANSHU%5COneDrive%5CDesktop%5Cportfolio%20saas%20-%20Copy%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/portfolio/generate/route.ts":
/*!*************************************************!*\
  !*** ./src/app/api/portfolio/generate/route.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/../node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _services_portfolio_generator__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/services/portfolio-generator */ \"(rsc)/./src/services/portfolio-generator.ts\");\n/* harmony import */ var _services_template_processor__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/services/template-processor */ \"(rsc)/./src/services/template-processor.ts\");\n\n\n\nasync function POST(request) {\n    try {\n        // Parse the request body to get the user's configuration\n        const requestData = await request.json();\n        // Destructure the user's configuration\n        const { profession, template, colors, sections, content } = requestData;\n        // Validate required fields\n        if (!template || !content || !colors) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Missing required configuration fields\"\n            }, {\n                status: 400\n            });\n        }\n        // Load the template configuration\n        const templateConfig = await (0,_services_template_processor__WEBPACK_IMPORTED_MODULE_2__.loadTemplateConfig)(template);\n        if (!templateConfig) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Invalid template selection or template configuration not found\"\n            }, {\n                status: 400\n            });\n        }\n        // Generate the portfolio data\n        const portfolioData = await (0,_services_portfolio_generator__WEBPACK_IMPORTED_MODULE_1__.generatePortfolioContent)({\n            profession,\n            template: {\n                id: templateConfig.id,\n                name: templateConfig.name,\n                previewComponent: templateConfig.id,\n                description: templateConfig.description\n            },\n            colors,\n            sections: sections.filter((section)=>section.isEnabled),\n            content\n        });\n        // Process the HTML template with the user's content\n        const processedHtml = await (0,_services_template_processor__WEBPACK_IMPORTED_MODULE_2__.processTemplate)(template, content, colors);\n        if (!processedHtml) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Failed to process template\"\n            }, {\n                status: 500\n            });\n        }\n        // Save the processed template to the output directory\n        const outputDir = await (0,_services_template_processor__WEBPACK_IMPORTED_MODULE_2__.saveProcessedTemplate)(portfolioData.id, processedHtml, template);\n        // Update the portfolio data with the deployment URL\n        portfolioData.deploymentUrl = `/portfolios/${portfolioData.id}`;\n        // Return the generated portfolio data\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: true,\n            portfolioId: portfolioData.id,\n            portfolioUrl: `/portfolio/${portfolioData.id}`,\n            deploymentUrl: portfolioData.deploymentUrl,\n            message: \"Portfolio generated successfully!\"\n        });\n    } catch (error) {\n        console.error(\"Error generating portfolio:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Failed to generate portfolio\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/portfolio/generate/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/services/portfolio-generator.ts":
/*!*********************************************!*\
  !*** ./src/services/portfolio-generator.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generatePortfolioContent: () => (/* binding */ generatePortfolioContent),\n/* harmony export */   getPortfolioById: () => (/* binding */ getPortfolioById),\n/* harmony export */   publishPortfolio: () => (/* binding */ publishPortfolio),\n/* harmony export */   updatePortfolio: () => (/* binding */ updatePortfolio)\n/* harmony export */ });\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! uuid */ \"(rsc)/../node_modules/uuid/dist/esm/v4.js\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_1__);\n\n\n\n// Portfolio storage directory\nconst PORTFOLIOS_DIR = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"frontend\", \"data\", \"portfolios\");\n/**\r\n * Generates the portfolio content based on the user's configuration\r\n */ async function generatePortfolioContent(config) {\n    try {\n        // Generate a unique ID for the portfolio\n        const portfolioId = (0,uuid__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n        // Create the portfolio object\n        const portfolio = {\n            id: portfolioId,\n            createdAt: new Date(),\n            updatedAt: new Date(),\n            template: config.template.id,\n            profession: config.profession,\n            colors: config.colors,\n            sections: config.sections.map((section)=>section.id),\n            content: {\n                ...config.content\n            },\n            status: \"draft\"\n        };\n        // Save the portfolio to file system\n        savePortfolioToFile(portfolio);\n        console.log(\"Portfolio saved successfully:\", portfolioId);\n        // Return the portfolio data\n        return portfolio;\n    } catch (error) {\n        console.error(\"Error generating portfolio content:\", error);\n        throw new Error(\"Failed to generate portfolio content\");\n    }\n}\n/**\r\n * Ensures the portfolios directory exists\r\n */ function ensurePortfoliosDir() {\n    if (!fs__WEBPACK_IMPORTED_MODULE_0___default().existsSync(PORTFOLIOS_DIR)) {\n        fs__WEBPACK_IMPORTED_MODULE_0___default().mkdirSync(PORTFOLIOS_DIR, {\n            recursive: true\n        });\n    }\n}\n/**\r\n * Saves a portfolio to a file\r\n */ function savePortfolioToFile(portfolio) {\n    try {\n        ensurePortfoliosDir();\n        const filePath = path__WEBPACK_IMPORTED_MODULE_1___default().join(PORTFOLIOS_DIR, `${portfolio.id}.json`);\n        fs__WEBPACK_IMPORTED_MODULE_0___default().writeFileSync(filePath, JSON.stringify(portfolio, null, 2));\n        console.log(`Portfolio saved to: ${filePath}`);\n    } catch (error) {\n        console.error(\"Error saving portfolio to file:\", error);\n        throw error;\n    }\n}\n/**\r\n * Retrieves a portfolio by ID\r\n */ async function getPortfolioById(id) {\n    try {\n        const filePath = path__WEBPACK_IMPORTED_MODULE_1___default().join(PORTFOLIOS_DIR, `${id}.json`);\n        if (!fs__WEBPACK_IMPORTED_MODULE_0___default().existsSync(filePath)) {\n            console.log(`Portfolio not found: ${id}`);\n            return null;\n        }\n        const fileContent = fs__WEBPACK_IMPORTED_MODULE_0___default().readFileSync(filePath, \"utf-8\");\n        const portfolio = JSON.parse(fileContent);\n        // Convert date strings back to Date objects\n        portfolio.createdAt = new Date(portfolio.createdAt);\n        portfolio.updatedAt = new Date(portfolio.updatedAt);\n        console.log(`Portfolio loaded: ${id}`);\n        return portfolio;\n    } catch (error) {\n        console.error(\"Error loading portfolio:\", error);\n        return null;\n    }\n}\n/**\r\n * Updates a portfolio\r\n */ async function updatePortfolio(id, updates) {\n    try {\n        // Get the existing portfolio\n        const portfolio = await getPortfolioById(id);\n        if (!portfolio) {\n            return null;\n        }\n        // Apply the updates\n        const updatedPortfolio = {\n            ...portfolio,\n            ...updates,\n            updatedAt: new Date()\n        };\n        // Save the updated portfolio\n        savePortfolioToFile(updatedPortfolio);\n        return updatedPortfolio;\n    } catch (error) {\n        console.error(\"Error updating portfolio:\", error);\n        return null;\n    }\n}\n/**\r\n * Publishes a portfolio\r\n */ async function publishPortfolio(id) {\n    try {\n        // Update the portfolio status to published\n        return await updatePortfolio(id, {\n            status: \"published\",\n            deploymentUrl: `https://portfolio.example.com/${id}`\n        });\n    } catch (error) {\n        console.error(\"Error publishing portfolio:\", error);\n        return null;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/services/portfolio-generator.ts\n");

/***/ }),

/***/ "(rsc)/./src/services/template-processor.ts":
/*!********************************************!*\
  !*** ./src/services/template-processor.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createSampleTemplateConfig: () => (/* binding */ createSampleTemplateConfig),\n/* harmony export */   loadAllTemplates: () => (/* binding */ loadAllTemplates),\n/* harmony export */   loadTemplateConfig: () => (/* binding */ loadTemplateConfig),\n/* harmony export */   processTemplate: () => (/* binding */ processTemplate),\n/* harmony export */   saveProcessedTemplate: () => (/* binding */ saveProcessedTemplate)\n/* harmony export */ });\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var jsdom__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! jsdom */ \"jsdom\");\n/* harmony import */ var jsdom__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(jsdom__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\n/**\r\n * Loads template configuration from the template directory\r\n */ async function loadTemplateConfig(templateId) {\n    try {\n        // Try the main path first\n        const configPath = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"templates\", \"html-templates\", templateId, \"config.json\");\n        if (fs__WEBPACK_IMPORTED_MODULE_0___default().existsSync(configPath)) {\n            console.log(`Loading template config from: ${configPath}`);\n            const configData = fs__WEBPACK_IMPORTED_MODULE_0___default().readFileSync(configPath, \"utf-8\");\n            return JSON.parse(configData);\n        }\n        // Try the alternative path\n        const altConfigPath = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"..\", \"templates\", \"html-templates\", templateId, \"config.json\");\n        if (fs__WEBPACK_IMPORTED_MODULE_0___default().existsSync(altConfigPath)) {\n            console.log(`Loading template config from alternative path: ${altConfigPath}`);\n            const configData = fs__WEBPACK_IMPORTED_MODULE_0___default().readFileSync(altConfigPath, \"utf-8\");\n            return JSON.parse(configData);\n        }\n        console.error(`Template config not found for ${templateId}`);\n        return null;\n    } catch (error) {\n        console.error(`Error loading template config for ${templateId}:`, error);\n        return null;\n    }\n}\n/**\r\n * Loads all available templates\r\n */ async function loadAllTemplates() {\n    try {\n        // Use a relative path from the frontend directory\n        const templatesDir = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"..\", \"templates\", \"html-templates\");\n        console.log(\"Loading templates from:\", templatesDir);\n        if (!fs__WEBPACK_IMPORTED_MODULE_0___default().existsSync(templatesDir)) {\n            console.warn(\"Templates directory does not exist\");\n            // Try alternative path\n            const altTemplatesDir = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"templates\", \"html-templates\");\n            console.log(\"Trying alternative path:\", altTemplatesDir);\n            if (!fs__WEBPACK_IMPORTED_MODULE_0___default().existsSync(altTemplatesDir)) {\n                console.warn(\"Alternative templates directory does not exist either\");\n                return [];\n            }\n            // Use the alternative path\n            const templateFolders = fs__WEBPACK_IMPORTED_MODULE_0___default().readdirSync(altTemplatesDir, {\n                withFileTypes: true\n            }).filter((dirent)=>dirent.isDirectory()).map((dirent)=>dirent.name);\n            console.log(\"Found template folders:\", templateFolders);\n            const templates = [];\n            for (const folder of templateFolders){\n                const configPath = path__WEBPACK_IMPORTED_MODULE_1___default().join(altTemplatesDir, folder, \"config.json\");\n                if (!fs__WEBPACK_IMPORTED_MODULE_0___default().existsSync(configPath)) {\n                    console.warn(`Config file not found for template ${folder}`);\n                    continue;\n                }\n                try {\n                    const configData = fs__WEBPACK_IMPORTED_MODULE_0___default().readFileSync(configPath, \"utf-8\");\n                    const config = JSON.parse(configData);\n                    templates.push(config);\n                    console.log(`Loaded template: ${config.name} (${config.id})`);\n                } catch (err) {\n                    console.error(`Error loading config for template ${folder}:`, err);\n                }\n            }\n            return templates;\n        }\n        const templateFolders = fs__WEBPACK_IMPORTED_MODULE_0___default().readdirSync(templatesDir, {\n            withFileTypes: true\n        }).filter((dirent)=>dirent.isDirectory()).map((dirent)=>dirent.name);\n        console.log(\"Found template folders:\", templateFolders);\n        const templates = [];\n        for (const folder of templateFolders){\n            const config = await loadTemplateConfig(folder);\n            if (config) {\n                templates.push(config);\n                console.log(`Loaded template: ${config.name} (${config.id})`);\n            }\n        }\n        return templates;\n    } catch (error) {\n        console.error(\"Error loading templates:\", error);\n        return [];\n    }\n}\n/**\r\n * Processes an HTML template by replacing placeholders with actual content\r\n */ async function processTemplate(templateId, content, colors) {\n    try {\n        const config = await loadTemplateConfig(templateId);\n        if (!config) {\n            throw new Error(`Template config not found for ${templateId}`);\n        }\n        const templatePath = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"templates\", \"html-templates\", templateId, config.mainFile);\n        if (!fs__WEBPACK_IMPORTED_MODULE_0___default().existsSync(templatePath)) {\n            throw new Error(`Template file not found: ${templatePath}`);\n        }\n        // Read the HTML template\n        let htmlContent = fs__WEBPACK_IMPORTED_MODULE_0___default().readFileSync(templatePath, \"utf-8\");\n        // Replace content placeholders\n        htmlContent = replaceContentPlaceholders(htmlContent, content, config.placeholders);\n        // Process the HTML with JSDOM to modify styles and other elements\n        const dom = new jsdom__WEBPACK_IMPORTED_MODULE_2__.JSDOM(htmlContent);\n        const document = dom.window.document;\n        // Apply color scheme\n        applyColorScheme(document, colors);\n        // Process any dynamic sections (skills, projects, experience)\n        processDynamicSections(document, content, config);\n        // Return the processed HTML\n        return dom.serialize();\n    } catch (error) {\n        console.error(`Error processing template ${templateId}:`, error);\n        return null;\n    }\n}\n/**\r\n * Replaces content placeholders in the HTML\r\n */ function replaceContentPlaceholders(html, content, placeholders) {\n    let processedHtml = html;\n    // Process each content type\n    for (const [key, placeholderList] of Object.entries(placeholders)){\n        if (content[key] && typeof content[key] === \"string\") {\n            // Replace simple string placeholders\n            for (const placeholder of placeholderList){\n                processedHtml = processedHtml.replace(new RegExp(placeholder, \"g\"), content[key]);\n            }\n        }\n    }\n    return processedHtml;\n}\n/**\r\n * Applies the color scheme to the HTML document\r\n */ function applyColorScheme(document, colors) {\n    // Find all style tags\n    const styleTags = document.querySelectorAll(\"style\");\n    styleTags.forEach((styleTag)=>{\n        let cssContent = styleTag.textContent || \"\";\n        // Replace color variables or specific color values\n        cssContent = cssContent.replace(/--primary-color:\\s*[^;]+;/g, `--primary-color: ${colors.primary};`).replace(/--secondary-color:\\s*[^;]+;/g, `--secondary-color: ${colors.secondary};`).replace(/--accent-color:\\s*[^;]+;/g, `--accent-color: ${colors.accent};`);\n        styleTag.textContent = cssContent;\n    });\n    // Also look for inline styles with color properties\n    const elementsWithStyle = document.querySelectorAll('[style*=\"color\"]');\n    elementsWithStyle.forEach((element)=>{\n        const style = element.getAttribute(\"style\") || \"\";\n        // Replace color values in inline styles\n        const updatedStyle = style.replace(/color:\\s*var\\(--primary-color\\)/g, `color: ${colors.primary}`).replace(/color:\\s*var\\(--secondary-color\\)/g, `color: ${colors.secondary}`).replace(/color:\\s*var\\(--accent-color\\)/g, `color: ${colors.accent}`).replace(/background-color:\\s*var\\(--primary-color\\)/g, `background-color: ${colors.primary}`).replace(/background-color:\\s*var\\(--secondary-color\\)/g, `background-color: ${colors.secondary}`).replace(/background-color:\\s*var\\(--accent-color\\)/g, `background-color: ${colors.accent}`);\n        element.setAttribute(\"style\", updatedStyle);\n    });\n}\n/**\r\n * Processes dynamic sections like skills, projects, and experience\r\n */ function processDynamicSections(document, content, config) {\n    // Process skills section\n    processSkillsSection(document, content.skills);\n    // Process projects section\n    processProjectsSection(document, content.projects);\n    // Process experience section\n    processExperienceSection(document, content.experience);\n}\n/**\r\n * Processes the skills section\r\n */ function processSkillsSection(document, skills) {\n    const skillsContainer = document.querySelector('.skills-container, #skills-container, [data-section=\"skills\"]');\n    if (skillsContainer && skills.length > 0) {\n        // Look for a template item to clone\n        const skillTemplate = skillsContainer.querySelector(\".skill-item, .skill\");\n        if (skillTemplate) {\n            // Clear existing skills\n            skillsContainer.innerHTML = \"\";\n            // Clone and populate for each skill\n            skills.forEach((skill)=>{\n                const skillElement = skillTemplate.cloneNode(true);\n                // Try different ways to set the skill text\n                const textElement = skillElement.querySelector(\".skill-name, .skill-text\");\n                if (textElement) {\n                    textElement.textContent = skill;\n                } else {\n                    skillElement.textContent = skill;\n                }\n                skillsContainer.appendChild(skillElement);\n            });\n        }\n    }\n}\n/**\r\n * Processes the projects section\r\n */ function processProjectsSection(document, projects) {\n    const projectsContainer = document.querySelector('.projects-container, #projects-container, [data-section=\"projects\"]');\n    if (projectsContainer && projects.length > 0) {\n        // Look for a template item to clone\n        const projectTemplate = projectsContainer.querySelector(\".project-item, .project\");\n        if (projectTemplate) {\n            // Clear existing projects\n            projectsContainer.innerHTML = \"\";\n            // Clone and populate for each project\n            projects.forEach((project)=>{\n                const projectElement = projectTemplate.cloneNode(true);\n                // Set project title\n                const titleElement = projectElement.querySelector(\".project-title, .title\");\n                if (titleElement) {\n                    titleElement.textContent = project.title;\n                }\n                // Set project description\n                const descElement = projectElement.querySelector(\".project-description, .description\");\n                if (descElement) {\n                    descElement.textContent = project.description;\n                }\n                // Set project image if available\n                if (project.image) {\n                    const imgElement = projectElement.querySelector(\"img\");\n                    if (imgElement) {\n                        imgElement.src = project.image;\n                        imgElement.alt = project.title;\n                    }\n                }\n                // Set project link if available\n                if (project.link) {\n                    const linkElement = projectElement.querySelector(\"a\");\n                    if (linkElement) {\n                        linkElement.href = project.link;\n                    }\n                }\n                projectsContainer.appendChild(projectElement);\n            });\n        }\n    }\n}\n/**\r\n * Processes the experience section\r\n */ function processExperienceSection(document, experiences) {\n    const experienceContainer = document.querySelector('.experience-container, #experience-container, [data-section=\"experience\"]');\n    if (experienceContainer && experiences.length > 0) {\n        // Look for a template item to clone\n        const experienceTemplate = experienceContainer.querySelector(\".experience-item, .job\");\n        if (experienceTemplate) {\n            // Clear existing experiences\n            experienceContainer.innerHTML = \"\";\n            // Clone and populate for each experience\n            experiences.forEach((exp)=>{\n                const expElement = experienceTemplate.cloneNode(true);\n                // Set job title\n                const titleElement = expElement.querySelector(\".job-title, .title\");\n                if (titleElement) {\n                    titleElement.textContent = exp.title;\n                }\n                // Set company name\n                const companyElement = expElement.querySelector(\".company-name, .company\");\n                if (companyElement) {\n                    companyElement.textContent = exp.company;\n                }\n                // Set job description\n                const descElement = expElement.querySelector(\".job-description, .description\");\n                if (descElement) {\n                    descElement.textContent = exp.description;\n                }\n                // Set job dates\n                const dateElement = expElement.querySelector(\".job-date, .date\");\n                if (dateElement) {\n                    const dateText = exp.endDate ? `${exp.startDate} - ${exp.endDate}` : `${exp.startDate} - Present`;\n                    dateElement.textContent = dateText;\n                }\n                experienceContainer.appendChild(expElement);\n            });\n        }\n    }\n}\n/**\r\n * Saves the processed HTML to the output directory\r\n */ async function saveProcessedTemplate(portfolioId, html, templateId) {\n    try {\n        // Create the output directory\n        const outputDir = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"data\", \"portfolios\", portfolioId);\n        if (!fs__WEBPACK_IMPORTED_MODULE_0___default().existsSync(outputDir)) {\n            fs__WEBPACK_IMPORTED_MODULE_0___default().mkdirSync(outputDir, {\n                recursive: true\n            });\n        }\n        // Save the HTML file\n        const htmlPath = path__WEBPACK_IMPORTED_MODULE_1___default().join(outputDir, \"index.html\");\n        fs__WEBPACK_IMPORTED_MODULE_0___default().writeFileSync(htmlPath, html);\n        // Copy all assets from the template directory\n        const templateDir = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"templates\", \"html-templates\", templateId);\n        copyTemplateAssets(templateDir, outputDir);\n        return outputDir;\n    } catch (error) {\n        console.error(`Error saving processed template for portfolio ${portfolioId}:`, error);\n        throw new Error(\"Failed to save processed template\");\n    }\n}\n/**\r\n * Copies all assets from the template directory to the output directory\r\n */ function copyTemplateAssets(sourceDir, targetDir) {\n    // Read all files and directories in the source directory\n    const items = fs__WEBPACK_IMPORTED_MODULE_0___default().readdirSync(sourceDir, {\n        withFileTypes: true\n    });\n    for (const item of items){\n        const sourcePath = path__WEBPACK_IMPORTED_MODULE_1___default().join(sourceDir, item.name);\n        const targetPath = path__WEBPACK_IMPORTED_MODULE_1___default().join(targetDir, item.name);\n        if (item.isDirectory()) {\n            // Create the directory in the target\n            if (!fs__WEBPACK_IMPORTED_MODULE_0___default().existsSync(targetPath)) {\n                fs__WEBPACK_IMPORTED_MODULE_0___default().mkdirSync(targetPath, {\n                    recursive: true\n                });\n            }\n            // Recursively copy contents\n            copyTemplateAssets(sourcePath, targetPath);\n        } else if (item.isFile() && !item.name.endsWith(\".html\") && item.name !== \"config.json\") {\n            // Copy the file (excluding HTML and config files)\n            fs__WEBPACK_IMPORTED_MODULE_0___default().copyFileSync(sourcePath, targetPath);\n        }\n    }\n}\n/**\r\n * Creates a sample template config file\r\n */ function createSampleTemplateConfig(templateId, templateName, description) {\n    return {\n        id: templateId,\n        name: templateName,\n        description: description,\n        folderPath: templateId,\n        mainFile: \"index.html\",\n        previewImage: `/${templateId}/preview.jpg`,\n        placeholders: {\n            name: [\n                \"{{NAME}}\",\n                \"{{name}}\",\n                \"{{User Name}}\"\n            ],\n            title: [\n                \"{{TITLE}}\",\n                \"{{title}}\",\n                \"{{Job Title}}\"\n            ],\n            bio: [\n                \"{{BIO}}\",\n                \"{{bio}}\",\n                \"{{About Me}}\",\n                \"{{about}}\"\n            ],\n            skills: [\n                \"{{SKILLS}}\",\n                \"{{skills}}\"\n            ],\n            projects: [\n                \"{{PROJECTS}}\",\n                \"{{projects}}\"\n            ],\n            experience: [\n                \"{{EXPERIENCE}}\",\n                \"{{experience}}\",\n                \"{{work}}\"\n            ],\n            contact: [\n                \"{{CONTACT}}\",\n                \"{{contact}}\",\n                \"{{email}}\",\n                \"{{phone}}\"\n            ],\n            social: [\n                \"{{SOCIAL}}\",\n                \"{{social}}\",\n                \"{{linkedin}}\",\n                \"{{github}}\",\n                \"{{twitter}}\"\n            ]\n        }\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/services/template-processor.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/uuid"], () => (__webpack_exec__("(rsc)/../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fportfolio%2Fgenerate%2Froute&page=%2Fapi%2Fportfolio%2Fgenerate%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fportfolio%2Fgenerate%2Froute.ts&appDir=C%3A%5CUsers%5CHIMANSHU%5COneDrive%5CDesktop%5Cportfolio%20saas%20-%20Copy%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHIMANSHU%5COneDrive%5CDesktop%5Cportfolio%20saas%20-%20Copy%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();