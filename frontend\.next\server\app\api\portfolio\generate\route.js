"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/portfolio/generate/route";
exports.ids = ["app/api/portfolio/generate/route"];
exports.modules = {

/***/ "jsdom":
/*!************************!*\
  !*** external "jsdom" ***!
  \************************/
/***/ ((module) => {

module.exports = require("jsdom");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "(rsc)/../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fportfolio%2Fgenerate%2Froute&page=%2Fapi%2Fportfolio%2Fgenerate%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fportfolio%2Fgenerate%2Froute.ts&appDir=C%3A%5CUsers%5CHIMANSHU%5COneDrive%5CDesktop%5Cportfolio%20saas%20-%20Copy%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHIMANSHU%5COneDrive%5CDesktop%5Cportfolio%20saas%20-%20Copy%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fportfolio%2Fgenerate%2Froute&page=%2Fapi%2Fportfolio%2Fgenerate%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fportfolio%2Fgenerate%2Froute.ts&appDir=C%3A%5CUsers%5CHIMANSHU%5COneDrive%5CDesktop%5Cportfolio%20saas%20-%20Copy%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHIMANSHU%5COneDrive%5CDesktop%5Cportfolio%20saas%20-%20Copy%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/../node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/../node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/../node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_HIMANSHU_OneDrive_Desktop_portfolio_saas_Copy_frontend_src_app_api_portfolio_generate_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/portfolio/generate/route.ts */ \"(rsc)/./src/app/api/portfolio/generate/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/portfolio/generate/route\",\n        pathname: \"/api/portfolio/generate\",\n        filename: \"route\",\n        bundlePath: \"app/api/portfolio/generate/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\api\\\\portfolio\\\\generate\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_HIMANSHU_OneDrive_Desktop_portfolio_saas_Copy_frontend_src_app_api_portfolio_generate_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/portfolio/generate/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fportfolio%2Fgenerate%2Froute&page=%2Fapi%2Fportfolio%2Fgenerate%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fportfolio%2Fgenerate%2Froute.ts&appDir=C%3A%5CUsers%5CHIMANSHU%5COneDrive%5CDesktop%5Cportfolio%20saas%20-%20Copy%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHIMANSHU%5COneDrive%5CDesktop%5Cportfolio%20saas%20-%20Copy%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/portfolio/generate/route.ts":
/*!*************************************************!*\
  !*** ./src/app/api/portfolio/generate/route.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/../node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _services_portfolio_generator__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/services/portfolio-generator */ \"(rsc)/./src/services/portfolio-generator.ts\");\n/* harmony import */ var _services_template_processor__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/services/template-processor */ \"(rsc)/./src/services/template-processor.ts\");\n\n\n\nasync function POST(request) {\n    try {\n        console.log(\"Portfolio generation API called\");\n        // Parse the request body to get the user's configuration\n        const requestData = await request.json();\n        console.log(\"Request data received:\", requestData);\n        // Destructure the user's configuration\n        const { profession, template, colors, sections, content } = requestData;\n        console.log(\"Extracted fields:\", {\n            profession,\n            template,\n            colors,\n            sections,\n            content\n        });\n        // Validate required fields\n        if (!template || !content || !colors) {\n            console.error(\"Missing required fields:\", {\n                template: !!template,\n                content: !!content,\n                colors: !!colors\n            });\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Missing required configuration fields\"\n            }, {\n                status: 400\n            });\n        }\n        // Load the template configuration\n        console.log(\"Loading template config for:\", template);\n        const templateConfig = await (0,_services_template_processor__WEBPACK_IMPORTED_MODULE_2__.loadTemplateConfig)(template);\n        console.log(\"Template config loaded:\", templateConfig);\n        if (!templateConfig) {\n            console.error(\"Template not found:\", template);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Invalid template selection or template configuration not found\"\n            }, {\n                status: 400\n            });\n        }\n        // Generate the portfolio data\n        const portfolioData = await (0,_services_portfolio_generator__WEBPACK_IMPORTED_MODULE_1__.generatePortfolioContent)({\n            profession,\n            template: {\n                id: templateConfig.id,\n                name: templateConfig.name,\n                previewComponent: templateConfig.id,\n                description: templateConfig.description\n            },\n            colors,\n            sections: sections.filter((section)=>section.isEnabled),\n            content\n        });\n        // Process the HTML template with the user's content\n        console.log(\"=== CALLING PROCESS TEMPLATE ===\");\n        console.log(\"Template:\", template);\n        console.log(\"Content:\", content);\n        console.log(\"Colors:\", colors);\n        const processedHtml = await (0,_services_template_processor__WEBPACK_IMPORTED_MODULE_2__.processTemplate)(template, content, colors);\n        console.log(\"=== PROCESS TEMPLATE RESULT ===\");\n        console.log(\"Processed HTML length:\", processedHtml ? processedHtml.length : \"null\");\n        if (!processedHtml) {\n            console.error(\"processTemplate returned null or empty\");\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Failed to process template\"\n            }, {\n                status: 500\n            });\n        }\n        // Save the processed template to the output directory\n        const outputDir = await (0,_services_template_processor__WEBPACK_IMPORTED_MODULE_2__.saveProcessedTemplate)(portfolioData.id, processedHtml, template);\n        // Update the portfolio data with the deployment URL\n        portfolioData.deploymentUrl = `/portfolios/${portfolioData.id}`;\n        // Return the generated portfolio data\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: true,\n            portfolioId: portfolioData.id,\n            portfolioUrl: `/portfolio/${portfolioData.id}`,\n            deploymentUrl: portfolioData.deploymentUrl,\n            message: \"Portfolio generated successfully!\"\n        });\n    } catch (error) {\n        console.error(\"Error generating portfolio:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Failed to generate portfolio\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/portfolio/generate/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/services/portfolio-generator.ts":
/*!*********************************************!*\
  !*** ./src/services/portfolio-generator.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generatePortfolioContent: () => (/* binding */ generatePortfolioContent),\n/* harmony export */   getPortfolioById: () => (/* binding */ getPortfolioById),\n/* harmony export */   publishPortfolio: () => (/* binding */ publishPortfolio),\n/* harmony export */   updatePortfolio: () => (/* binding */ updatePortfolio)\n/* harmony export */ });\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! uuid */ \"(rsc)/../node_modules/uuid/dist/esm/v4.js\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_1__);\n\n\n\n// Portfolio storage directory\nconst PORTFOLIOS_DIR = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"frontend\", \"data\", \"portfolios\");\n/**\r\n * Generates the portfolio content based on the user's configuration\r\n */ async function generatePortfolioContent(config) {\n    try {\n        // Generate a unique ID for the portfolio\n        const portfolioId = (0,uuid__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n        // Create the portfolio object\n        const portfolio = {\n            id: portfolioId,\n            createdAt: new Date(),\n            updatedAt: new Date(),\n            template: config.template.id,\n            profession: config.profession,\n            colors: config.colors,\n            sections: config.sections.map((section)=>section.id),\n            content: {\n                ...config.content\n            },\n            status: \"draft\"\n        };\n        // Save the portfolio to file system\n        savePortfolioToFile(portfolio);\n        console.log(\"Portfolio saved successfully:\", portfolioId);\n        // Return the portfolio data\n        return portfolio;\n    } catch (error) {\n        console.error(\"Error generating portfolio content:\", error);\n        throw new Error(\"Failed to generate portfolio content\");\n    }\n}\n/**\r\n * Ensures the portfolios directory exists\r\n */ function ensurePortfoliosDir() {\n    if (!fs__WEBPACK_IMPORTED_MODULE_0___default().existsSync(PORTFOLIOS_DIR)) {\n        fs__WEBPACK_IMPORTED_MODULE_0___default().mkdirSync(PORTFOLIOS_DIR, {\n            recursive: true\n        });\n    }\n}\n/**\r\n * Saves a portfolio to a file\r\n */ function savePortfolioToFile(portfolio) {\n    try {\n        ensurePortfoliosDir();\n        const filePath = path__WEBPACK_IMPORTED_MODULE_1___default().join(PORTFOLIOS_DIR, `${portfolio.id}.json`);\n        fs__WEBPACK_IMPORTED_MODULE_0___default().writeFileSync(filePath, JSON.stringify(portfolio, null, 2));\n        console.log(`Portfolio saved to: ${filePath}`);\n    } catch (error) {\n        console.error(\"Error saving portfolio to file:\", error);\n        throw error;\n    }\n}\n/**\r\n * Retrieves a portfolio by ID\r\n */ async function getPortfolioById(id) {\n    try {\n        const filePath = path__WEBPACK_IMPORTED_MODULE_1___default().join(PORTFOLIOS_DIR, `${id}.json`);\n        if (!fs__WEBPACK_IMPORTED_MODULE_0___default().existsSync(filePath)) {\n            console.log(`Portfolio not found: ${id}`);\n            return null;\n        }\n        const fileContent = fs__WEBPACK_IMPORTED_MODULE_0___default().readFileSync(filePath, \"utf-8\");\n        const portfolio = JSON.parse(fileContent);\n        // Convert date strings back to Date objects\n        portfolio.createdAt = new Date(portfolio.createdAt);\n        portfolio.updatedAt = new Date(portfolio.updatedAt);\n        console.log(`Portfolio loaded: ${id}`);\n        return portfolio;\n    } catch (error) {\n        console.error(\"Error loading portfolio:\", error);\n        return null;\n    }\n}\n/**\r\n * Updates a portfolio\r\n */ async function updatePortfolio(id, updates) {\n    try {\n        // Get the existing portfolio\n        const portfolio = await getPortfolioById(id);\n        if (!portfolio) {\n            return null;\n        }\n        // Apply the updates\n        const updatedPortfolio = {\n            ...portfolio,\n            ...updates,\n            updatedAt: new Date()\n        };\n        // Save the updated portfolio\n        savePortfolioToFile(updatedPortfolio);\n        return updatedPortfolio;\n    } catch (error) {\n        console.error(\"Error updating portfolio:\", error);\n        return null;\n    }\n}\n/**\r\n * Publishes a portfolio\r\n */ async function publishPortfolio(id) {\n    try {\n        // Update the portfolio status to published\n        return await updatePortfolio(id, {\n            status: \"published\",\n            deploymentUrl: `https://portfolio.example.com/${id}`\n        });\n    } catch (error) {\n        console.error(\"Error publishing portfolio:\", error);\n        return null;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/services/portfolio-generator.ts\n");

/***/ }),

/***/ "(rsc)/./src/services/template-processor.ts":
/*!********************************************!*\
  !*** ./src/services/template-processor.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createSampleTemplateConfig: () => (/* binding */ createSampleTemplateConfig),\n/* harmony export */   loadAllTemplates: () => (/* binding */ loadAllTemplates),\n/* harmony export */   loadTemplateConfig: () => (/* binding */ loadTemplateConfig),\n/* harmony export */   processTemplate: () => (/* binding */ processTemplate),\n/* harmony export */   saveProcessedTemplate: () => (/* binding */ saveProcessedTemplate)\n/* harmony export */ });\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var jsdom__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! jsdom */ \"jsdom\");\n/* harmony import */ var jsdom__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(jsdom__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\n/**\r\n * Loads template configuration from the template directory\r\n */ async function loadTemplateConfig(templateId) {\n    try {\n        // Try different possible paths for the template config\n        const possiblePaths = [\n            // From project root\n            path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"templates\", \"html-templates\", templateId, \"config.json\"),\n            // From parent directory (when running from frontend folder)\n            path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"..\", \"templates\", \"html-templates\", templateId, \"config.json\"),\n            // From frontend public directory\n            path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"frontend\", \"public\", \"templates\", \"html-templates\", templateId, \"config.json\"),\n            // Alternative frontend path\n            path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"frontend\", \"templates\", \"html-templates\", templateId, \"config.json\")\n        ];\n        for (const configPath of possiblePaths){\n            if (fs__WEBPACK_IMPORTED_MODULE_0___default().existsSync(configPath)) {\n                console.log(`Loading template config from: ${configPath}`);\n                const configData = fs__WEBPACK_IMPORTED_MODULE_0___default().readFileSync(configPath, \"utf-8\");\n                return JSON.parse(configData);\n            }\n        }\n        console.error(`Template config not found for ${templateId} in any of the following paths:`, possiblePaths);\n        return null;\n    } catch (error) {\n        console.error(`Error loading template config for ${templateId}:`, error);\n        return null;\n    }\n}\n/**\r\n * Loads all available templates\r\n */ async function loadAllTemplates() {\n    try {\n        // Try different possible paths for templates directory\n        const possibleTemplatesDirs = [\n            path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"templates\", \"html-templates\"),\n            path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"..\", \"templates\", \"html-templates\"),\n            path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"frontend\", \"templates\", \"html-templates\")\n        ];\n        let templatesDir = null;\n        for (const testDir of possibleTemplatesDirs){\n            console.log(\"Testing templates directory:\", testDir);\n            if (fs__WEBPACK_IMPORTED_MODULE_0___default().existsSync(testDir)) {\n                templatesDir = testDir;\n                console.log(\"Found templates directory at:\", templatesDir);\n                break;\n            }\n        }\n        if (!templatesDir) {\n            console.warn(\"Templates directory not found in any of the expected locations:\", possibleTemplatesDirs);\n            return [];\n        }\n        const templateFolders = fs__WEBPACK_IMPORTED_MODULE_0___default().readdirSync(templatesDir, {\n            withFileTypes: true\n        }).filter((dirent)=>dirent.isDirectory()).map((dirent)=>dirent.name);\n        console.log(\"Found template folders:\", templateFolders);\n        const templates = [];\n        for (const folder of templateFolders){\n            const config = await loadTemplateConfig(folder);\n            if (config) {\n                templates.push(config);\n                console.log(`Loaded template: ${config.name} (${config.id})`);\n            }\n        }\n        return templates;\n    } catch (error) {\n        console.error(\"Error loading templates:\", error);\n        return [];\n    }\n}\n/**\r\n * Processes an HTML template by replacing placeholders with actual content\r\n */ async function processTemplate(templateId, content, colors) {\n    try {\n        console.log(\"=== PROCESS TEMPLATE CALLED ===\");\n        console.log(`Processing template ${templateId} with content:`, content);\n        console.log(\"Colors:\", colors);\n        const config = await loadTemplateConfig(templateId);\n        if (!config) {\n            throw new Error(`Template config not found for ${templateId}`);\n        }\n        console.log(\"Template config loaded successfully\");\n        // Try different possible paths for the template HTML file\n        const possibleTemplatePaths = [\n            path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"templates\", \"html-templates\", templateId, config.mainFile),\n            path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"..\", \"templates\", \"html-templates\", templateId, config.mainFile),\n            path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"frontend\", \"templates\", \"html-templates\", templateId, config.mainFile)\n        ];\n        let templatePath = null;\n        for (const testPath of possibleTemplatePaths){\n            if (fs__WEBPACK_IMPORTED_MODULE_0___default().existsSync(testPath)) {\n                templatePath = testPath;\n                console.log(`Found template file at: ${templatePath}`);\n                break;\n            }\n        }\n        if (!templatePath) {\n            throw new Error(`Template file not found for ${templateId} in any of the following paths: ${possibleTemplatePaths.join(\", \")}`);\n        }\n        // Read the HTML template\n        let htmlContent = fs__WEBPACK_IMPORTED_MODULE_0___default().readFileSync(templatePath, \"utf-8\");\n        console.log(\"Template HTML loaded, length:\", htmlContent.length);\n        // Replace content placeholders\n        console.log(\"About to replace placeholders...\");\n        htmlContent = replaceContentPlaceholders(htmlContent, content, config.placeholders);\n        console.log(\"Placeholders replaced, new length:\", htmlContent.length);\n        // Process the HTML with JSDOM to modify styles and other elements\n        const dom = new jsdom__WEBPACK_IMPORTED_MODULE_2__.JSDOM(htmlContent);\n        const document = dom.window.document;\n        console.log(\"=== STARTING TEMPLATE PROCESSING ===\");\n        console.log(\"About to apply color scheme...\");\n        // Apply color scheme\n        applyColorScheme(document, colors);\n        console.log(\"About to process dynamic sections...\");\n        // Process any dynamic sections (skills, projects, experience)\n        processDynamicSections(document, content, config);\n        console.log(\"Template processing completed, serializing...\");\n        // Return the processed HTML\n        const finalHtml = dom.serialize();\n        console.log(\"Final HTML length:\", finalHtml.length);\n        console.log(\"=== PROCESS TEMPLATE COMPLETED ===\");\n        return finalHtml;\n    } catch (error) {\n        console.error(`Error processing template ${templateId}:`, error);\n        return null;\n    }\n}\n/**\r\n * Replaces content placeholders in the HTML\r\n */ function replaceContentPlaceholders(html, content, placeholders) {\n    console.log(\"=== REPLACE CONTENT PLACEHOLDERS ===\");\n    console.log(\"Content received:\", content);\n    console.log(\"Placeholders config:\", placeholders);\n    let processedHtml = html;\n    // Direct replacement for common placeholders\n    console.log(\"Replacing direct placeholders...\");\n    processedHtml = processedHtml.replace(/\\{\\{name\\}\\}/g, content.name || \"Your Name\");\n    processedHtml = processedHtml.replace(/\\{\\{title\\}\\}/g, content.title || \"Your Title\");\n    processedHtml = processedHtml.replace(/\\{\\{bio\\}\\}/g, content.bio || \"Your bio goes here...\");\n    processedHtml = processedHtml.replace(/\\{\\{email\\}\\}/g, content.email || \"<EMAIL>\");\n    processedHtml = processedHtml.replace(/\\{\\{phone\\}\\}/g, content.phone || \"+****************\");\n    // Handle social media links\n    if (content.social) {\n        processedHtml = processedHtml.replace(/\\{\\{linkedin\\}\\}/g, content.social.linkedin || \"#\");\n        processedHtml = processedHtml.replace(/\\{\\{github\\}\\}/g, content.social.github || \"#\");\n        processedHtml = processedHtml.replace(/\\{\\{twitter\\}\\}/g, content.social.twitter || \"#\");\n    } else {\n        processedHtml = processedHtml.replace(/\\{\\{linkedin\\}\\}/g, \"#\");\n        processedHtml = processedHtml.replace(/\\{\\{github\\}\\}/g, \"#\");\n        processedHtml = processedHtml.replace(/\\{\\{twitter\\}\\}/g, \"#\");\n    }\n    // Process each content type from config\n    for (const [key, placeholderList] of Object.entries(placeholders)){\n        if (content[key] && typeof content[key] === \"string\") {\n            console.log(`Processing ${key}: ${content[key]}`);\n            // Replace simple string placeholders\n            for (const placeholder of placeholderList){\n                const regex = new RegExp(placeholder.replace(/[.*+?^${}()|[\\]\\\\]/g, \"\\\\$&\"), \"g\");\n                processedHtml = processedHtml.replace(regex, content[key]);\n                console.log(`Replaced ${placeholder} with ${content[key]}`);\n            }\n        }\n    }\n    console.log(\"Placeholder replacement completed\");\n    return processedHtml;\n}\n/**\r\n * Applies the color scheme to the HTML document\r\n */ function applyColorScheme(document, colors) {\n    // Find all style tags\n    const styleTags = document.querySelectorAll(\"style\");\n    styleTags.forEach((styleTag)=>{\n        let cssContent = styleTag.textContent || \"\";\n        // Replace color variables or specific color values\n        cssContent = cssContent.replace(/--primary-color:\\s*[^;]+;/g, `--primary-color: ${colors.primary};`).replace(/--secondary-color:\\s*[^;]+;/g, `--secondary-color: ${colors.secondary};`).replace(/--accent-color:\\s*[^;]+;/g, `--accent-color: ${colors.accent};`);\n        styleTag.textContent = cssContent;\n    });\n    // Also look for inline styles with color properties\n    const elementsWithStyle = document.querySelectorAll('[style*=\"color\"]');\n    elementsWithStyle.forEach((element)=>{\n        const style = element.getAttribute(\"style\") || \"\";\n        // Replace color values in inline styles\n        const updatedStyle = style.replace(/color:\\s*var\\(--primary-color\\)/g, `color: ${colors.primary}`).replace(/color:\\s*var\\(--secondary-color\\)/g, `color: ${colors.secondary}`).replace(/color:\\s*var\\(--accent-color\\)/g, `color: ${colors.accent}`).replace(/background-color:\\s*var\\(--primary-color\\)/g, `background-color: ${colors.primary}`).replace(/background-color:\\s*var\\(--secondary-color\\)/g, `background-color: ${colors.secondary}`).replace(/background-color:\\s*var\\(--accent-color\\)/g, `background-color: ${colors.accent}`);\n        element.setAttribute(\"style\", updatedStyle);\n    });\n}\n/**\r\n * Processes dynamic sections like skills, projects, and experience\r\n */ function processDynamicSections(document, content, config) {\n    console.log(\"=== PROCESSING DYNAMIC SECTIONS ===\");\n    console.log(\"Content received:\", content);\n    console.log(\"Skills:\", content.skills);\n    console.log(\"Projects:\", content.projects);\n    console.log(\"Experience:\", content.experience);\n    // Process skills section\n    processSkillsSection(document, content.skills);\n    // Process projects section\n    processProjectsSection(document, content.projects);\n    // Process experience section\n    processExperienceSection(document, content.experience);\n}\n/**\r\n * Processes the skills section\r\n */ function processSkillsSection(document, skills) {\n    console.log(\"=== PROCESSING SKILLS ===\");\n    console.log(\"Skills to process:\", skills);\n    const skillsContainer = document.querySelector('.skills-container, #skills-container, [data-section=\"skills\"]');\n    console.log(\"Skills container found:\", !!skillsContainer);\n    if (skillsContainer && skills && skills.length > 0) {\n        console.log(\"Processing\", skills.length, \"skills\");\n        // Clear existing skills\n        skillsContainer.innerHTML = \"\";\n        // Create skill elements for each skill\n        skills.forEach((skill)=>{\n            const skillElement = document.createElement(\"div\");\n            skillElement.className = \"skill\";\n            skillElement.textContent = skill;\n            skillsContainer.appendChild(skillElement);\n            console.log(\"Added skill:\", skill);\n        });\n    } else {\n        console.log(\"No skills container found or no skills to process\");\n        console.log(\"Skills array:\", skills);\n        console.log(\"Skills container:\", skillsContainer);\n    }\n}\n/**\r\n * Processes the projects section\r\n */ function processProjectsSection(document, projects) {\n    console.log(\"=== PROCESSING PROJECTS ===\");\n    console.log(\"Projects to process:\", projects);\n    const projectsContainer = document.querySelector('.projects-container, #projects-container, [data-section=\"projects\"]');\n    console.log(\"Projects container found:\", !!projectsContainer);\n    if (projectsContainer && projects && projects.length > 0) {\n        console.log(\"Processing\", projects.length, \"projects\");\n        // Clear existing projects\n        projectsContainer.innerHTML = \"\";\n        // Create project elements for each project\n        projects.forEach((project)=>{\n            const projectElement = document.createElement(\"div\");\n            projectElement.className = \"project\";\n            // Create project content\n            projectElement.innerHTML = `\r\n        <img src=\"${project.image || \"/placeholder-project.jpg\"}\" alt=\"${project.title}\">\r\n        <div class=\"project-content\">\r\n          <h3 class=\"project-title\">${project.title}</h3>\r\n          <p class=\"project-description\">${project.description}</p>\r\n          ${project.link ? `<a href=\"${project.link}\" class=\"project-link\" target=\"_blank\">View Project</a>` : \"\"}\r\n        </div>\r\n      `;\n            projectsContainer.appendChild(projectElement);\n            console.log(\"Added project:\", project.title);\n        });\n    } else {\n        console.log(\"No projects container found or no projects to process\");\n    }\n}\n/**\r\n * Processes the experience section\r\n */ function processExperienceSection(document, experiences) {\n    console.log(\"=== PROCESSING EXPERIENCE ===\");\n    console.log(\"Experience to process:\", experiences);\n    const experienceContainer = document.querySelector('.experience-container, #experience-container, [data-section=\"experience\"]');\n    console.log(\"Experience container found:\", !!experienceContainer);\n    if (experienceContainer && experiences.length > 0) {\n        console.log(\"Processing\", experiences.length, \"experience items\");\n        // Clear existing experiences\n        experienceContainer.innerHTML = \"\";\n        // Create experience elements for each experience\n        experiences.forEach((exp)=>{\n            const expElement = document.createElement(\"div\");\n            expElement.className = \"experience-item\";\n            const dateText = exp.endDate ? `${exp.startDate} - ${exp.endDate}` : `${exp.startDate} - Present`;\n            // Create experience content\n            expElement.innerHTML = `\r\n        <h3 class=\"job-title\">${exp.title}</h3>\r\n        <p class=\"company-name\">${exp.company}</p>\r\n        <p class=\"job-date\">${dateText}</p>\r\n        <p class=\"job-description\">${exp.description}</p>\r\n      `;\n            experienceContainer.appendChild(expElement);\n            console.log(\"Added experience:\", exp.title, \"at\", exp.company);\n        });\n    } else {\n        console.log(\"No experience container found or no experience to process\");\n    }\n}\n/**\r\n * Saves the processed HTML to the output directory\r\n */ async function saveProcessedTemplate(portfolioId, html, templateId) {\n    try {\n        // Try different possible output directories\n        const possibleOutputDirs = [\n            path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"data\", \"portfolios\", portfolioId),\n            path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"frontend\", \"data\", \"portfolios\", portfolioId),\n            path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"..\", \"data\", \"portfolios\", portfolioId)\n        ];\n        // Use the first directory that we can create or that already exists\n        let outputDir = possibleOutputDirs[0]; // Default to first option\n        // Try to create the directory\n        if (!fs__WEBPACK_IMPORTED_MODULE_0___default().existsSync(outputDir)) {\n            fs__WEBPACK_IMPORTED_MODULE_0___default().mkdirSync(outputDir, {\n                recursive: true\n            });\n        }\n        console.log(`Saving portfolio to: ${outputDir}`);\n        // Save the HTML file\n        const htmlPath = path__WEBPACK_IMPORTED_MODULE_1___default().join(outputDir, \"index.html\");\n        fs__WEBPACK_IMPORTED_MODULE_0___default().writeFileSync(htmlPath, html);\n        console.log(`HTML file saved to: ${htmlPath}`);\n        // Find the template directory and copy assets\n        const possibleTemplateDirs = [\n            path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"templates\", \"html-templates\", templateId),\n            path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"..\", \"templates\", \"html-templates\", templateId),\n            path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"frontend\", \"templates\", \"html-templates\", templateId)\n        ];\n        let templateDir = null;\n        for (const testDir of possibleTemplateDirs){\n            if (fs__WEBPACK_IMPORTED_MODULE_0___default().existsSync(testDir)) {\n                templateDir = testDir;\n                console.log(`Found template directory at: ${templateDir}`);\n                break;\n            }\n        }\n        if (templateDir) {\n            copyTemplateAssets(templateDir, outputDir);\n        } else {\n            console.warn(`Template directory not found for ${templateId}`);\n        }\n        return outputDir;\n    } catch (error) {\n        console.error(`Error saving processed template for portfolio ${portfolioId}:`, error);\n        throw new Error(\"Failed to save processed template\");\n    }\n}\n/**\r\n * Copies all assets from the template directory to the output directory\r\n */ function copyTemplateAssets(sourceDir, targetDir) {\n    // Read all files and directories in the source directory\n    const items = fs__WEBPACK_IMPORTED_MODULE_0___default().readdirSync(sourceDir, {\n        withFileTypes: true\n    });\n    for (const item of items){\n        const sourcePath = path__WEBPACK_IMPORTED_MODULE_1___default().join(sourceDir, item.name);\n        const targetPath = path__WEBPACK_IMPORTED_MODULE_1___default().join(targetDir, item.name);\n        if (item.isDirectory()) {\n            // Create the directory in the target\n            if (!fs__WEBPACK_IMPORTED_MODULE_0___default().existsSync(targetPath)) {\n                fs__WEBPACK_IMPORTED_MODULE_0___default().mkdirSync(targetPath, {\n                    recursive: true\n                });\n            }\n            // Recursively copy contents\n            copyTemplateAssets(sourcePath, targetPath);\n        } else if (item.isFile() && !item.name.endsWith(\".html\") && item.name !== \"config.json\") {\n            // Copy the file (excluding HTML and config files)\n            fs__WEBPACK_IMPORTED_MODULE_0___default().copyFileSync(sourcePath, targetPath);\n        }\n    }\n}\n/**\r\n * Creates a sample template config file\r\n */ function createSampleTemplateConfig(templateId, templateName, description) {\n    return {\n        id: templateId,\n        name: templateName,\n        description: description,\n        folderPath: templateId,\n        mainFile: \"index.html\",\n        previewImage: `/${templateId}/preview.jpg`,\n        placeholders: {\n            name: [\n                \"{{NAME}}\",\n                \"{{name}}\",\n                \"{{User Name}}\"\n            ],\n            title: [\n                \"{{TITLE}}\",\n                \"{{title}}\",\n                \"{{Job Title}}\"\n            ],\n            bio: [\n                \"{{BIO}}\",\n                \"{{bio}}\",\n                \"{{About Me}}\",\n                \"{{about}}\"\n            ],\n            skills: [\n                \"{{SKILLS}}\",\n                \"{{skills}}\"\n            ],\n            projects: [\n                \"{{PROJECTS}}\",\n                \"{{projects}}\"\n            ],\n            experience: [\n                \"{{EXPERIENCE}}\",\n                \"{{experience}}\",\n                \"{{work}}\"\n            ],\n            contact: [\n                \"{{CONTACT}}\",\n                \"{{contact}}\",\n                \"{{email}}\",\n                \"{{phone}}\"\n            ],\n            social: [\n                \"{{SOCIAL}}\",\n                \"{{social}}\",\n                \"{{linkedin}}\",\n                \"{{github}}\",\n                \"{{twitter}}\"\n            ]\n        }\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./src/services/template-processor.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/uuid"], () => (__webpack_exec__("(rsc)/../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fportfolio%2Fgenerate%2Froute&page=%2Fapi%2Fportfolio%2Fgenerate%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fportfolio%2Fgenerate%2Froute.ts&appDir=C%3A%5CUsers%5CHIMANSHU%5COneDrive%5CDesktop%5Cportfolio%20saas%20-%20Copy%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHIMANSHU%5COneDrive%5CDesktop%5Cportfolio%20saas%20-%20Copy%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();