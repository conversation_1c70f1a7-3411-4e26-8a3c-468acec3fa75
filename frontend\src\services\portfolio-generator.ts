import { v4 as uuidv4 } from 'uuid';
import fs from 'fs';
import path from 'path';

// Portfolio storage directory
const PORTFOLIOS_DIR = path.join(process.cwd(), 'frontend', 'data', 'portfolios');

interface PortfolioGenerationConfig {
  profession: string;
  template: {
    id: string;
    name: string;
    previewComponent: string;
    description: string;
  };
  colors: {
    primary: string;
    secondary: string;
    accent: string;
  };
  sections: Array<{
    id: string;
    name: string;
    description: string;
    isRequired?: boolean;
    isEnabled: boolean;
  }>;
  content: {
    name: string;
    title: string;
    bio: string;
    skills: string[];
    experience: Array<{
      title: string;
      company: string;
      description: string;
      startDate: string;
      endDate?: string;
    }>;
    projects: Array<{
      title: string;
      description: string;
      image?: string;
      link?: string;
    }>;
    social: {
      linkedin?: string;
      github?: string;
      twitter?: string;
      other?: string;
    };
    [key: string]: any;
  };
}

interface GeneratedPortfolio {
  id: string;
  createdAt: Date;
  updatedAt: Date;
  template: string;
  profession: string;
  colors: {
    primary: string;
    secondary: string;
    accent: string;
  };
  sections: string[];
  content: Record<string, any>;
  deploymentUrl?: string;
  status: 'draft' | 'published' | 'archived';
}

/**
 * Generates the portfolio content based on the user's configuration
 */
export async function generatePortfolioContent(config: PortfolioGenerationConfig): Promise<GeneratedPortfolio> {
  try {
    // Generate a unique ID for the portfolio
    const portfolioId = uuidv4();
    
    // Create the portfolio object
    const portfolio: GeneratedPortfolio = {
      id: portfolioId,
      createdAt: new Date(),
      updatedAt: new Date(),
      template: config.template.id,
      profession: config.profession,
      colors: config.colors,
      sections: config.sections.map(section => section.id),
      content: {
        ...config.content,
        // Add any additional processed content here
      },
      status: 'draft'
    };
    
    // Save the portfolio to file system
    savePortfolioToFile(portfolio);
    console.log('Portfolio saved successfully:', portfolioId);

    // Return the portfolio data
    return portfolio;
  } catch (error) {
    console.error('Error generating portfolio content:', error);
    throw new Error('Failed to generate portfolio content');
  }
}

/**
 * Ensures the portfolios directory exists
 */
function ensurePortfoliosDir(): void {
  if (!fs.existsSync(PORTFOLIOS_DIR)) {
    fs.mkdirSync(PORTFOLIOS_DIR, { recursive: true });
  }
}

/**
 * Saves a portfolio to a file
 */
function savePortfolioToFile(portfolio: GeneratedPortfolio): void {
  try {
    ensurePortfoliosDir();
    const filePath = path.join(PORTFOLIOS_DIR, `${portfolio.id}.json`);
    fs.writeFileSync(filePath, JSON.stringify(portfolio, null, 2));
    console.log(`Portfolio saved to: ${filePath}`);
  } catch (error) {
    console.error('Error saving portfolio to file:', error);
    throw error;
  }
}

/**
 * Retrieves a portfolio by ID
 */
export async function getPortfolioById(id: string): Promise<GeneratedPortfolio | null> {
  try {
    const filePath = path.join(PORTFOLIOS_DIR, `${id}.json`);

    if (!fs.existsSync(filePath)) {
      console.log(`Portfolio not found: ${id}`);
      return null;
    }

    const fileContent = fs.readFileSync(filePath, 'utf-8');
    const portfolio = JSON.parse(fileContent);

    // Convert date strings back to Date objects
    portfolio.createdAt = new Date(portfolio.createdAt);
    portfolio.updatedAt = new Date(portfolio.updatedAt);

    console.log(`Portfolio loaded: ${id}`);
    return portfolio;
  } catch (error) {
    console.error('Error loading portfolio:', error);
    return null;
  }
}

/**
 * Updates a portfolio
 */
export async function updatePortfolio(id: string, updates: Partial<GeneratedPortfolio>): Promise<GeneratedPortfolio | null> {
  try {
    // Get the existing portfolio
    const portfolio = await getPortfolioById(id);
    if (!portfolio) {
      return null;
    }
    
    // Apply the updates
    const updatedPortfolio: GeneratedPortfolio = {
      ...portfolio,
      ...updates,
      updatedAt: new Date()
    };
    
    // Save the updated portfolio
    savePortfolioToFile(updatedPortfolio);
    
    return updatedPortfolio;
  } catch (error) {
    console.error('Error updating portfolio:', error);
    return null;
  }
}

/**
 * Publishes a portfolio
 */
export async function publishPortfolio(id: string): Promise<GeneratedPortfolio | null> {
  try {
    // Update the portfolio status to published
    return await updatePortfolio(id, { 
      status: 'published',
      deploymentUrl: `https://portfolio.example.com/${id}`
    });
  } catch (error) {
    console.error('Error publishing portfolio:', error);
    return null;
  }
}
