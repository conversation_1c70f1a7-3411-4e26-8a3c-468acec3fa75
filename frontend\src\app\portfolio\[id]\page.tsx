"use client"

import { useParams } from 'next/navigation'
import { DynamicPortfolio } from '@/components/portfolio/dynamic-portfolio'

export default function PortfolioPage() {
  const params = useParams() || {}
  const portfolioId = typeof params.id === 'string' ? params.id : ''

  if (!portfolioId) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600 mb-4">Invalid Portfolio ID</h1>
          <p className="text-gray-600">The portfolio ID is missing or invalid.</p>
        </div>
      </div>
    )
  }

  return <DynamicPortfolio portfolioId={portfolioId} />
}
