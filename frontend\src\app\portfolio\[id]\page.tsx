"use client"

import { useEffect, useState } from 'react'
import { useParams } from 'next/navigation'
import dynamic from 'next/dynamic'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'

// Simple Skeleton component
const Skeleton = ({ className }: { className: string }) => (
  <div className={`animate-pulse bg-gray-200 rounded ${className}`}></div>
)

// Define the portfolio data interface
interface PortfolioData {
  id: string
  template: string
  profession: string
  colors: {
    primary: string
    secondary: string
    accent: string
  }
  sections: string[]
  content: Record<string, any>
  deploymentUrl?: string
  status: 'draft' | 'published' | 'archived'
}

export default function PortfolioPage() {
  const params = useParams() || {}
  const portfolioId = typeof params.id === 'string' ? params.id : ''
  const [htmlContent, setHtmlContent] = useState<string>('')
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const loadPortfolio = async () => {
      if (!portfolioId) {
        setError('Invalid portfolio ID')
        setLoading(false)
        return
      }

      try {
        setLoading(true)
        // Try to load the generated HTML file directly
        const htmlResponse = await fetch(`/api/portfolio/${portfolioId}/html`)

        if (htmlResponse.ok) {
          const html = await htmlResponse.text()
          setHtmlContent(html)
        } else {
          setError('Portfolio not found')
        }
      } catch (err) {
        setError('Failed to load portfolio')
        console.error(err)
      } finally {
        setLoading(false)
      }
    }

    loadPortfolio()
  }, [portfolioId])

  if (loading) {
    return <TemplateLoading />
  }

  if (error) {
    return (
      <div className="container mx-auto py-12 px-4">
        <Card>
          <CardContent className="py-12 text-center">
            <h1 className="text-2xl font-bold text-red-500 mb-4">
              {error}
            </h1>
            <p className="mb-6">
              The portfolio you're looking for could not be loaded.
            </p>
            <Button onClick={() => window.history.back()}>
              Go Back
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (htmlContent) {
    return (
      <div
        className="portfolio-viewer"
        dangerouslySetInnerHTML={{ __html: htmlContent }}
        style={{ width: '100%', height: '100vh' }}
      />
    )
  }

  return <TemplateLoading />
}

function TemplateLoading() {
  return (
    <div className="container mx-auto py-12 px-4">
      <Skeleton className="h-[50px] w-full mb-6" />
      <Skeleton className="h-[300px] w-full mb-6" />
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <Skeleton className="h-[200px] w-full" />
        <Skeleton className="h-[200px] w-full" />
      </div>
      <Skeleton className="h-[400px] w-full" />
    </div>
  )
}
