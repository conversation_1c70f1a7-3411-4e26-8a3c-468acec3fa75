"use client"

import { useEffect, useState } from 'react'
import { useParams } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'

export default function PortfolioPage() {
  const params = useParams() || {}
  const portfolioId = typeof params.id === 'string' ? params.id : ''
  const [htmlContent, setHtmlContent] = useState<string>('')
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const loadPortfolio = async () => {
      if (!portfolioId) {
        setError('Invalid portfolio ID')
        setLoading(false)
        return
      }

      try {
        setLoading(true)
        console.log(`Loading portfolio HTML for ID: ${portfolioId}`)

        // Try to load the generated HTML file directly
        const htmlResponse = await fetch(`/api/portfolio/${portfolioId}/html`)

        if (htmlResponse.ok) {
          const html = await htmlResponse.text()
          console.log(`HTML loaded successfully, length: ${html.length}`)
          setHtmlContent(html)
        } else {
          console.error(`Failed to load HTML: ${htmlResponse.status}`)
          setError('Portfolio not found')
        }
      } catch (err) {
        console.error('Error loading portfolio:', err)
        setError('Failed to load portfolio')
      } finally {
        setLoading(false)
      }
    }

    loadPortfolio()
  }, [portfolioId])

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p>Loading portfolio...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="container mx-auto py-12 px-4">
        <Card>
          <CardContent className="py-12 text-center">
            <h1 className="text-2xl font-bold text-red-500 mb-4">
              {error}
            </h1>
            <p className="mb-6">
              The portfolio you're looking for could not be loaded.
            </p>
            <Button onClick={() => window.history.back()}>
              Go Back
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (htmlContent) {
    return (
      <div
        className="portfolio-viewer"
        dangerouslySetInnerHTML={{ __html: htmlContent }}
        style={{ width: '100%', minHeight: '100vh' }}
      />
    )
  }

  return (
    <div className="min-h-screen flex items-center justify-center">
      <p>No content available</p>
    </div>
  )
}
