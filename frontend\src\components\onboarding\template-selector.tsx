"use client"

import * as React from "react"
import Image from "next/image"
import { <PERSON>, <PERSON> } from "lucide-react"
import { cn } from "@/lib/utils"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>rollBar } from "@/components/ui/scroll-area"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"

interface TemplateSelectorProps {
  value: string
  onChange: (value: string) => void
  previewContent?: {
    name: string;
    title: string;
    bio: string;
    // Additional content that might be available from earlier steps
  };
}

interface Template {
  id: string;
  name: string;
  description: string;
  previewImage: string;
}

// Define the template config interface from API
interface ApiTemplateConfig {
  id: string;
  name: string;
  description: string;
  previewImage?: string;
  folderPath?: string;
  mainFile?: string;
  placeholders?: Record<string, string[]>;
}

export function TemplateSelector({ value, onChange, previewContent = {
  name: "<PERSON>",
  title: "Software Developer",
  bio: "Passionate software developer with 5+ years of experience in building web applications.",
} }: TemplateSelectorProps) {
  const [templates, setTemplates] = React.useState<Template[]>([]);
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState<string | null>(null);
  
  // Load templates on component mount
  React.useEffect(() => {
    const fetchTemplates = async () => {
      try {
        setLoading(true);
        // Use the new API endpoint instead of directly calling loadAllTemplates
        const response = await fetch('/api/templates');
        
        if (!response.ok) {
          throw new Error('Failed to fetch templates');
        }
        
        const data = await response.json();
        const templateConfigs: ApiTemplateConfig[] = data.templates;
        
        const formattedTemplates = templateConfigs.map((config: ApiTemplateConfig) => ({
          id: config.id,
          name: config.name,
          description: config.description,
          previewImage: `/api/templates/${config.id}/preview`
        }));
        
        setTemplates(formattedTemplates);
        
        // If no template is selected and we have templates, select the first one
        if (!value && formattedTemplates.length > 0) {
          onChange(formattedTemplates[0].id);
        }
      } catch (err) {
        console.error('Error loading templates:', err);
        setError('Failed to load templates. Please try again later.');
      } finally {
        setLoading(false);
      }
    };
    
    fetchTemplates();
  }, [value, onChange]);
  
  if (loading) {
    return <div className="flex justify-center items-center p-8">Loading templates...</div>;
  }
  
  if (error) {
    return <div className="text-red-500 p-4">{error}</div>;
  }
  
  if (templates.length === 0) {
    return <div className="p-4">No templates available. Please add templates to the templates/html-templates directory.</div>;
  }

  return (
    <>
      <ScrollArea className="w-full">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pb-4">
          {templates.map((template) => (
            <Card
              key={template.id}
              className={cn(
                "relative cursor-pointer transition-all hover:border-primary",
                value === template.id && "border-primary"
              )}
              onClick={() => onChange(template.id)}
            >
              {value === template.id && (
                <div className="absolute top-2 right-2 h-6 w-6 rounded-full bg-primary text-primary-foreground flex items-center justify-center">
                  <Check className="h-4 w-4" />
                </div>
              )}
              <CardHeader className="pb-4">
                <CardTitle className="text-lg">{template.name}</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="aspect-video relative rounded-lg overflow-hidden border">
                  <Image
                    src={template.previewImage}
                    alt={template.name}
                    fill
                    className="object-cover"
                    priority
                    unoptimized
                  />
                </div>
                <div className="flex justify-between items-center mt-3">
                  <p className="text-sm text-muted-foreground">
                    {template.description}
                  </p>
                  <Dialog>
                    <DialogTrigger asChild>
                      <Button 
                        variant="outline" 
                        size="sm"
                        className="flex items-center gap-1"
                        onClick={(e) => {
                          e.stopPropagation(); // Prevent card click from triggering
                        }}
                      >
                        <Eye className="h-4 w-4" />
                        Preview
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="max-w-screen-lg w-full max-h-[80vh] overflow-y-auto">
                      <DialogHeader>
                        <DialogTitle>{template.name} Template Preview</DialogTitle>
                      </DialogHeader>
                      <div className="mt-4 rounded-lg border overflow-hidden">
                        <iframe 
                          src={`/api/preview-template?templateId=${template.id}`} 
                          className="w-full h-[60vh] border-0"
                          title={`${template.name} Preview`}
                        />
                      </div>
                      <div className="text-sm text-muted-foreground mt-2">
                        <p>This is a preview with sample content. Your actual portfolio will use your own information.</p>
                      </div>
                    </DialogContent>
                  </Dialog>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
        <ScrollBar orientation="horizontal" />
      </ScrollArea>
    </>
  )
} 