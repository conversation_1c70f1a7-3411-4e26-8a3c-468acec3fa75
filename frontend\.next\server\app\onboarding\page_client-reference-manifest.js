globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/onboarding/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/../node_modules/next/dist/client/components/app-router.js":{"*":{"id":"(ssr)/../node_modules/next/dist/client/components/app-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/../node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/../node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../node_modules/next/dist/client/components/not-found-boundary.js":{"*":{"id":"(ssr)/../node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/../node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js":{"*":{"id":"(ssr)/../node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../node_modules/next/dist/client/link.js":{"*":{"id":"(ssr)/../node_modules/next/dist/client/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/navbar.tsx":{"*":{"id":"(ssr)/./src/components/navbar.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/theme-provider.tsx":{"*":{"id":"(ssr)/./src/components/theme-provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/onboarding/page.tsx":{"*":{"id":"(ssr)/./src/app/onboarding/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\OneDrive\\Desktop\\portfolio saas - Copy\\node_modules\\next\\dist\\client\\components\\app-router.js":{"id":"(app-pages-browser)/../node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\portfolio saas - Copy\\node_modules\\next\\dist\\esm\\client\\components\\app-router.js":{"id":"(app-pages-browser)/../node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\portfolio saas - Copy\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/../node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\portfolio saas - Copy\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/../node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\portfolio saas - Copy\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/../node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\portfolio saas - Copy\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/../node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\portfolio saas - Copy\\node_modules\\next\\dist\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/../node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\portfolio saas - Copy\\node_modules\\next\\dist\\esm\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/../node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\portfolio saas - Copy\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/../node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\portfolio saas - Copy\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/../node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\portfolio saas - Copy\\node_modules\\next\\dist\\client\\components\\static-generation-searchparams-bailout-provider.js":{"id":"(app-pages-browser)/../node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\portfolio saas - Copy\\node_modules\\next\\dist\\esm\\client\\components\\static-generation-searchparams-bailout-provider.js":{"id":"(app-pages-browser)/../node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\portfolio saas - Copy\\node_modules\\next\\dist\\client\\link.js":{"id":"(app-pages-browser)/../node_modules/next/dist/client/link.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\portfolio saas - Copy\\node_modules\\next\\dist\\esm\\client\\link.js":{"id":"(app-pages-browser)/../node_modules/next/dist/client/link.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\portfolio saas - Copy\\frontend\\src\\app\\globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\portfolio saas - Copy\\frontend\\src\\components\\navbar.tsx":{"id":"(app-pages-browser)/./src/components/navbar.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\portfolio saas - Copy\\frontend\\src\\components\\theme-provider.tsx":{"id":"(app-pages-browser)/./src/components/theme-provider.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\portfolio saas - Copy\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/../node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\portfolio saas - Copy\\frontend\\src\\app\\onboarding\\page.tsx":{"id":"(app-pages-browser)/./src/app/onboarding/page.tsx","name":"*","chunks":["app/onboarding/page","static/chunks/app/onboarding/page.js"],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\OneDrive\\Desktop\\portfolio saas - Copy\\frontend\\src\\app\\page":[],"C:\\Users\\<USER>\\OneDrive\\Desktop\\portfolio saas - Copy\\frontend\\src\\app\\layout":["static/css/app/layout.css"],"C:\\Users\\<USER>\\OneDrive\\Desktop\\portfolio saas - Copy\\frontend\\src\\app\\not-found":[],"C:\\Users\\<USER>\\OneDrive\\Desktop\\portfolio saas - Copy\\frontend\\src\\app\\onboarding\\page":[]}}