"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/templates/[id]/preview/route";
exports.ids = ["app/api/templates/[id]/preview/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "(rsc)/../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Ftemplates%2F%5Bid%5D%2Fpreview%2Froute&page=%2Fapi%2Ftemplates%2F%5Bid%5D%2Fpreview%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftemplates%2F%5Bid%5D%2Fpreview%2Froute.ts&appDir=C%3A%5CUsers%5CHIMANSHU%5COneDrive%5CDesktop%5Cportfolio%20saas%20-%20Copy%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHIMANSHU%5COneDrive%5CDesktop%5Cportfolio%20saas%20-%20Copy%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Ftemplates%2F%5Bid%5D%2Fpreview%2Froute&page=%2Fapi%2Ftemplates%2F%5Bid%5D%2Fpreview%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftemplates%2F%5Bid%5D%2Fpreview%2Froute.ts&appDir=C%3A%5CUsers%5CHIMANSHU%5COneDrive%5CDesktop%5Cportfolio%20saas%20-%20Copy%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHIMANSHU%5COneDrive%5CDesktop%5Cportfolio%20saas%20-%20Copy%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/../node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/../node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/../node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_HIMANSHU_OneDrive_Desktop_portfolio_saas_Copy_frontend_src_app_api_templates_id_preview_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/templates/[id]/preview/route.ts */ \"(rsc)/./src/app/api/templates/[id]/preview/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/templates/[id]/preview/route\",\n        pathname: \"/api/templates/[id]/preview\",\n        filename: \"route\",\n        bundlePath: \"app/api/templates/[id]/preview/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\api\\\\templates\\\\[id]\\\\preview\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_HIMANSHU_OneDrive_Desktop_portfolio_saas_Copy_frontend_src_app_api_templates_id_preview_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/templates/[id]/preview/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Ftemplates%2F%5Bid%5D%2Fpreview%2Froute&page=%2Fapi%2Ftemplates%2F%5Bid%5D%2Fpreview%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftemplates%2F%5Bid%5D%2Fpreview%2Froute.ts&appDir=C%3A%5CUsers%5CHIMANSHU%5COneDrive%5CDesktop%5Cportfolio%20saas%20-%20Copy%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHIMANSHU%5COneDrive%5CDesktop%5Cportfolio%20saas%20-%20Copy%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/templates/[id]/preview/route.ts":
/*!*****************************************************!*\
  !*** ./src/app/api/templates/[id]/preview/route.ts ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/../node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nasync function GET(request, { params }) {\n    try {\n        const { id } = params;\n        if (!id) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Template ID is required\"\n            }, {\n                status: 400\n            });\n        }\n        // Try to find the preview image in the template directory\n        const templateDir = path__WEBPACK_IMPORTED_MODULE_2___default().join(process.cwd(), \"frontend\", \"templates\", \"html-templates\", id);\n        const possibleExtensions = [\n            \"png\",\n            \"jpg\",\n            \"jpeg\",\n            \"gif\",\n            \"webp\"\n        ];\n        let previewPath = null;\n        for (const ext of possibleExtensions){\n            const testPath = path__WEBPACK_IMPORTED_MODULE_2___default().join(templateDir, `preview.${ext}`);\n            if (fs__WEBPACK_IMPORTED_MODULE_1___default().existsSync(testPath)) {\n                previewPath = testPath;\n                break;\n            }\n        }\n        if (!previewPath) {\n            // Return a placeholder SVG if no preview image exists\n            const placeholderSvg = `\n        <svg width=\"400\" height=\"300\" xmlns=\"http://www.w3.org/2000/svg\">\n          <rect width=\"100%\" height=\"100%\" fill=\"#f3f4f6\"/>\n          <text x=\"50%\" y=\"50%\" text-anchor=\"middle\" dy=\".3em\" font-family=\"Arial, sans-serif\" font-size=\"16\" fill=\"#6b7280\">\n            ${id} Template Preview\n          </text>\n        </svg>\n      `;\n            return new next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"](placeholderSvg, {\n                headers: {\n                    \"Content-Type\": \"image/svg+xml\"\n                }\n            });\n        }\n        // Read and serve the actual image\n        const imageBuffer = fs__WEBPACK_IMPORTED_MODULE_1___default().readFileSync(previewPath);\n        const ext = path__WEBPACK_IMPORTED_MODULE_2___default().extname(previewPath).toLowerCase();\n        let contentType = \"image/png\";\n        switch(ext){\n            case \".jpg\":\n            case \".jpeg\":\n                contentType = \"image/jpeg\";\n                break;\n            case \".gif\":\n                contentType = \"image/gif\";\n                break;\n            case \".webp\":\n                contentType = \"image/webp\";\n                break;\n        }\n        return new next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"](imageBuffer, {\n            headers: {\n                \"Content-Type\": contentType\n            }\n        });\n    } catch (error) {\n        console.error(\"Error serving template preview:\", error);\n        // Return error placeholder\n        const errorSvg = `\n      <svg width=\"400\" height=\"300\" xmlns=\"http://www.w3.org/2000/svg\">\n        <rect width=\"100%\" height=\"100%\" fill=\"#fee2e2\"/>\n        <text x=\"50%\" y=\"50%\" text-anchor=\"middle\" dy=\".3em\" font-family=\"Arial, sans-serif\" font-size=\"16\" fill=\"#dc2626\">\n          Preview Not Available\n        </text>\n      </svg>\n    `;\n        return new next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"](errorSvg, {\n            headers: {\n                \"Content-Type\": \"image/svg+xml\"\n            }\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/templates/[id]/preview/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Ftemplates%2F%5Bid%5D%2Fpreview%2Froute&page=%2Fapi%2Ftemplates%2F%5Bid%5D%2Fpreview%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftemplates%2F%5Bid%5D%2Fpreview%2Froute.ts&appDir=C%3A%5CUsers%5CHIMANSHU%5COneDrive%5CDesktop%5Cportfolio%20saas%20-%20Copy%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHIMANSHU%5COneDrive%5CDesktop%5Cportfolio%20saas%20-%20Copy%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();