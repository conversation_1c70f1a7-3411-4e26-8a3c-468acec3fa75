"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/portfolio/[id]/html/route";
exports.ids = ["app/api/portfolio/[id]/html/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "(rsc)/../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fportfolio%2F%5Bid%5D%2Fhtml%2Froute&page=%2Fapi%2Fportfolio%2F%5Bid%5D%2Fhtml%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fportfolio%2F%5Bid%5D%2Fhtml%2Froute.ts&appDir=C%3A%5CUsers%5CHIMANSHU%5COneDrive%5CDesktop%5Cportfolio%20saas%20-%20Copy%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHIMANSHU%5COneDrive%5CDesktop%5Cportfolio%20saas%20-%20Copy%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fportfolio%2F%5Bid%5D%2Fhtml%2Froute&page=%2Fapi%2Fportfolio%2F%5Bid%5D%2Fhtml%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fportfolio%2F%5Bid%5D%2Fhtml%2Froute.ts&appDir=C%3A%5CUsers%5CHIMANSHU%5COneDrive%5CDesktop%5Cportfolio%20saas%20-%20Copy%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHIMANSHU%5COneDrive%5CDesktop%5Cportfolio%20saas%20-%20Copy%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/../node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/../node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/../node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_HIMANSHU_OneDrive_Desktop_portfolio_saas_Copy_frontend_src_app_api_portfolio_id_html_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/portfolio/[id]/html/route.ts */ \"(rsc)/./src/app/api/portfolio/[id]/html/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/portfolio/[id]/html/route\",\n        pathname: \"/api/portfolio/[id]/html\",\n        filename: \"route\",\n        bundlePath: \"app/api/portfolio/[id]/html/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\api\\\\portfolio\\\\[id]\\\\html\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_HIMANSHU_OneDrive_Desktop_portfolio_saas_Copy_frontend_src_app_api_portfolio_id_html_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/portfolio/[id]/html/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fportfolio%2F%5Bid%5D%2Fhtml%2Froute&page=%2Fapi%2Fportfolio%2F%5Bid%5D%2Fhtml%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fportfolio%2F%5Bid%5D%2Fhtml%2Froute.ts&appDir=C%3A%5CUsers%5CHIMANSHU%5COneDrive%5CDesktop%5Cportfolio%20saas%20-%20Copy%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHIMANSHU%5COneDrive%5CDesktop%5Cportfolio%20saas%20-%20Copy%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/portfolio/[id]/html/route.ts":
/*!**************************************************!*\
  !*** ./src/app/api/portfolio/[id]/html/route.ts ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/../node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nasync function GET(request, { params }) {\n    try {\n        const { id } = params;\n        if (!id) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Portfolio ID is required\"\n            }, {\n                status: 400\n            });\n        }\n        // Try to find the HTML file in the data directory\n        console.log(\"Current working directory:\", process.cwd());\n        // Try different path combinations\n        const paths = [\n            path__WEBPACK_IMPORTED_MODULE_2___default().join(process.cwd(), \"data\", \"portfolios\", id, \"index.html\"),\n            path__WEBPACK_IMPORTED_MODULE_2___default().join(process.cwd(), \"frontend\", \"data\", \"portfolios\", id, \"index.html\"),\n            path__WEBPACK_IMPORTED_MODULE_2___default().join(process.cwd(), \"..\", \"data\", \"portfolios\", id, \"index.html\"),\n            path__WEBPACK_IMPORTED_MODULE_2___default().join(process.cwd(), \"..\", \"frontend\", \"data\", \"portfolios\", id, \"index.html\")\n        ];\n        let htmlPath = null;\n        for (const testPath of paths){\n            console.log(\"Testing path:\", testPath);\n            if (fs__WEBPACK_IMPORTED_MODULE_1___default().existsSync(testPath)) {\n                htmlPath = testPath;\n                console.log(\"Found HTML file at:\", htmlPath);\n                break;\n            }\n        }\n        if (!htmlPath) {\n            console.log(\"HTML file not found in any of the tested paths\");\n            console.log(\"Looking for HTML file at:\", paths.map((p)=>`${p} (exists: ${fs__WEBPACK_IMPORTED_MODULE_1___default().existsSync(p)})`));\n        }\n        if (!htmlPath) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Portfolio HTML not found\"\n            }, {\n                status: 404\n            });\n        }\n        // Read the HTML file\n        const htmlContent = fs__WEBPACK_IMPORTED_MODULE_1___default().readFileSync(htmlPath, \"utf-8\");\n        // Return the HTML content\n        return new next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"](htmlContent, {\n            headers: {\n                \"Content-Type\": \"text/html\"\n            }\n        });\n    } catch (error) {\n        console.error(\"Error serving portfolio HTML:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Failed to serve portfolio\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/portfolio/[id]/html/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fportfolio%2F%5Bid%5D%2Fhtml%2Froute&page=%2Fapi%2Fportfolio%2F%5Bid%5D%2Fhtml%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fportfolio%2F%5Bid%5D%2Fhtml%2Froute.ts&appDir=C%3A%5CUsers%5CHIMANSHU%5COneDrive%5CDesktop%5Cportfolio%20saas%20-%20Copy%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHIMANSHU%5COneDrive%5CDesktop%5Cportfolio%20saas%20-%20Copy%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();