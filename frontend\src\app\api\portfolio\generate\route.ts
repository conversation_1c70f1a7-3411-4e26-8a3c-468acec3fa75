import { NextRequest, NextResponse } from 'next/server';
import { generatePortfolioContent } from '@/services/portfolio-generator';
import { processTemplate, saveProcessedTemplate, loadTemplateConfig } from '@/services/template-processor';

export async function POST(request: NextRequest) {
  try {
    console.log('Portfolio generation API called');

    // Parse the request body to get the user's configuration
    const requestData = await request.json();
    console.log('Request data received:', requestData);

    // Destructure the user's configuration
    const {
      profession,
      template,
      colors,
      sections,
      content
    } = requestData;

    console.log('Extracted fields:', { profession, template, colors, sections, content });

    // Validate required fields
    if (!template || !content || !colors) {
      console.error('Missing required fields:', { template: !!template, content: !!content, colors: !!colors });
      return NextResponse.json(
        { error: 'Missing required configuration fields' },
        { status: 400 }
      );
    }
    
    // Load the template configuration
    console.log('Loading template config for:', template);
    const templateConfig = await loadTemplateConfig(template);
    console.log('Template config loaded:', templateConfig);

    if (!templateConfig) {
      console.error('Template not found:', template);
      return NextResponse.json(
        { error: 'Invalid template selection or template configuration not found' },
        { status: 400 }
      );
    }
    
    // Generate the portfolio data
    const portfolioData = await generatePortfolioContent({
      profession,
      template: {
        id: templateConfig.id,
        name: templateConfig.name,
        previewComponent: templateConfig.id,
        description: templateConfig.description
      },
      colors,
      sections: sections.filter((section: any) => section.isEnabled),
      content
    });
    
    // Process the HTML template with the user's content
    console.log("=== CALLING PROCESS TEMPLATE ===");
    console.log("Template:", template);
    console.log("Content:", content);
    console.log("Colors:", colors);

    const processedHtml = await processTemplate(
      template,
      content,
      colors
    );

    console.log("=== PROCESS TEMPLATE RESULT ===");
    console.log("Processed HTML length:", processedHtml ? processedHtml.length : 'null');

    if (!processedHtml) {
      console.error("processTemplate returned null or empty");
      return NextResponse.json(
        { error: 'Failed to process template' },
        { status: 500 }
      );
    }
    
    // Save the processed template to the output directory
    const outputDir = await saveProcessedTemplate(
      portfolioData.id,
      processedHtml,
      template
    );
    
    // Update the portfolio data with the deployment URL
    portfolioData.deploymentUrl = `/portfolios/${portfolioData.id}`;
    
    // Return the generated portfolio data
    return NextResponse.json({
      success: true,
      portfolioId: portfolioData.id,
      portfolioUrl: `/portfolio/${portfolioData.id}`,
      deploymentUrl: portfolioData.deploymentUrl,
      message: 'Portfolio generated successfully!'
    });
    
  } catch (error) {
    console.error('Error generating portfolio:', error);
    return NextResponse.json(
      { error: 'Failed to generate portfolio' },
      { status: 500 }
    );
  }
} 