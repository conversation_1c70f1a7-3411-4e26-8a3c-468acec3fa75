"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/portfolio/[id]/page",{

/***/ "(app-pages-browser)/./src/components/portfolio/dynamic-portfolio.tsx":
/*!********************************************************!*\
  !*** ./src/components/portfolio/dynamic-portfolio.tsx ***!
  \********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DynamicPortfolio: function() { return /* binding */ DynamicPortfolio; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ExternalLink_Github_Linkedin_Mail_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ExternalLink,Github,Linkedin,Mail,Phone!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ExternalLink_Github_Linkedin_Mail_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ExternalLink,Github,Linkedin,Mail,Phone!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/linkedin.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ExternalLink_Github_Linkedin_Mail_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ExternalLink,Github,Linkedin,Mail,Phone!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/github.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ExternalLink_Github_Linkedin_Mail_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ExternalLink,Github,Linkedin,Mail,Phone!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ExternalLink_Github_Linkedin_Mail_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ExternalLink,Github,Linkedin,Mail,Phone!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ExternalLink_Github_Linkedin_Mail_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ExternalLink,Github,Linkedin,Mail,Phone!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* __next_internal_client_entry_do_not_use__ DynamicPortfolio auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction DynamicPortfolio(param) {\n    let { portfolioId } = param;\n    var _content_social, _content_social1;\n    _s();\n    const [portfolio, setPortfolio] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchPortfolio = async ()=>{\n            try {\n                console.log(\"Fetching portfolio data for ID: \".concat(portfolioId));\n                const response = await fetch(\"/api/portfolio/\".concat(portfolioId, \"/data\"));\n                const data = await response.json();\n                if (!response.ok) {\n                    throw new Error(data.error || \"Failed to fetch portfolio\");\n                }\n                console.log(\"Portfolio data received:\", data.portfolio);\n                setPortfolio(data.portfolio);\n            } catch (err) {\n                console.error(\"Error fetching portfolio:\", err);\n                setError(err instanceof Error ? err.message : \"Failed to load portfolio\");\n            } finally{\n                setLoading(false);\n            }\n        };\n        if (portfolioId) {\n            fetchPortfolio();\n        }\n    }, [\n        portfolioId\n    ]);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\portfolio\\\\dynamic-portfolio.tsx\",\n                        lineNumber: 89,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"Loading portfolio...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\portfolio\\\\dynamic-portfolio.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\portfolio\\\\dynamic-portfolio.tsx\",\n                lineNumber: 88,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\portfolio\\\\dynamic-portfolio.tsx\",\n            lineNumber: 87,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"max-w-md\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"p-6 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-bold text-red-600 mb-2\",\n                            children: \"Error\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\portfolio\\\\dynamic-portfolio.tsx\",\n                            lineNumber: 101,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-4\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\portfolio\\\\dynamic-portfolio.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            onClick: ()=>window.location.reload(),\n                            children: \"Try Again\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\portfolio\\\\dynamic-portfolio.tsx\",\n                            lineNumber: 103,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\portfolio\\\\dynamic-portfolio.tsx\",\n                    lineNumber: 100,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\portfolio\\\\dynamic-portfolio.tsx\",\n                lineNumber: 99,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\portfolio\\\\dynamic-portfolio.tsx\",\n            lineNumber: 98,\n            columnNumber: 7\n        }, this);\n    }\n    if (!portfolio) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                children: \"Portfolio not found\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\portfolio\\\\dynamic-portfolio.tsx\",\n                lineNumber: 115,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\portfolio\\\\dynamic-portfolio.tsx\",\n            lineNumber: 114,\n            columnNumber: 7\n        }, this);\n    }\n    const { content, colors } = portfolio;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        style: {\n            \"--primary-color\": colors.primary,\n            \"--secondary-color\": colors.secondary,\n            \"--accent-color\": colors.accent\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"bg-white py-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-5xl font-bold mb-4\",\n                            style: {\n                                color: colors.primary\n                            },\n                            children: content.name || \"Your Name\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\portfolio\\\\dynamic-portfolio.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl mb-6\",\n                            style: {\n                                color: colors.secondary\n                            },\n                            children: content.title || \"Your Professional Title\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\portfolio\\\\dynamic-portfolio.tsx\",\n                            lineNumber: 140,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-gray-600 max-w-2xl mx-auto mb-8\",\n                            children: content.bio || \"Your professional bio will appear here...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\portfolio\\\\dynamic-portfolio.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center space-x-4\",\n                            children: [\n                                content.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    asChild: true,\n                                    style: {\n                                        borderColor: colors.primary,\n                                        color: colors.primary\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"mailto:\".concat(content.email),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ExternalLink_Github_Linkedin_Mail_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\portfolio\\\\dynamic-portfolio.tsx\",\n                                                lineNumber: 159,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Email\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\portfolio\\\\dynamic-portfolio.tsx\",\n                                        lineNumber: 158,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\portfolio\\\\dynamic-portfolio.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 15\n                                }, this),\n                                ((_content_social = content.social) === null || _content_social === void 0 ? void 0 : _content_social.linkedin) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    asChild: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: content.social.linkedin,\n                                        target: \"_blank\",\n                                        rel: \"noopener noreferrer\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ExternalLink_Github_Linkedin_Mail_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\portfolio\\\\dynamic-portfolio.tsx\",\n                                                lineNumber: 167,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"LinkedIn\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\portfolio\\\\dynamic-portfolio.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\portfolio\\\\dynamic-portfolio.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 15\n                                }, this),\n                                ((_content_social1 = content.social) === null || _content_social1 === void 0 ? void 0 : _content_social1.github) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    asChild: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: content.social.github,\n                                        target: \"_blank\",\n                                        rel: \"noopener noreferrer\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ExternalLink_Github_Linkedin_Mail_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\portfolio\\\\dynamic-portfolio.tsx\",\n                                                lineNumber: 175,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"GitHub\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\portfolio\\\\dynamic-portfolio.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\portfolio\\\\dynamic-portfolio.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\portfolio\\\\dynamic-portfolio.tsx\",\n                            lineNumber: 151,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\portfolio\\\\dynamic-portfolio.tsx\",\n                    lineNumber: 133,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\portfolio\\\\dynamic-portfolio.tsx\",\n                lineNumber: 132,\n                columnNumber: 7\n            }, this),\n            content.skills && content.skills.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-3xl font-bold text-center mb-12\",\n                            style: {\n                                color: colors.primary\n                            },\n                            children: \"Skills\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\portfolio\\\\dynamic-portfolio.tsx\",\n                            lineNumber: 188,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap justify-center gap-3\",\n                            children: content.skills.map((skill, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-4 py-2 text-sm rounded-full border\",\n                                    style: {\n                                        backgroundColor: colors.accent + \"20\",\n                                        color: colors.primary,\n                                        borderColor: colors.accent\n                                    },\n                                    children: skill\n                                }, index, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\portfolio\\\\dynamic-portfolio.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\portfolio\\\\dynamic-portfolio.tsx\",\n                            lineNumber: 194,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\portfolio\\\\dynamic-portfolio.tsx\",\n                    lineNumber: 187,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\portfolio\\\\dynamic-portfolio.tsx\",\n                lineNumber: 186,\n                columnNumber: 9\n            }, this),\n            content.experience && content.experience.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-16 bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-3xl font-bold text-center mb-12\",\n                            style: {\n                                color: colors.primary\n                            },\n                            children: \"Experience\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\portfolio\\\\dynamic-portfolio.tsx\",\n                            lineNumber: 217,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-4xl mx-auto space-y-8\",\n                            children: content.experience.map((exp, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                        className: \"p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-start mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-xl font-semibold\",\n                                                                style: {\n                                                                    color: colors.primary\n                                                                },\n                                                                children: exp.title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\portfolio\\\\dynamic-portfolio.tsx\",\n                                                                lineNumber: 229,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-lg\",\n                                                                style: {\n                                                                    color: colors.secondary\n                                                                },\n                                                                children: exp.company\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\portfolio\\\\dynamic-portfolio.tsx\",\n                                                                lineNumber: 232,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\portfolio\\\\dynamic-portfolio.tsx\",\n                                                        lineNumber: 228,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ExternalLink_Github_Linkedin_Mail_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                className: \"w-4 h-4 inline mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\portfolio\\\\dynamic-portfolio.tsx\",\n                                                                lineNumber: 237,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            exp.startDate,\n                                                            \" - \",\n                                                            exp.endDate || \"Present\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\portfolio\\\\dynamic-portfolio.tsx\",\n                                                        lineNumber: 236,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\portfolio\\\\dynamic-portfolio.tsx\",\n                                                lineNumber: 227,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600\",\n                                                children: exp.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\portfolio\\\\dynamic-portfolio.tsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\portfolio\\\\dynamic-portfolio.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 19\n                                    }, this)\n                                }, index, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\portfolio\\\\dynamic-portfolio.tsx\",\n                                    lineNumber: 225,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\portfolio\\\\dynamic-portfolio.tsx\",\n                            lineNumber: 223,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\portfolio\\\\dynamic-portfolio.tsx\",\n                    lineNumber: 216,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\portfolio\\\\dynamic-portfolio.tsx\",\n                lineNumber: 215,\n                columnNumber: 9\n            }, this),\n            content.projects && content.projects.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-3xl font-bold text-center mb-12\",\n                            style: {\n                                color: colors.primary\n                            },\n                            children: \"Projects\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\portfolio\\\\dynamic-portfolio.tsx\",\n                            lineNumber: 254,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl mx-auto\",\n                            children: content.projects.map((project, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    className: \"hover:shadow-lg transition-shadow\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                        className: \"p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-xl font-semibold mb-3\",\n                                                style: {\n                                                    color: colors.primary\n                                                },\n                                                children: project.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\portfolio\\\\dynamic-portfolio.tsx\",\n                                                lineNumber: 264,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 mb-4\",\n                                                children: project.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\portfolio\\\\dynamic-portfolio.tsx\",\n                                                lineNumber: 267,\n                                                columnNumber: 21\n                                            }, this),\n                                            project.link && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                asChild: true,\n                                                style: {\n                                                    borderColor: colors.accent,\n                                                    color: colors.accent\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: project.link,\n                                                    target: \"_blank\",\n                                                    rel: \"noopener noreferrer\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ExternalLink_Github_Linkedin_Mail_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"w-4 h-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\portfolio\\\\dynamic-portfolio.tsx\",\n                                                            lineNumber: 276,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        \"View Project\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\portfolio\\\\dynamic-portfolio.tsx\",\n                                                    lineNumber: 275,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\portfolio\\\\dynamic-portfolio.tsx\",\n                                                lineNumber: 269,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\portfolio\\\\dynamic-portfolio.tsx\",\n                                        lineNumber: 263,\n                                        columnNumber: 19\n                                    }, this)\n                                }, index, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\portfolio\\\\dynamic-portfolio.tsx\",\n                                    lineNumber: 262,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\portfolio\\\\dynamic-portfolio.tsx\",\n                            lineNumber: 260,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\portfolio\\\\dynamic-portfolio.tsx\",\n                    lineNumber: 253,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\portfolio\\\\dynamic-portfolio.tsx\",\n                lineNumber: 252,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-16 bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-3xl font-bold mb-8\",\n                            style: {\n                                color: colors.primary\n                            },\n                            children: \"Get In Touch\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\portfolio\\\\dynamic-portfolio.tsx\",\n                            lineNumber: 292,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center space-x-6\",\n                            children: [\n                                content.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"mailto:\".concat(content.email),\n                                    className: \"flex items-center space-x-2 text-gray-600 hover:text-blue-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ExternalLink_Github_Linkedin_Mail_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\portfolio\\\\dynamic-portfolio.tsx\",\n                                            lineNumber: 304,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: content.email\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\portfolio\\\\dynamic-portfolio.tsx\",\n                                            lineNumber: 305,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\portfolio\\\\dynamic-portfolio.tsx\",\n                                    lineNumber: 300,\n                                    columnNumber: 15\n                                }, this),\n                                content.phone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"tel:\".concat(content.phone),\n                                    className: \"flex items-center space-x-2 text-gray-600 hover:text-blue-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ExternalLink_Github_Linkedin_Mail_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\portfolio\\\\dynamic-portfolio.tsx\",\n                                            lineNumber: 313,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: content.phone\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\portfolio\\\\dynamic-portfolio.tsx\",\n                                            lineNumber: 314,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\portfolio\\\\dynamic-portfolio.tsx\",\n                                    lineNumber: 309,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\portfolio\\\\dynamic-portfolio.tsx\",\n                            lineNumber: 298,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\portfolio\\\\dynamic-portfolio.tsx\",\n                    lineNumber: 291,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\portfolio\\\\dynamic-portfolio.tsx\",\n                lineNumber: 290,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\portfolio\\\\dynamic-portfolio.tsx\",\n        lineNumber: 123,\n        columnNumber: 5\n    }, this);\n}\n_s(DynamicPortfolio, \"wIEPePCbwKw2vZ03aSgo6mr8LA4=\");\n_c = DynamicPortfolio;\nvar _c;\n$RefreshReg$(_c, \"DynamicPortfolio\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/portfolio/dynamic-portfolio.tsx\n"));

/***/ })

});