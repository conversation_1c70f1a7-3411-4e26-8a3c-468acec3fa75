"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/onboarding/page",{

/***/ "(app-pages-browser)/./src/components/onboarding/content-form.tsx":
/*!****************************************************!*\
  !*** ./src/components/onboarding/content-form.tsx ***!
  \****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ContentForm: function() { return /* binding */ ContentForm; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/../node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! zod */ \"(app-pages-browser)/../node_modules/zod/lib/index.mjs\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_form__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/form */ \"(app-pages-browser)/./src/components/ui/form.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* __next_internal_client_entry_do_not_use__ ContentForm auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst contentSchema = zod__WEBPACK_IMPORTED_MODULE_9__.object({\n    name: zod__WEBPACK_IMPORTED_MODULE_9__.string().min(2, \"Name must be at least 2 characters\"),\n    title: zod__WEBPACK_IMPORTED_MODULE_9__.string().min(2, \"Title must be at least 2 characters\"),\n    bio: zod__WEBPACK_IMPORTED_MODULE_9__.string().min(10, \"Bio must be at least 10 characters\"),\n    skills: zod__WEBPACK_IMPORTED_MODULE_9__.array(zod__WEBPACK_IMPORTED_MODULE_9__.string()).min(1, \"Add at least one skill\"),\n    experience: zod__WEBPACK_IMPORTED_MODULE_9__.array(zod__WEBPACK_IMPORTED_MODULE_9__.object({\n        title: zod__WEBPACK_IMPORTED_MODULE_9__.string().min(2, \"Title must be at least 2 characters\"),\n        company: zod__WEBPACK_IMPORTED_MODULE_9__.string().min(2, \"Company must be at least 2 characters\"),\n        startDate: zod__WEBPACK_IMPORTED_MODULE_9__.string().min(2, \"Start date is required\"),\n        endDate: zod__WEBPACK_IMPORTED_MODULE_9__.string().optional(),\n        description: zod__WEBPACK_IMPORTED_MODULE_9__.string().min(10, \"Description must be at least 10 characters\")\n    })).min(0),\n    projects: zod__WEBPACK_IMPORTED_MODULE_9__.array(zod__WEBPACK_IMPORTED_MODULE_9__.object({\n        title: zod__WEBPACK_IMPORTED_MODULE_9__.string().min(2, \"Title must be at least 2 characters\"),\n        description: zod__WEBPACK_IMPORTED_MODULE_9__.string().min(10, \"Description must be at least 10 characters\"),\n        link: zod__WEBPACK_IMPORTED_MODULE_9__.string().url(\"Must be a valid URL\").optional()\n    })).min(0),\n    email: zod__WEBPACK_IMPORTED_MODULE_9__.string().email(\"Must be a valid email\").optional(),\n    phone: zod__WEBPACK_IMPORTED_MODULE_9__.string().optional(),\n    social: zod__WEBPACK_IMPORTED_MODULE_9__.object({\n        linkedin: zod__WEBPACK_IMPORTED_MODULE_9__.string().url(\"Must be a valid URL\").optional(),\n        github: zod__WEBPACK_IMPORTED_MODULE_9__.string().url(\"Must be a valid URL\").optional(),\n        twitter: zod__WEBPACK_IMPORTED_MODULE_9__.string().url(\"Must be a valid URL\").optional()\n    })\n});\nfunction ContentForm(param) {\n    let { defaultValues, onSubmit } = param;\n    _s();\n    const form = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_10__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__.zodResolver)(contentSchema),\n        defaultValues: defaultValues || {\n            name: \"\",\n            title: \"\",\n            bio: \"\",\n            skills: [],\n            experience: [],\n            projects: [],\n            email: \"\",\n            phone: \"\",\n            social: {\n                linkedin: \"\",\n                github: \"\",\n                twitter: \"\"\n            }\n        }\n    });\n    const handleFormSubmit = (values)=>{\n        console.log(\"=== CONTENT FORM SUBMIT ===\");\n        console.log(\"Form values:\", values);\n        console.log(\"Calling onSubmit prop...\");\n        onSubmit(values);\n    };\n    const { fields: experienceFields, append: appendExperience, remove: removeExperience } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_10__.useFieldArray)({\n        control: form.control,\n        name: \"experience\"\n    });\n    const { fields: projectFields, append: appendProject, remove: removeProject } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_10__.useFieldArray)({\n        control: form.control,\n        name: \"projects\"\n    });\n    const [newSkill, setNewSkill] = react__WEBPACK_IMPORTED_MODULE_1__.useState(\"\");\n    const addSkill = ()=>{\n        if (newSkill.trim()) {\n            const currentSkills = form.getValues(\"skills\");\n            form.setValue(\"skills\", [\n                ...currentSkills,\n                newSkill.trim()\n            ]);\n            setNewSkill(\"\");\n        }\n    };\n    const removeSkill = (index)=>{\n        const currentSkills = form.getValues(\"skills\");\n        form.setValue(\"skills\", currentSkills.filter((_, i)=>i !== index));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.Form, {\n        ...form,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n            onSubmit: form.handleSubmit(handleFormSubmit),\n            className: \"space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                            control: form.control,\n                            name: \"name\",\n                            render: (param)=>{\n                                let { field } = param;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                            children: \"Full Name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                placeholder: \"John Doe\",\n                                                ...field\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 19\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                            lineNumber: 125,\n                                            columnNumber: 17\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 15\n                                }, void 0);\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                            control: form.control,\n                            name: \"title\",\n                            render: (param)=>{\n                                let { field } = param;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                            children: \"Professional Title\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                            lineNumber: 135,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                placeholder: \"Senior Software Engineer\",\n                                                ...field\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                lineNumber: 137,\n                                                columnNumber: 19\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 17\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 15\n                                }, void 0);\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                            lineNumber: 130,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                            control: form.control,\n                            name: \"bio\",\n                            render: (param)=>{\n                                let { field } = param;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                            children: \"Bio\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                                placeholder: \"Tell us about yourself...\",\n                                                ...field\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                lineNumber: 151,\n                                                columnNumber: 19\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 17\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 15\n                                }, void 0);\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                            lineNumber: 144,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                    children: \"Skills\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                            value: newSkill,\n                                            onChange: (e)=>setNewSkill(e.target.value),\n                                            placeholder: \"Add a skill\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                            lineNumber: 164,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            type: \"button\",\n                                            onClick: addSkill,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                lineNumber: 170,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-2\",\n                                    children: form.watch(\"skills\").map((skill, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-1 bg-secondary text-secondary-foreground px-2 py-1 rounded-md\",\n                                            children: [\n                                                skill,\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    type: \"button\",\n                                                    variant: \"ghost\",\n                                                    size: \"icon\",\n                                                    className: \"h-4 w-4 p-0 hover:bg-transparent\",\n                                                    onClick: ()=>removeSkill(index),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-3 w-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                        lineNumber: 187,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                    lineNumber: 180,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                            lineNumber: 175,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                            children: \"Experience\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                            lineNumber: 196,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            type: \"button\",\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            onClick: ()=>appendExperience({\n                                                    title: \"\",\n                                                    company: \"\",\n                                                    startDate: \"\",\n                                                    endDate: \"\",\n                                                    description: \"\"\n                                                }),\n                                            children: \"Add Experience\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: experienceFields.map((field, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                                className: \"pt-6 space-y-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1 grid gap-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                                                                    control: form.control,\n                                                                    name: \"experience.\".concat(index, \".title\"),\n                                                                    render: (param)=>{\n                                                                        let { field } = param;\n                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                                                    children: \"Title\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 225,\n                                                                                    columnNumber: 31\n                                                                                }, void 0),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                        ...field\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                        lineNumber: 227,\n                                                                                        columnNumber: 33\n                                                                                    }, void 0)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 226,\n                                                                                    columnNumber: 31\n                                                                                }, void 0),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 229,\n                                                                                    columnNumber: 31\n                                                                                }, void 0)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                            lineNumber: 224,\n                                                                            columnNumber: 29\n                                                                        }, void 0);\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                    lineNumber: 220,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                                                                    control: form.control,\n                                                                    name: \"experience.\".concat(index, \".company\"),\n                                                                    render: (param)=>{\n                                                                        let { field } = param;\n                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                                                    children: \"Company\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 238,\n                                                                                    columnNumber: 31\n                                                                                }, void 0),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                        ...field\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                        lineNumber: 240,\n                                                                                        columnNumber: 33\n                                                                                    }, void 0)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 239,\n                                                                                    columnNumber: 31\n                                                                                }, void 0),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 242,\n                                                                                    columnNumber: 31\n                                                                                }, void 0)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                            lineNumber: 237,\n                                                                            columnNumber: 29\n                                                                        }, void 0);\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                    lineNumber: 233,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                                                                    control: form.control,\n                                                                    name: \"experience.\".concat(index, \".startDate\"),\n                                                                    render: (param)=>{\n                                                                        let { field } = param;\n                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                                                    children: \"Start Date\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 251,\n                                                                                    columnNumber: 31\n                                                                                }, void 0),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                        ...field,\n                                                                                        placeholder: \"e.g., January 2020\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                        lineNumber: 253,\n                                                                                        columnNumber: 33\n                                                                                    }, void 0)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 252,\n                                                                                    columnNumber: 31\n                                                                                }, void 0),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 255,\n                                                                                    columnNumber: 31\n                                                                                }, void 0)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                            lineNumber: 250,\n                                                                            columnNumber: 29\n                                                                        }, void 0);\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                    lineNumber: 246,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                                                                    control: form.control,\n                                                                    name: \"experience.\".concat(index, \".endDate\"),\n                                                                    render: (param)=>{\n                                                                        let { field } = param;\n                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                                                    children: \"End Date (Optional)\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 264,\n                                                                                    columnNumber: 31\n                                                                                }, void 0),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                        ...field,\n                                                                                        value: field.value || \"\",\n                                                                                        placeholder: \"e.g., Present or December 2023\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                        lineNumber: 266,\n                                                                                        columnNumber: 33\n                                                                                    }, void 0)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 265,\n                                                                                    columnNumber: 31\n                                                                                }, void 0),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 268,\n                                                                                    columnNumber: 31\n                                                                                }, void 0)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                            lineNumber: 263,\n                                                                            columnNumber: 29\n                                                                        }, void 0);\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                    lineNumber: 259,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                                                                    control: form.control,\n                                                                    name: \"experience.\".concat(index, \".description\"),\n                                                                    render: (param)=>{\n                                                                        let { field } = param;\n                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                                                    children: \"Description\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 277,\n                                                                                    columnNumber: 31\n                                                                                }, void 0),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                                                                        ...field\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                        lineNumber: 279,\n                                                                                        columnNumber: 33\n                                                                                    }, void 0)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 278,\n                                                                                    columnNumber: 31\n                                                                                }, void 0),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 281,\n                                                                                    columnNumber: 31\n                                                                                }, void 0)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                            lineNumber: 276,\n                                                                            columnNumber: 29\n                                                                        }, void 0);\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                    lineNumber: 272,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                            lineNumber: 219,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            type: \"button\",\n                                                            variant: \"ghost\",\n                                                            size: \"icon\",\n                                                            onClick: ()=>removeExperience(index),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                lineNumber: 292,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                            lineNumber: 286,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                    lineNumber: 218,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                lineNumber: 217,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, field.id, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                            lineNumber: 216,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                    lineNumber: 214,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                            lineNumber: 194,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                            children: \"Projects\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                            lineNumber: 303,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            type: \"button\",\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            onClick: ()=>appendProject({\n                                                    title: \"\",\n                                                    description: \"\",\n                                                    link: \"\"\n                                                }),\n                                            children: \"Add Project\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                            lineNumber: 304,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                    lineNumber: 302,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: projectFields.map((field, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                                className: \"pt-6 space-y-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1 grid gap-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                                                                    control: form.control,\n                                                                    name: \"projects.\".concat(index, \".title\"),\n                                                                    render: (param)=>{\n                                                                        let { field } = param;\n                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                                                    children: \"Title\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 330,\n                                                                                    columnNumber: 31\n                                                                                }, void 0),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                        ...field\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                        lineNumber: 332,\n                                                                                        columnNumber: 33\n                                                                                    }, void 0)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 331,\n                                                                                    columnNumber: 31\n                                                                                }, void 0),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 334,\n                                                                                    columnNumber: 31\n                                                                                }, void 0)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                            lineNumber: 329,\n                                                                            columnNumber: 29\n                                                                        }, void 0);\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                    lineNumber: 325,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                                                                    control: form.control,\n                                                                    name: \"projects.\".concat(index, \".description\"),\n                                                                    render: (param)=>{\n                                                                        let { field } = param;\n                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                                                    children: \"Description\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 343,\n                                                                                    columnNumber: 31\n                                                                                }, void 0),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                                                                        ...field\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                        lineNumber: 345,\n                                                                                        columnNumber: 33\n                                                                                    }, void 0)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 344,\n                                                                                    columnNumber: 31\n                                                                                }, void 0),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 347,\n                                                                                    columnNumber: 31\n                                                                                }, void 0)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                            lineNumber: 342,\n                                                                            columnNumber: 29\n                                                                        }, void 0);\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                    lineNumber: 338,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                                                                    control: form.control,\n                                                                    name: \"projects.\".concat(index, \".link\"),\n                                                                    render: (param)=>{\n                                                                        let { field } = param;\n                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                                                    children: \"Link\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 356,\n                                                                                    columnNumber: 31\n                                                                                }, void 0),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                        type: \"url\",\n                                                                                        ...field,\n                                                                                        value: field.value || \"\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                        lineNumber: 358,\n                                                                                        columnNumber: 33\n                                                                                    }, void 0)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 357,\n                                                                                    columnNumber: 31\n                                                                                }, void 0),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 360,\n                                                                                    columnNumber: 31\n                                                                                }, void 0)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                            lineNumber: 355,\n                                                                            columnNumber: 29\n                                                                        }, void 0);\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                    lineNumber: 351,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                            lineNumber: 324,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            type: \"button\",\n                                                            variant: \"ghost\",\n                                                            size: \"icon\",\n                                                            onClick: ()=>removeProject(index),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                lineNumber: 371,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                            lineNumber: 365,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                    lineNumber: 323,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                lineNumber: 322,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, field.id, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                            lineNumber: 321,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                    lineNumber: 319,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                            lineNumber: 301,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                    children: \"Contact Information\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                    lineNumber: 381,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                                            control: form.control,\n                                            name: \"email\",\n                                            render: (param)=>{\n                                                let { field } = param;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                            children: \"Email\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                            lineNumber: 388,\n                                                            columnNumber: 21\n                                                        }, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                type: \"email\",\n                                                                ...field,\n                                                                value: field.value || \"\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                lineNumber: 390,\n                                                                columnNumber: 23\n                                                            }, void 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                            lineNumber: 389,\n                                                            columnNumber: 21\n                                                        }, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                            lineNumber: 392,\n                                                            columnNumber: 21\n                                                        }, void 0)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                    lineNumber: 387,\n                                                    columnNumber: 19\n                                                }, void 0);\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                            lineNumber: 383,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                                            control: form.control,\n                                            name: \"phone\",\n                                            render: (param)=>{\n                                                let { field } = param;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                            children: \"Phone (Optional)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                            lineNumber: 401,\n                                                            columnNumber: 21\n                                                        }, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                type: \"tel\",\n                                                                ...field,\n                                                                value: field.value || \"\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                lineNumber: 403,\n                                                                columnNumber: 23\n                                                            }, void 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                            lineNumber: 402,\n                                                            columnNumber: 21\n                                                        }, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                            lineNumber: 405,\n                                                            columnNumber: 21\n                                                        }, void 0)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                    lineNumber: 400,\n                                                    columnNumber: 19\n                                                }, void 0);\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                            lineNumber: 396,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                    lineNumber: 382,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                            lineNumber: 380,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                    children: \"Social Links\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                    lineNumber: 413,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                                            control: form.control,\n                                            name: \"social.linkedin\",\n                                            render: (param)=>{\n                                                let { field } = param;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                            children: \"LinkedIn\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                            lineNumber: 420,\n                                                            columnNumber: 21\n                                                        }, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                type: \"url\",\n                                                                ...field,\n                                                                value: field.value || \"\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                lineNumber: 422,\n                                                                columnNumber: 23\n                                                            }, void 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                            lineNumber: 421,\n                                                            columnNumber: 21\n                                                        }, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                            lineNumber: 424,\n                                                            columnNumber: 21\n                                                        }, void 0)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                    lineNumber: 419,\n                                                    columnNumber: 19\n                                                }, void 0);\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                            lineNumber: 415,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                                            control: form.control,\n                                            name: \"social.github\",\n                                            render: (param)=>{\n                                                let { field } = param;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                            children: \"GitHub\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                            lineNumber: 433,\n                                                            columnNumber: 21\n                                                        }, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                type: \"url\",\n                                                                ...field,\n                                                                value: field.value || \"\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                lineNumber: 435,\n                                                                columnNumber: 23\n                                                            }, void 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                            lineNumber: 434,\n                                                            columnNumber: 21\n                                                        }, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                            lineNumber: 437,\n                                                            columnNumber: 21\n                                                        }, void 0)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                    lineNumber: 432,\n                                                    columnNumber: 19\n                                                }, void 0);\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                            lineNumber: 428,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                                            control: form.control,\n                                            name: \"social.twitter\",\n                                            render: (param)=>{\n                                                let { field } = param;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                            children: \"Twitter\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                            lineNumber: 446,\n                                                            columnNumber: 21\n                                                        }, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                type: \"url\",\n                                                                ...field,\n                                                                value: field.value || \"\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                lineNumber: 448,\n                                                                columnNumber: 23\n                                                            }, void 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                            lineNumber: 447,\n                                                            columnNumber: 21\n                                                        }, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                            lineNumber: 450,\n                                                            columnNumber: 21\n                                                        }, void 0)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                    lineNumber: 445,\n                                                    columnNumber: 19\n                                                }, void 0);\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                            lineNumber: 441,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                    lineNumber: 414,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                            lineNumber: 412,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                    lineNumber: 115,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-end\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        type: \"submit\",\n                        className: \"bg-green-600 hover:bg-green-700\",\n                        children: \"\\uD83D\\uDE80 Create Portfolio\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                        lineNumber: 459,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                    lineNumber: 458,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n            lineNumber: 114,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n        lineNumber: 113,\n        columnNumber: 5\n    }, this);\n}\n_s(ContentForm, \"CUNbDDiudl1iQFH+kZW4keFRSFc=\", false, function() {\n    return [\n        react_hook_form__WEBPACK_IMPORTED_MODULE_10__.useForm,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_10__.useFieldArray,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_10__.useFieldArray\n    ];\n});\n_c = ContentForm;\nvar _c;\n$RefreshReg$(_c, \"ContentForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/onboarding/content-form.tsx\n"));

/***/ })

});