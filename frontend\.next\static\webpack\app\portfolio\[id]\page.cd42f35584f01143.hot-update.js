"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/portfolio/[id]/page",{

/***/ "(app-pages-browser)/./src/app/portfolio/[id]/page.tsx":
/*!*****************************************!*\
  !*** ./src/app/portfolio/[id]/page.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ PortfolioPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/../node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction PortfolioPage() {\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)() || {};\n    const portfolioId = typeof params.id === \"string\" ? params.id : \"\";\n    const [htmlContent, setHtmlContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadPortfolio = async ()=>{\n            if (!portfolioId) {\n                setError(\"Invalid portfolio ID\");\n                setLoading(false);\n                return;\n            }\n            try {\n                setLoading(true);\n                console.log(\"Loading portfolio HTML for ID: \".concat(portfolioId));\n                // Try to load the generated HTML file directly\n                const htmlResponse = await fetch(\"/api/portfolio/\".concat(portfolioId, \"/html\"));\n                if (htmlResponse.ok) {\n                    const html = await htmlResponse.text();\n                    console.log(\"HTML loaded successfully, length: \".concat(html.length));\n                    setHtmlContent(html);\n                } else {\n                    console.error(\"Failed to load HTML: \".concat(htmlResponse.status));\n                    setError(\"Portfolio not found\");\n                }\n            } catch (err) {\n                console.error(\"Error loading portfolio:\", err);\n                setError(\"Failed to load portfolio\");\n            } finally{\n                setLoading(false);\n            }\n        };\n        loadPortfolio();\n    }, [\n        portfolioId\n    ]);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\portfolio\\\\[id]\\\\page.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"Loading portfolio...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\portfolio\\\\[id]\\\\page.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\portfolio\\\\[id]\\\\page.tsx\",\n                lineNumber: 52,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\portfolio\\\\[id]\\\\page.tsx\",\n            lineNumber: 51,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto py-12 px-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                    className: \"py-12 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-red-500 mb-4\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\portfolio\\\\[id]\\\\page.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mb-6\",\n                            children: \"The portfolio you're looking for could not be loaded.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\portfolio\\\\[id]\\\\page.tsx\",\n                            lineNumber: 68,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            onClick: ()=>window.history.back(),\n                            children: \"Go Back\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\portfolio\\\\[id]\\\\page.tsx\",\n                            lineNumber: 71,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\portfolio\\\\[id]\\\\page.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\portfolio\\\\[id]\\\\page.tsx\",\n                lineNumber: 63,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\portfolio\\\\[id]\\\\page.tsx\",\n            lineNumber: 62,\n            columnNumber: 7\n        }, this);\n    }\n    if (htmlContent) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"portfolio-viewer\",\n            dangerouslySetInnerHTML: {\n                __html: htmlContent\n            },\n            style: {\n                width: \"100%\",\n                minHeight: \"100vh\"\n            }\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\portfolio\\\\[id]\\\\page.tsx\",\n            lineNumber: 82,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n            children: \"No content available\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\portfolio\\\\[id]\\\\page.tsx\",\n            lineNumber: 92,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\app\\\\portfolio\\\\[id]\\\\page.tsx\",\n        lineNumber: 91,\n        columnNumber: 5\n    }, this);\n}\n_s(PortfolioPage, \"DhHZTo7/fSTWSKhuztHc51bToe0=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams\n    ];\n});\n_c = PortfolioPage;\nvar _c;\n$RefreshReg$(_c, \"PortfolioPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/portfolio/[id]/page.tsx\n"));

/***/ })

});