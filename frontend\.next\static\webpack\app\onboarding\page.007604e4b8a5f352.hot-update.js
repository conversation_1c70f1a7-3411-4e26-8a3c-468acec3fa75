"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/onboarding/page",{

/***/ "(app-pages-browser)/./src/components/onboarding/content-form.tsx":
/*!****************************************************!*\
  !*** ./src/components/onboarding/content-form.tsx ***!
  \****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ContentForm: function() { return /* binding */ ContentForm; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/../node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! zod */ \"(app-pages-browser)/../node_modules/zod/lib/index.mjs\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_form__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/form */ \"(app-pages-browser)/./src/components/ui/form.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* __next_internal_client_entry_do_not_use__ ContentForm auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nconst contentSchema = zod__WEBPACK_IMPORTED_MODULE_9__.object({\n    name: zod__WEBPACK_IMPORTED_MODULE_9__.string().min(2, \"Name must be at least 2 characters\"),\n    title: zod__WEBPACK_IMPORTED_MODULE_9__.string().min(2, \"Title must be at least 2 characters\"),\n    bio: zod__WEBPACK_IMPORTED_MODULE_9__.string().min(10, \"Bio must be at least 10 characters\"),\n    skills: zod__WEBPACK_IMPORTED_MODULE_9__.array(zod__WEBPACK_IMPORTED_MODULE_9__.string()).min(1, \"Add at least one skill\"),\n    experience: zod__WEBPACK_IMPORTED_MODULE_9__.array(zod__WEBPACK_IMPORTED_MODULE_9__.object({\n        title: zod__WEBPACK_IMPORTED_MODULE_9__.string().min(2, \"Title must be at least 2 characters\"),\n        company: zod__WEBPACK_IMPORTED_MODULE_9__.string().min(2, \"Company must be at least 2 characters\"),\n        startDate: zod__WEBPACK_IMPORTED_MODULE_9__.string().min(2, \"Start date is required\"),\n        endDate: zod__WEBPACK_IMPORTED_MODULE_9__.string().optional(),\n        description: zod__WEBPACK_IMPORTED_MODULE_9__.string().min(10, \"Description must be at least 10 characters\")\n    })).min(0),\n    projects: zod__WEBPACK_IMPORTED_MODULE_9__.array(zod__WEBPACK_IMPORTED_MODULE_9__.object({\n        title: zod__WEBPACK_IMPORTED_MODULE_9__.string().min(2, \"Title must be at least 2 characters\"),\n        description: zod__WEBPACK_IMPORTED_MODULE_9__.string().min(10, \"Description must be at least 10 characters\"),\n        link: zod__WEBPACK_IMPORTED_MODULE_9__.string().url(\"Must be a valid URL\").optional()\n    })).min(0),\n    email: zod__WEBPACK_IMPORTED_MODULE_9__.string().email(\"Must be a valid email\").optional(),\n    phone: zod__WEBPACK_IMPORTED_MODULE_9__.string().optional(),\n    social: zod__WEBPACK_IMPORTED_MODULE_9__.object({\n        linkedin: zod__WEBPACK_IMPORTED_MODULE_9__.string().url(\"Must be a valid URL\").optional(),\n        github: zod__WEBPACK_IMPORTED_MODULE_9__.string().url(\"Must be a valid URL\").optional(),\n        twitter: zod__WEBPACK_IMPORTED_MODULE_9__.string().url(\"Must be a valid URL\").optional()\n    })\n});\nfunction ContentForm(param) {\n    let { defaultValues, onSubmit } = param;\n    var _defaultValues_social, _defaultValues_social1, _defaultValues_social2;\n    _s();\n    // Simple state-based approach instead of complex form validation\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: (defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.name) || \"\",\n        title: (defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.title) || \"\",\n        bio: (defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.bio) || \"\",\n        skills: (defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.skills) || [],\n        experience: (defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.experience) || [],\n        projects: (defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.projects) || [],\n        email: (defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.email) || \"\",\n        phone: (defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.phone) || \"\",\n        social: {\n            linkedin: (defaultValues === null || defaultValues === void 0 ? void 0 : (_defaultValues_social = defaultValues.social) === null || _defaultValues_social === void 0 ? void 0 : _defaultValues_social.linkedin) || \"\",\n            github: (defaultValues === null || defaultValues === void 0 ? void 0 : (_defaultValues_social1 = defaultValues.social) === null || _defaultValues_social1 === void 0 ? void 0 : _defaultValues_social1.github) || \"\",\n            twitter: (defaultValues === null || defaultValues === void 0 ? void 0 : (_defaultValues_social2 = defaultValues.social) === null || _defaultValues_social2 === void 0 ? void 0 : _defaultValues_social2.twitter) || \"\"\n        }\n    });\n    const form = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_10__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__.zodResolver)(contentSchema),\n        defaultValues: formData\n    });\n    const handleFormSubmit = ()=>{\n        var _finalData_name, _finalData_title, _finalData_bio, _finalData_email;\n        console.log(\"=== FORM SUBMIT FUNCTION CALLED ===\");\n        // Get fresh data from form\n        const formValues = form.getValues();\n        console.log(\"Form values:\", formValues);\n        // Merge form values with state data\n        const finalData = {\n            ...formData,\n            ...formValues\n        };\n        console.log(\"Final merged data:\", finalData);\n        console.log(\"Name:\", finalData.name);\n        console.log(\"Title:\", finalData.title);\n        console.log(\"Bio:\", finalData.bio);\n        console.log(\"Skills:\", finalData.skills);\n        console.log(\"Projects:\", finalData.projects);\n        console.log(\"Experience:\", finalData.experience);\n        console.log(\"Email:\", finalData.email);\n        // Validate required fields\n        if (!((_finalData_name = finalData.name) === null || _finalData_name === void 0 ? void 0 : _finalData_name.trim())) {\n            alert(\"Please enter your name\");\n            return;\n        }\n        if (!((_finalData_title = finalData.title) === null || _finalData_title === void 0 ? void 0 : _finalData_title.trim())) {\n            alert(\"Please enter your professional title\");\n            return;\n        }\n        if (!((_finalData_bio = finalData.bio) === null || _finalData_bio === void 0 ? void 0 : _finalData_bio.trim())) {\n            alert(\"Please enter your bio\");\n            return;\n        }\n        if (!((_finalData_email = finalData.email) === null || _finalData_email === void 0 ? void 0 : _finalData_email.trim())) {\n            alert(\"Please enter your email\");\n            return;\n        }\n        // Submit with merged data\n        console.log(\"Submitting form with final data:\", finalData);\n        onSubmit(finalData);\n    };\n    // Update form data when input changes\n    const updateFormData = (field, value)=>{\n        console.log(\"Updating \".concat(field, \" with:\"), value);\n        const newFormData = {\n            ...formData,\n            [field]: value\n        };\n        setFormData(newFormData);\n        // Also update the React Hook Form\n        form.setValue(field, value);\n        console.log(\"Updated formData:\", newFormData);\n        console.log(\"Updated form value:\", form.getValues(field));\n    };\n    const { fields: experienceFields, append: appendExperience, remove: removeExperience } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_10__.useFieldArray)({\n        control: form.control,\n        name: \"experience\"\n    });\n    const { fields: projectFields, append: appendProject, remove: removeProject } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_10__.useFieldArray)({\n        control: form.control,\n        name: \"projects\"\n    });\n    const [newSkill, setNewSkill] = react__WEBPACK_IMPORTED_MODULE_1__.useState(\"\");\n    const addSkill = ()=>{\n        if (newSkill.trim()) {\n            const currentSkills = formData.skills || [];\n            const newSkills = [\n                ...currentSkills,\n                newSkill.trim()\n            ];\n            // Update both form and state\n            form.setValue(\"skills\", newSkills);\n            updateFormData(\"skills\", newSkills);\n            setNewSkill(\"\");\n            console.log(\"Added skill:\", newSkill.trim());\n            console.log(\"Updated skills:\", newSkills);\n        }\n    };\n    const removeSkill = (index)=>{\n        const currentSkills = formData.skills || [];\n        const newSkills = currentSkills.filter((_, i)=>i !== index);\n        // Update both form and state\n        form.setValue(\"skills\", newSkills);\n        updateFormData(\"skills\", newSkills);\n        console.log(\"Removed skill at index:\", index);\n        console.log(\"Updated skills:\", newSkills);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.Form, {\n        ...form,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n            className: \"space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                    htmlFor: \"name\",\n                                    children: \"Full Name\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                    id: \"name\",\n                                    value: formData.name,\n                                    onChange: (e)=>updateFormData(\"name\", e.target.value),\n                                    placeholder: \"John Doe\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                    lineNumber: 187,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                            lineNumber: 185,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                    htmlFor: \"title\",\n                                    children: \"Professional Title\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                    id: \"title\",\n                                    value: formData.title,\n                                    onChange: (e)=>updateFormData(\"title\", e.target.value),\n                                    placeholder: \"Senior Software Engineer\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                    lineNumber: 197,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                            lineNumber: 195,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                    htmlFor: \"bio\",\n                                    children: \"Bio\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                    id: \"bio\",\n                                    value: formData.bio,\n                                    onChange: (e)=>updateFormData(\"bio\", e.target.value),\n                                    placeholder: \"Tell us about yourself...\",\n                                    className: \"min-h-[100px]\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                            lineNumber: 205,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                    children: \"Skills\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                            value: newSkill,\n                                            onChange: (e)=>setNewSkill(e.target.value),\n                                            placeholder: \"Add a skill\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                            lineNumber: 219,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            type: \"button\",\n                                            onClick: addSkill,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                lineNumber: 225,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-2\",\n                                    children: (formData.skills || []).map((skill, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-1 bg-secondary text-secondary-foreground px-2 py-1 rounded-md\",\n                                            children: [\n                                                skill,\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    type: \"button\",\n                                                    variant: \"ghost\",\n                                                    size: \"icon\",\n                                                    className: \"h-4 w-4 p-0 hover:bg-transparent\",\n                                                    onClick: ()=>removeSkill(index),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-3 w-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                        lineNumber: 242,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                    lineNumber: 235,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                            lineNumber: 230,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                    lineNumber: 228,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                            lineNumber: 216,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                            children: \"Experience\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                            lineNumber: 251,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            type: \"button\",\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            onClick: ()=>appendExperience({\n                                                    title: \"\",\n                                                    company: \"\",\n                                                    startDate: \"\",\n                                                    endDate: \"\",\n                                                    description: \"\"\n                                                }),\n                                            children: \"Add Experience\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                            lineNumber: 252,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                    lineNumber: 250,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: experienceFields.map((field, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                                className: \"pt-6 space-y-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1 grid gap-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                                                                    control: form.control,\n                                                                    name: \"experience.\".concat(index, \".title\"),\n                                                                    render: (param)=>{\n                                                                        let { field } = param;\n                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                                                    children: \"Title\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 280,\n                                                                                    columnNumber: 31\n                                                                                }, void 0),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                        ...field\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                        lineNumber: 282,\n                                                                                        columnNumber: 33\n                                                                                    }, void 0)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 281,\n                                                                                    columnNumber: 31\n                                                                                }, void 0),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 284,\n                                                                                    columnNumber: 31\n                                                                                }, void 0)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                            lineNumber: 279,\n                                                                            columnNumber: 29\n                                                                        }, void 0);\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                    lineNumber: 275,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                                                                    control: form.control,\n                                                                    name: \"experience.\".concat(index, \".company\"),\n                                                                    render: (param)=>{\n                                                                        let { field } = param;\n                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                                                    children: \"Company\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 293,\n                                                                                    columnNumber: 31\n                                                                                }, void 0),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                        ...field\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                        lineNumber: 295,\n                                                                                        columnNumber: 33\n                                                                                    }, void 0)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 294,\n                                                                                    columnNumber: 31\n                                                                                }, void 0),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 297,\n                                                                                    columnNumber: 31\n                                                                                }, void 0)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                            lineNumber: 292,\n                                                                            columnNumber: 29\n                                                                        }, void 0);\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                    lineNumber: 288,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                                                                    control: form.control,\n                                                                    name: \"experience.\".concat(index, \".startDate\"),\n                                                                    render: (param)=>{\n                                                                        let { field } = param;\n                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                                                    children: \"Start Date\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 306,\n                                                                                    columnNumber: 31\n                                                                                }, void 0),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                        ...field,\n                                                                                        placeholder: \"e.g., January 2020\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                        lineNumber: 308,\n                                                                                        columnNumber: 33\n                                                                                    }, void 0)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 307,\n                                                                                    columnNumber: 31\n                                                                                }, void 0),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 310,\n                                                                                    columnNumber: 31\n                                                                                }, void 0)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                            lineNumber: 305,\n                                                                            columnNumber: 29\n                                                                        }, void 0);\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                    lineNumber: 301,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                                                                    control: form.control,\n                                                                    name: \"experience.\".concat(index, \".endDate\"),\n                                                                    render: (param)=>{\n                                                                        let { field } = param;\n                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                                                    children: \"End Date (Optional)\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 319,\n                                                                                    columnNumber: 31\n                                                                                }, void 0),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                        ...field,\n                                                                                        value: field.value || \"\",\n                                                                                        placeholder: \"e.g., Present or December 2023\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                        lineNumber: 321,\n                                                                                        columnNumber: 33\n                                                                                    }, void 0)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 320,\n                                                                                    columnNumber: 31\n                                                                                }, void 0),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 323,\n                                                                                    columnNumber: 31\n                                                                                }, void 0)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                            lineNumber: 318,\n                                                                            columnNumber: 29\n                                                                        }, void 0);\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                    lineNumber: 314,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                                                                    control: form.control,\n                                                                    name: \"experience.\".concat(index, \".description\"),\n                                                                    render: (param)=>{\n                                                                        let { field } = param;\n                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                                                    children: \"Description\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 332,\n                                                                                    columnNumber: 31\n                                                                                }, void 0),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                                                                        ...field\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                        lineNumber: 334,\n                                                                                        columnNumber: 33\n                                                                                    }, void 0)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 333,\n                                                                                    columnNumber: 31\n                                                                                }, void 0),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 336,\n                                                                                    columnNumber: 31\n                                                                                }, void 0)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                            lineNumber: 331,\n                                                                            columnNumber: 29\n                                                                        }, void 0);\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                    lineNumber: 327,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                            lineNumber: 274,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            type: \"button\",\n                                                            variant: \"ghost\",\n                                                            size: \"icon\",\n                                                            onClick: ()=>removeExperience(index),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                lineNumber: 347,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                            lineNumber: 341,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                    lineNumber: 273,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                lineNumber: 272,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, field.id, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                            lineNumber: 271,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                    lineNumber: 269,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                            lineNumber: 249,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                            children: \"Projects\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                            lineNumber: 358,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            type: \"button\",\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            onClick: ()=>appendProject({\n                                                    title: \"\",\n                                                    description: \"\",\n                                                    link: \"\"\n                                                }),\n                                            children: \"Add Project\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                            lineNumber: 359,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                    lineNumber: 357,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: projectFields.map((field, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                                className: \"pt-6 space-y-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1 grid gap-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                                                                    control: form.control,\n                                                                    name: \"projects.\".concat(index, \".title\"),\n                                                                    render: (param)=>{\n                                                                        let { field } = param;\n                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                                                    children: \"Title\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 385,\n                                                                                    columnNumber: 31\n                                                                                }, void 0),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                        ...field\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                        lineNumber: 387,\n                                                                                        columnNumber: 33\n                                                                                    }, void 0)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 386,\n                                                                                    columnNumber: 31\n                                                                                }, void 0),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 389,\n                                                                                    columnNumber: 31\n                                                                                }, void 0)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                            lineNumber: 384,\n                                                                            columnNumber: 29\n                                                                        }, void 0);\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                    lineNumber: 380,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                                                                    control: form.control,\n                                                                    name: \"projects.\".concat(index, \".description\"),\n                                                                    render: (param)=>{\n                                                                        let { field } = param;\n                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                                                    children: \"Description\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 398,\n                                                                                    columnNumber: 31\n                                                                                }, void 0),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                                                                        ...field\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                        lineNumber: 400,\n                                                                                        columnNumber: 33\n                                                                                    }, void 0)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 399,\n                                                                                    columnNumber: 31\n                                                                                }, void 0),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 402,\n                                                                                    columnNumber: 31\n                                                                                }, void 0)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                            lineNumber: 397,\n                                                                            columnNumber: 29\n                                                                        }, void 0);\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                    lineNumber: 393,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                                                                    control: form.control,\n                                                                    name: \"projects.\".concat(index, \".link\"),\n                                                                    render: (param)=>{\n                                                                        let { field } = param;\n                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                                                    children: \"Link\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 411,\n                                                                                    columnNumber: 31\n                                                                                }, void 0),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                        type: \"url\",\n                                                                                        ...field,\n                                                                                        value: field.value || \"\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                        lineNumber: 413,\n                                                                                        columnNumber: 33\n                                                                                    }, void 0)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 412,\n                                                                                    columnNumber: 31\n                                                                                }, void 0),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                                    lineNumber: 415,\n                                                                                    columnNumber: 31\n                                                                                }, void 0)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                            lineNumber: 410,\n                                                                            columnNumber: 29\n                                                                        }, void 0);\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                    lineNumber: 406,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                            lineNumber: 379,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            type: \"button\",\n                                                            variant: \"ghost\",\n                                                            size: \"icon\",\n                                                            onClick: ()=>removeProject(index),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                lineNumber: 426,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                            lineNumber: 420,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                    lineNumber: 378,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                lineNumber: 377,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, field.id, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                            lineNumber: 376,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                    lineNumber: 374,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                            lineNumber: 356,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                    children: \"Contact Information\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                    lineNumber: 436,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"email\",\n                                                    children: \"Email\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                    lineNumber: 439,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                    id: \"email\",\n                                                    type: \"email\",\n                                                    value: formData.email,\n                                                    onChange: (e)=>updateFormData(\"email\", e.target.value),\n                                                    placeholder: \"<EMAIL>\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                    lineNumber: 440,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                            lineNumber: 438,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                                            control: form.control,\n                                            name: \"phone\",\n                                            render: (param)=>{\n                                                let { field } = param;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                            children: \"Phone (Optional)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                            lineNumber: 453,\n                                                            columnNumber: 21\n                                                        }, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                type: \"tel\",\n                                                                ...field,\n                                                                value: field.value || \"\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                lineNumber: 455,\n                                                                columnNumber: 23\n                                                            }, void 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                            lineNumber: 454,\n                                                            columnNumber: 21\n                                                        }, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                            lineNumber: 457,\n                                                            columnNumber: 21\n                                                        }, void 0)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                    lineNumber: 452,\n                                                    columnNumber: 19\n                                                }, void 0);\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                            lineNumber: 448,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                    lineNumber: 437,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                            lineNumber: 435,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                    children: \"Social Links\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                    lineNumber: 465,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                                            control: form.control,\n                                            name: \"social.linkedin\",\n                                            render: (param)=>{\n                                                let { field } = param;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                            children: \"LinkedIn\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                            lineNumber: 472,\n                                                            columnNumber: 21\n                                                        }, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                type: \"url\",\n                                                                ...field,\n                                                                value: field.value || \"\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                lineNumber: 474,\n                                                                columnNumber: 23\n                                                            }, void 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                            lineNumber: 473,\n                                                            columnNumber: 21\n                                                        }, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                            lineNumber: 476,\n                                                            columnNumber: 21\n                                                        }, void 0)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                    lineNumber: 471,\n                                                    columnNumber: 19\n                                                }, void 0);\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                            lineNumber: 467,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                                            control: form.control,\n                                            name: \"social.github\",\n                                            render: (param)=>{\n                                                let { field } = param;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                            children: \"GitHub\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                            lineNumber: 485,\n                                                            columnNumber: 21\n                                                        }, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                type: \"url\",\n                                                                ...field,\n                                                                value: field.value || \"\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                lineNumber: 487,\n                                                                columnNumber: 23\n                                                            }, void 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                            lineNumber: 486,\n                                                            columnNumber: 21\n                                                        }, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                            lineNumber: 489,\n                                                            columnNumber: 21\n                                                        }, void 0)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                    lineNumber: 484,\n                                                    columnNumber: 19\n                                                }, void 0);\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                            lineNumber: 480,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                                            control: form.control,\n                                            name: \"social.twitter\",\n                                            render: (param)=>{\n                                                let { field } = param;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                            children: \"Twitter\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                            lineNumber: 498,\n                                                            columnNumber: 21\n                                                        }, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                type: \"url\",\n                                                                ...field,\n                                                                value: field.value || \"\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                                lineNumber: 500,\n                                                                columnNumber: 23\n                                                            }, void 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                            lineNumber: 499,\n                                                            columnNumber: 21\n                                                        }, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                            lineNumber: 502,\n                                                            columnNumber: 21\n                                                        }, void 0)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                                    lineNumber: 497,\n                                                    columnNumber: 19\n                                                }, void 0);\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                            lineNumber: 493,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                                    lineNumber: 466,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                            lineNumber: 464,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                    lineNumber: 184,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-end\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        type: \"button\",\n                        className: \"bg-green-600 hover:bg-green-700\",\n                        onClick: ()=>{\n                            console.log(\"=== BUTTON CLICKED ===\");\n                            console.log(\"Form errors:\", form.formState.errors);\n                            console.log(\"Form is valid:\", form.formState.isValid);\n                            const formValues = form.getValues();\n                            console.log(\"Form values:\", formValues);\n                            // Call the submit handler\n                            handleFormSubmit();\n                        },\n                        children: \"\\uD83D\\uDE80 Create Portfolio\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                        lineNumber: 511,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n                    lineNumber: 510,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n            lineNumber: 183,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\portfolio saas - Copy\\\\frontend\\\\src\\\\components\\\\onboarding\\\\content-form.tsx\",\n        lineNumber: 182,\n        columnNumber: 5\n    }, this);\n}\n_s(ContentForm, \"ThUbwnkfEpBYs65QPtuxgo/jn5c=\", false, function() {\n    return [\n        react_hook_form__WEBPACK_IMPORTED_MODULE_10__.useForm,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_10__.useFieldArray,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_10__.useFieldArray\n    ];\n});\n_c = ContentForm;\nvar _c;\n$RefreshReg$(_c, \"ContentForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/onboarding/content-form.tsx\n"));

/***/ })

});